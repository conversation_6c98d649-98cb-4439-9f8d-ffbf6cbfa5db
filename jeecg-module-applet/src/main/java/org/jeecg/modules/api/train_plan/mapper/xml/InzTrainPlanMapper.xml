<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.train_plan.mapper.InzTrainPlanMapper">

    <select id="getExistingWordIdsByPlanId" resultType="java.lang.String">
        SELECT word_id
        FROM inz_train_plan_info
        WHERE plan_id = #{id} AND word_id IN
        <foreach collection="wordIds" item="wordId" open="(" separator="," close=")">
            #{wordId}
        </foreach>
    </select>
</mapper>