package org.jeecg.modules.api.user_front.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * @Description: inz_user_front
 * @Author: jeecg-boot
 * @Date:   2025-01-13
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="用户修改密码", description="用户修改密码")
public class UserUpdatePassword implements Serializable {

	/**手机号*/
    @NotEmpty(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    @ApiModelProperty(value = "手机号", required = true)
    private String phone;
    /**手机号*/
    @NotEmpty(message = "手机号验证码不能为空")
    @ApiModelProperty(value = "手机号验证码", required = true)
    private String code;
    /**密码*/
    @NotEmpty(message = "密码不能为空")
    @ApiModelProperty(value = "密码", required = true)
    private String password;
    /**重复密码**/
    @NotEmpty(message = "重复密码不能为空")
    @ApiModelProperty(value = "重复密码", required = true)
    private String repeatPassword;


}
