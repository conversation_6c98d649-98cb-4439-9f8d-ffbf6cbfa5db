package org.jeecg.modules.api.words.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.words.entity.PhoneticSymbols;
import org.jeecg.modules.api.words.mapper.PhoneticSymbolsMapper;
import org.jeecg.modules.api.words.service.PhoneticSymbolsService;
import org.springframework.stereotype.Service;

/**
 * @Description: 音标管理
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Service
public class PhoneticSymbolsServiceImpl extends ServiceImpl<PhoneticSymbolsMapper, PhoneticSymbols> implements PhoneticSymbolsService {

}
