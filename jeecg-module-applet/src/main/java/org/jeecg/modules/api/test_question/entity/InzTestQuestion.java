package org.jeecg.modules.api.test_question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Data
@TableName("inz_test_question")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_test_question对象", description="精选试题")
public class InzTestQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**所属图书*/
	@Excel(name = "所属图书", width = 15)
    @ApiModelProperty(value = "所属图书")
    private String bookId;
	/**所属单元*/
	@Excel(name = "所属单元", width = 15)
    @ApiModelProperty(value = "所属单元")
    private String unitId;
    @Excel(name = "试题数量", width = 15)
    @ApiModelProperty(value = "试题数量")
    private String questionCount;
	/**试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5问答题 6应用题*/
	@Excel(name = "试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5问答题 6应用题", width = 15)
    @ApiModelProperty(value = "试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5问答题 6应用题")
    private String questionType;
	/**参考答案1需要 0不需要*/
	@Excel(name = "参考答案1需要 0不需要", width = 15)
    @ApiModelProperty(value = "参考答案1需要 0不需要")
    private Integer isNeedAnswer;
	/**其他需求*/
	@Excel(name = "其他需求", width = 15)
    @ApiModelProperty(value = "其他需求")
    private String otherNeeds;
	/**完成时间*/
	@Excel(name = "完成时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "完成时间")
    private Date finishTime;

    @TableField(exist = false)
    @ApiModelProperty(value = "问题列表")
    private List<InzTestQuestionInfo> questionInfoList;
}
