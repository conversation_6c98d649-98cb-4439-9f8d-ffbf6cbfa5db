package org.jeecg.modules.api.user_front.controller;

import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.constant.CommonConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.base.entity.DeviceInfo;
import org.jeecg.common.system.util.JwtUtil;
import org.jeecg.common.util.*;
import org.jeecg.modules.api.books.entity.WordBooks;
import org.jeecg.modules.api.books.service.WordBooksService;
import org.jeecg.modules.api.poster_template.entity.PosterTemplate;
import org.jeecg.modules.api.poster_template.service.PosterTemplateService;
import org.jeecg.modules.api.user_books.entity.InzUserBooks;
import org.jeecg.modules.api.user_books.service.UserBooksService;
import org.jeecg.modules.api.user_front.entity.*;
import org.jeecg.modules.api.user_front.service.UserDeviceService;
import org.jeecg.modules.api.user_front.service.UserFrontService;
import org.jeecg.modules.api.user_learn_log.entity.InzUserLearnLog;
import org.jeecg.modules.api.user_learn_log.service.IInzUserLearnLogService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * @Description: inz_user_front
 * @Author: jeecg-boot
 * @Date: 2025-01-13
 * @Version: V1.0
 */
@Api(tags = "H5 - 前台用户")
@RestController
@RequestMapping("/user")
@Slf4j
public class UserFrontController extends JeecgController<UserFront, UserFrontService> {
    @Autowired
    private UserFrontService userFrontService;

    @Autowired
    private UserBooksService inzUserBooksService;

    @Autowired
    private WordBooksService wordBooksService;

    @Autowired
    private IInzUserLearnLogService inzUserLearnLogService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserDeviceService userDeviceService;

    @Autowired
    private PosterTemplateService posterTemplateService;

    @Value(value = "${jeecg.path.domain}")
    private String domain;

    /**
     * 用户注册
     *
     * @param inzUserFront
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "用户注册")
    @ApiOperation(value = "用户注册", notes = "用户注册")
    @PostMapping(value = "/userRegister")
    public Result<String> userRegister(@RequestBody UserRegister inzUserFront) {
        UserFront front = userFrontService.getOne(new QueryWrapper<>(UserFront.class).lambda().eq(UserFront::getPhone, inzUserFront.getPhone()));
        if (front != null) {
            return Result.error("当前手机号已被注册，请勿重复注册");
        }
        if (!inzUserFront.getPassword().equals(inzUserFront.getRepeatPassword())) {
            return Result.error("两次密码输入不一致，请检查后输入");
        }
        if (redisUtil.get("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code") == null) {
            return Result.error("未找到验证码，请先发送验证码");
        }

        if (!inzUserFront.getCode().equals(redisUtil.get("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code"))) {
            return Result.error("验证码错误，请验证后重新输入");
        }
        UserFront userFront = new UserFront();

        if (StringUtils.isNotBlank(inzUserFront.getInviteCode())) {
            UserFront inviteUser = userFrontService.getOne(new QueryWrapper<UserFront>().lambda()
                    .eq(UserFront::getInviteCode, inzUserFront.getInviteCode()));
            if (inviteUser != null) {
                userFront.setParentId(inviteUser.getId());
            } else {
                return Result.error("邀请码错误，请检查后重新输入");
            }
        }

        BeanUtils.copyProperties(inzUserFront, userFront);
        String salt = oConvertUtils.randomGen(8);
        userFront.setSalt(salt);
        userFront.setInviteCode(RandomUtil.randomString(8));
        String passwordEncode = PasswordUtil.encrypt(userFront.getPhone(), userFront.getPassword(), salt);
        userFront.setPassword(passwordEncode);
        userFront.setRole("normal");
        userFrontService.register(userFront);

        //将缓存删除
        redisUtil.del("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code", "");

        return Result.OK("注册成功！");
    }

    /**
     * 编辑
     *
     * @param inzUserFront
     * @return
     */
    @AutoLog(value = "用户账号登录")
    @ApiOperation(value = "用户账号登录", notes = "用户账号登录")
    @RequestMapping(value = "/userLogin", method = {RequestMethod.POST})
    public Result<Object> userLogin(@Valid @RequestBody UserAccountLogin inzUserFront, HttpServletRequest request) {
        // 1. 用户验证逻辑
        UserFront front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, inzUserFront.getPhone()));
        if (front == null) {
            return Result.error(500, "该用户不存在，请注册");
        }

        // 2. 密码验证
        String userpassword = PasswordUtil.encrypt(inzUserFront.getPhone(), inzUserFront.getPassword(), front.getSalt());
        if (!userpassword.equals(front.getPassword())) {
            return Result.error(500, "用户名或密码错误");
        }

        // 3. 账户状态检查
        if (front.getStatus() != 1) {
            return Result.error(500, "当前账号已被禁用,请联系管理员");
        }

        // 4. 获取并解析设备信息
        String userAgent = request.getHeader("User-Agent");
        String clientIP = IpUtils.getIpAddr(request); // 需要IP工具类
        DeviceInfo device = DeviceUtils.parse(userAgent);


        // 6. 设备数量控制逻辑
        List<UserDevice> deviceList = userDeviceService.list(
                new QueryWrapper<UserDevice>()
                        .lambda()
                        .eq(UserDevice::getUserId, front.getId())
                        .eq(UserDevice::getRemark, "用户正常登录")
                        .orderByAsc(UserDevice::getLoginTime) // 按登录时间排序
        );

        JSONObject obj = new JSONObject(new LinkedHashMap<>());
        String token = JwtUtil.sign(front.getPhone() + "/api", front.getPassword());

        // 检查是否已有该设备
        boolean isExistingDevice = deviceList.stream()
                .anyMatch(d -> d.getDeviceName().equals(device.getDeviceModel()));
        LocalDateTime now = LocalDateTime.now();
        long expireSeconds = JwtUtil.EXPIRE_TIME * 2 / 1000L;
        LocalDateTime expireTime = now.plusSeconds(expireSeconds);

        Date expireDate = Date.from(
                expireTime.atZone(ZoneId.systemDefault()).toInstant()
        );
        if (!isExistingDevice) {
            // 新设备：检查数量是否超限
            if (!deviceList.isEmpty()) {
                // 删除最旧的1台设备
                UserDevice oldestDevice = deviceList.get(0);
                userDeviceService.removeById(oldestDevice.getId());
                redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + oldestDevice.getToken());
//                return Result.error(500, "您已登录5台设备，请先退出登录");
            }

            // 添加新设备记录
            UserDevice newDevice = new UserDevice();
            newDevice.setUserId(front.getId());
            newDevice.setDeviceName(device.getDeviceModel());
            newDevice.setSystemName(device.getOs());
            newDevice.setBrowerName(device.getBrowser());
            newDevice.setLoginTime(new Date());
            newDevice.setToken(token);
            newDevice.setRemark("用户正常登录");

            newDevice.setExprie(expireDate);
            userDeviceService.save(newDevice);
        } else {
            // 已有设备：更新登录时间
            UserDevice existingDevice = deviceList.stream()
                    .filter(d -> d.getDeviceName().equals(device.getDeviceModel()))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("未找到匹配设备记录"));
            existingDevice.setLoginTime(new Date());
            existingDevice.setToken(token);
            existingDevice.setExprie(expireDate);
            existingDevice.setRemark("用户正常登录");
            userDeviceService.updateById(existingDevice);
        }
        // 7. 生成Token等后续逻辑
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        obj.put("token", token);
        obj.put("userInfo", front);

        return Result.OK("登录成功!", obj);
    }

    /**
     * 获取用户详情
     *
     * @return
     */
    @AutoLog(value = "获取用户详情")
    @ApiOperation(value = "获取用户详情", notes = "根据 token 获取用户详情")
    @GetMapping(value = "/getUserDetails")
    public Result<UserFront> getUserDetails() {
        String userIdByToken = CommonUtils.getUserIdByToken();
        // 根据手机号查询用户信息
        UserFront front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getId, userIdByToken));
        if (front == null) {
            return Result.error(500, "用户不存在");
        }
        if (StringUtils.isBlank(front.getInviteCode())) {
            front.setInviteCode(RandomUtil.randomString(8));
        }

        // 获取域名
        String domain1 = redisUtil.get("SuperWords:config:DOMAIN").toString();

        // 生成带有注册页面URL和邀请码的二维码内容
        String registerUrl = domain + "/#/pages/login/sign-in/index?name=%E8%B4%A6%E5%8F%B7%E6%B3%A8%E5%86%8C&inviteCode=" + front.getInviteCode();
        String path;
        try {
            // 生成包含完整注册URL的二维码
            path = QrCodeUtil.generateQRCode(registerUrl, 200, 200);
            front.setInviteQrcodePath(path);
            userFrontService.updateById(front);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        // 判断用户是否处于试学期
        if (front.getCreateTime() != null) {
            // 获取试学天数配置
            Object freeDaysObj = redisUtil.get("SuperWords:config:FREE_MEMBERSHIP_DAYS");
            int freeDays = 1; // 默认1天
            if (freeDaysObj != null) {
                try {
                    freeDays = Integer.parseInt(freeDaysObj.toString());
                } catch (NumberFormatException e) {
                    log.error("试学天数配置格式错误: {}", freeDaysObj);
                }
            }

            // 计算试学结束时间
            Date trialEndTime = new Date(front.getCreateTime().getTime() + freeDays * 24L * 60L * 60L * 1000L);
            Date now = new Date();

            // 如果当前时间小于试学结束时间，则设置为试学状态
            if (now.before(trialEndTime)) {
                front.setTryStudy("1");
            } else {
                front.setTryStudy("0");
            }
        } else {
            front.setTryStudy("0");
        }

        front.setInviteQrcodePath(domain1 + "/super-words/sys/common/static/" + front.getInviteQrcodePath());
        return Result.OK("获取用户详情成功!", front);
    }

    /**
     * 编辑
     *
     * @param inzUserFront
     * @return
     */
    @AutoLog(value = "用户手机号登录")
    @ApiOperation(value = "用户手机号登录", notes = "用户手机号登录")
    @RequestMapping(value = "/modelLogin", method = {RequestMethod.POST})
    public Result<Object> userModelLogin(@Valid @RequestBody UserSmsLogin inzUserFront) {

        if (redisUtil.get("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code") == null) {
            return Result.error("未找到验证码，请先发送验证码");
        }
        UserFront front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, inzUserFront.getPhone()));
        if (front == null) {
            UserFront userFront = new UserFront();
            inzUserFront.setPassword("123456");
            BeanUtils.copyProperties(inzUserFront, userFront);
            String salt = oConvertUtils.randomGen(8);
            userFront.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(userFront.getPhone(), userFront.getPassword(), salt);
            userFront.setPassword(passwordEncode);
            userFrontService.register(userFront);

            front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, inzUserFront.getPhone()));
        } else {
            if (1 != front.getStatus()) {
                return Result.error(500, "当前账号已被禁用,请联系管理员");
            }
        }

        JSONObject obj = new JSONObject(new LinkedHashMap<>());
        //1.生成token
        String token = JwtUtil.sign(front.getPhone() + "/api", front.getPassword());
        // 设置token缓存有效时间
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        redisUtil.del("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code");
        obj.put("token", token);
        obj.put("userInfo", front);

        return Result.OK("登录成功!", obj);
    }

    /**
     * 发送短信
     */
    @AutoLog(value = "发送短信")
    @ApiOperation(value = "发送短信", notes = "发送短信")
    @RequestMapping(value = "/sendSms", method = {RequestMethod.POST})
    public Result<String> sendSms(@Valid @RequestBody SmsRequest smsRequest) {
        String thirdToken = ThirdRequestUtils.getThirdToken("infrabiz", "infrabiz", redisUtil);
        long nowTimestamp = Instant.now().getEpochSecond();
        if (redisUtil.get("SuperWords:phone_code:" + smsRequest.getPhone() + ":time") != null) {
            String timestamp = redisUtil.get("SuperWords:phone_code:" + smsRequest.getPhone() + ":time").toString();
            long l = nowTimestamp - Long.parseLong(timestamp);
            if (l < 10) {
                return Result.error("请勿频繁发送", "");
            }
        }
        String code = "";

        code = RandomUtil.randomNumbers(6);
        if ("18961208839".equals(smsRequest.getPhone())) {
            code = "258011";
        } else {
            boolean b = ThirdRequestUtils.sendSms(smsRequest.getPhone(), code, thirdToken, redisUtil);
            if (!b) {
                return Result.error("发送失败");
            }
        }
        redisUtil.set("SuperWords:phone_code:" + smsRequest.getPhone() + ":code", code, 60 * 5);
        redisUtil.set("SuperWords:phone_code:" + smsRequest.getPhone() + ":time", nowTimestamp, 60 * 5);
        return Result.OK("短信发送成功!", "");
    }

    /**
     * 发送短信验证码（使用阿里云短信服务）
     */
    @ApiOperation(value = "发送短信验证码", notes = "发送短信验证码")
    @GetMapping(value = "/sendSmsCode")
    public Result<String> sendSmsCode(@RequestParam String phone) {
        if (StringUtils.isBlank(phone)) {
            return Result.error("手机号不能为空");
        }

        // 频率限制检查
        long nowTimestamp = Instant.now().getEpochSecond();
        if (redisUtil.get("SuperWords:phone_code:" + phone + ":time") != null) {
            String timestamp = redisUtil.get("SuperWords:phone_code:" + phone + ":time").toString();
            long timeDiff = nowTimestamp - Long.parseLong(timestamp);
            if (timeDiff < 60) { // 限制60秒内不能重复发送
                return Result.error("请勿频繁发送短信，请" + (60 - timeDiff) + "秒后重试");
            }
        }

        // 生成6位随机验证码
        String code = RandomUtil.randomNumbers(6);

        // 获取阿里云短信工具类
        AliyunSmsUtil aliyunSmsUtil = SpringContextUtils.getBean(AliyunSmsUtil.class);

        // 发送验证码短信
        boolean success = aliyunSmsUtil.sendVerificationCode(phone, code);

        if (success) {
            // 将验证码存入Redis，有效期5分钟
            redisUtil.set("SuperWords:phone_code:" + phone + ":code", code, 300);
            redisUtil.set("SuperWords:phone_code:" + phone + ":time", nowTimestamp, 300);
            return Result.ok("验证码发送成功");
        } else {
            return Result.error("验证码发送失败，请稍后重试");
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @AutoLog(value = "用户修改密码")
    @ApiOperation(value = "用户修改密码", notes = "用户修改密码")
    @PostMapping(value = "/updatePassword")
    public Result<String> updatePassword(@Valid @RequestBody UserUpdatePassword inzUserFront) {
        // 1. 获取用户信息
        UserFront front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, inzUserFront.getPhone()));
        if (front == null) {
            return Result.error("该用户不存在，请先注册");
        }

        // 3. 校验新密码
        if (inzUserFront.getPassword() == null || inzUserFront.getRepeatPassword().isEmpty()) {
            return Result.error("两次密码输入不一致");
        }
        if (redisUtil.get("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code") == null) {
            return Result.error("未找到验证码，请先发送验证码");
        }
        if (!inzUserFront.getCode().equals(redisUtil.get("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code"))) {
            return Result.error("验证码错误，请验证后重新输入");
        }

        // 4. 设置新密码
        String salt = oConvertUtils.randomGen(8);
        String encryptedNewPassword = PasswordUtil.encrypt(inzUserFront.getPhone(), inzUserFront.getPassword(), salt);
        front.setPassword(encryptedNewPassword);
        front.setSalt(salt);

        // 5. 保存新密码
        userFrontService.updateById(front);
        return Result.OK("密码修改成功！");
    }

    @Data
    public static class UserUpdatePassword {
        private String phone;
        private String code;
        private String password;
        private String repeatPassword;
    }

    @AutoLog(value = "忘记密码")
    @ApiOperation(value = "忘记密码", notes = "忘记密码")
    @PostMapping(value = "/forgetPassword")
    public Result<String> forgetPassword(@Valid @RequestBody UserUpdatePassword inzUserFront) {
        QueryWrapper<UserFront> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(UserFront::getPhone, inzUserFront.getPhone());
        UserFront front = userFrontService.getOne(queryWrapper);
        if (front == null) {
            return Result.error("该用户不存在，请先注册");
        }
        if (!inzUserFront.getPassword().equals(inzUserFront.getRepeatPassword())) {
            return Result.error("两次密码输入不一致");
        }
        String code = redisUtil.get("SuperWords:phone_code:"+ inzUserFront.getPhone() + ":code").toString();
        if(code == null){
            return Result.error("未找到验证码，请先发送验证码");
        }
        if (!inzUserFront.getCode().equals(code)) {
            return Result.error("验证码错误，请验证后重新输入");
        }
        String salt = oConvertUtils.randomGen(8);
        String encryptedNewPassword = PasswordUtil.encrypt(inzUserFront.getPhone(), inzUserFront.getPassword(), salt);
        front.setPassword(encryptedNewPassword);
        front.setSalt(salt);
        userFrontService.updateById(front);
        redisUtil.del("SuperWords:phone_code:" + inzUserFront.getPhone() + ":code");
        redisUtil.del("SuperWords:phone_code:" + inzUserFront.getPhone() + ":time");

        return Result.OK("修改密码成功！");
    }

    @AutoLog(value = "获取用户首页图书")
    @ApiOperation(value = "获取用户首页图书", notes = "获取用户首页图书")
    @RequestMapping(value = "/getIndexBook", method = {RequestMethod.POST})
    public Result<WordBooks> getIndexBook(HttpServletRequest request) {
        String token = request.getHeader("x-access-token");

        String userId = null;
        if (StringUtils.isNotBlank(token)) {
            System.out.println("token:" + token);
            String username = JwtUtil.getUsername(token);

            if (username.endsWith("/api")) {
                username = username.replaceAll("/api$", "");
            }

            UserFront front = service.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, username));

            userId = front.getId();
        }
        Date now = new Date();
        String today = LocalDate.now().format(DateTimeFormatter.ISO_DATE);
        if (StringUtils.isNotBlank(userId)) {
            InzUserBooks one = inzUserBooksService.getOne(new QueryWrapper<InzUserBooks>().lambda()
                    .eq(InzUserBooks::getStatus, 1)
                    .eq(InzUserBooks::getCreateBy, userId));

            if (one != null) {
                //::todo 判断当前时间是否在有效期内
                if (one.getExpirationStartData() != null) {
                    if (today.compareTo(one.getExpirationStartData()) < 0) {
                        return Result.error("您的图书从 " + one.getExpirationStartData() + " 开始可用");
                    }
                    if (today.compareTo(one.getExpirationEndData()) > 0) {
                        return Result.error("您的图书已于 " + one.getExpirationEndData() + " 到期");
                    }
                }
                WordBooks books = wordBooksService.getById(one.getWordBookId());
                if (books != null) {
//                    if(StringUtils.isNotBlank(one.getWordChapterId())){
                    books.setChapterId(one.getWordChapterId());
//                    } else {
//                        List<WordBookChapter> list1 = wordBookChapterService.list(new QueryWrapper<WordBookChapter>().lambda()
//                                .eq(WordBookChapter::getBookId, one.getWordBookId()).orderBy(true, true, WordBookChapter::getCreateBy)
//                                .orderBy(true, true, WordBookChapter::getSort));
//                         books.setChapterId(list1 != null ? list1.get(0).getId() : null);
//                    }
                    books.setWordId(one.getWordId());
                    return processBookData(books, userId);
                }
            } else {
                return Result.error("您的图书已被暂停使用，请联系客服");
            }
        }
        return getDefaultBook();
    }

    /**
     * 处理图书数据（设置学习数量、封面URL等）
     */
    private Result<WordBooks> processBookData(WordBooks books, String userId) {
        // 设置学习单词数
        books.setLearnWordCount((int) inzUserLearnLogService.count(new QueryWrapper<InzUserLearnLog>().lambda()
                .eq(InzUserLearnLog::getBookId, books.getId())
                .eq(InzUserLearnLog::getCreateBy, userId)));

        // 设置完整封面URL
        String baseUrl = redisUtil.get("SuperWords:config:DOMAIN") + "/super-words/sys/common/static/";
        books.setFullThumbUrl(baseUrl + (books.getThumb() != null ? books.getThumb() : ""));
        return Result.OK("获取成功", books);
    }

    /**
     * 获取默认图书（未登录时返回）
     */
    private Result<WordBooks> getDefaultBook() {

        // 2. 获取默认图书
        WordBooks defaultBook = wordBooksService.getOne(new QueryWrapper<WordBooks>().lambda().eq(WordBooks::getIsDefault, 1));
        if (defaultBook == null) {
            defaultBook = wordBooksService.getOne(new QueryWrapper<WordBooks>().lambda().orderBy(true, true, WordBooks::getCreateTime).last("limit 1"));
        }

        // 3. 设置默认封面URL
        String baseUrl = redisUtil.get("SuperWords:config:DOMAIN") + "/super-words/sys/common/static/";
        defaultBook.setFullThumbUrl(baseUrl + (defaultBook.getThumb() != null ? defaultBook.getThumb() : ""));

        // 4. 未登录用户学习数设为0
        defaultBook.setLearnWordCount(0);

        return Result.OK("获取默认图书成功", defaultBook);
    }

    /**
     * 查看个人的登录设备
     */
    @AutoLog(value = "查看个人的登录设备")
    @ApiOperation(value = "查看个人的登录设备", notes = "查看个人的登录设备")
    @GetMapping(value = "/getUserDevice")
    public Result<List<UserDevice>> getUserDevice() {
        List<UserDevice> list = userDeviceService.list(new QueryWrapper<UserDevice>().lambda()
                .eq(UserDevice::getUserId, CommonUtils.getUserIdByToken()));
        return Result.OK(list);
    }

    /**
     * 退出某台设备登录
     *
     * @return
     */
    @AutoLog(value = "退出某台设备登录")
    @Transactional
    @GetMapping(value = "/logoutDevice")
    public Result<String> logoutDevice(@RequestParam(name = "deviceUId", required = true) String deviceUId, HttpServletRequest req) {
        if (userDeviceService.getById(deviceUId) == null) {
            return Result.error("该设备不存在");
        }
        UserDevice device = userDeviceService.getOne(new QueryWrapper<UserDevice>().lambda().eq(UserDevice::getId, deviceUId));
        userDeviceService.remove(new QueryWrapper<UserDevice>().lambda()
                .eq(UserDevice::getUserId, device.getToken())
                .eq(UserDevice::getId, deviceUId));
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + CommonUtils.getUserIdByToken());
        return Result.OK("退出成功");
    }


    @AutoLog(value = "退出登录")
    @ApiOperation(value = "退出登录", notes = "退出登录")
    @GetMapping(value = "/logout")
    @Transactional
    public Result<String> logout() {
        // 1. 获取用户信息
        UserFront front = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, CommonUtils.getUserIdByToken()));
        if (front == null) {
            return Result.error("该用户不存在，请先注册");
        }

        // 2. 删除token
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + front.getPhone());

        return Result.OK("退出成功");
    }

    /**
     * 切换用户
     */
    @AutoLog(value = "切换用户")
    @ApiOperation(value = "切换用户", notes = "切换用户")
    @PostMapping(value = "/switchUser")
    public Result<Object> switchUser(@RequestBody UserSmsLogin loginDTO, HttpServletRequest request) {
        String userId = CommonUtils.getUserIdByToken();
        if (userId == null) {
            return Result.error("请先登录");
        }
        UserFront coachInfo = userFrontService.getById(userId);
        if (coachInfo == null) {
            return Result.error("该用户不存在，请先注册");
        }
        if (!"coach".equals(coachInfo.getRole())) {
            return Result.error("只有教练身份可以切换用户");
        }
        //目前用户
        UserFront userFront = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getPhone, loginDTO.getPhone()));
        if (userFront == null) {
            return Result.error(500, "该用户不存在，请注册");
        }

        //密码验证
        String userpassword = PasswordUtil.encrypt(loginDTO.getPhone(), loginDTO.getPassword(), userFront.getSalt());
        if (!userpassword.equals(userFront.getPassword())) {
            return Result.error(500, "用户名或密码错误");
        }

        //账户状态检查
        if (userFront.getStatus() != 1) {
            return Result.error(500, "当前账号已被禁用,请联系管理员");
        }
        if (!"vip".equals(userFront.getRole())) {
            return Result.error("只能切换到VIP用户");
        }

        String userAgent = request.getHeader("User-Agent");
        String clientIP = IpUtils.getIpAddr(request); // 需要IP工具类
        DeviceInfo device = DeviceUtils.parse(userAgent);


        // 6. 设备数量控制逻辑
        List<UserDevice> deviceList = userDeviceService.list(
                new QueryWrapper<UserDevice>()
                        .lambda()
                        .eq(UserDevice::getUserId, coachInfo.getId())
                        .eq(UserDevice::getRemark, "教练协同登录")
                        .orderByAsc(UserDevice::getLoginTime) // 按登录时间排序
        );

        JSONObject obj = new JSONObject(new LinkedHashMap<>());
        String token = JwtUtil.sign(userFront.getPhone() + "/api", userFront.getPassword());

        // 检查是否已有该设备
        boolean isExistingDevice = deviceList.stream()
                .anyMatch(d -> d.getDeviceName().equals(device.getDeviceModel()));
        LocalDateTime now = LocalDateTime.now();
        long expireSeconds = JwtUtil.EXPIRE_TIME * 2 / 1000L;
        LocalDateTime expireTime = now.plusSeconds(expireSeconds);

        Date expireDate = Date.from(
                expireTime.atZone(ZoneId.systemDefault()).toInstant()
        );
        if (!isExistingDevice) {
            // 添加新设备记录
            UserDevice newDevice = new UserDevice();
            newDevice.setUserId(coachInfo.getId());
            newDevice.setDeviceName(device.getDeviceModel());
            newDevice.setSystemName(device.getOs());
            newDevice.setBrowerName(device.getBrowser());
            newDevice.setLoginTime(new Date());
            newDevice.setToken(token);
            newDevice.setRemark("教练协同登录");
            newDevice.setWithUserId(userFront.getId());
            newDevice.setExprie(expireDate);
            userDeviceService.save(newDevice);
        } else {
            // 已有设备：更新登录时间
            UserDevice existingDevice = deviceList.stream()
                    .filter(d -> d.getDeviceName().equals(device.getDeviceModel()))
                    .findFirst()
                    .orElseThrow(() -> new RuntimeException("未找到匹配设备记录"));
            existingDevice.setLoginTime(new Date());
            existingDevice.setToken(token);
            existingDevice.setExprie(expireDate);
            existingDevice.setRemark("教练协同登录");
            existingDevice.setWithUserId(userFront.getId());
            userDeviceService.updateById(existingDevice);
        }
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token);
        redisUtil.expire(CommonConstant.PREFIX_USER_TOKEN + token, JwtUtil.EXPIRE_TIME * 2 / 1000);
        obj.put("token", token);
        obj.put("userInfo", userFront);

        return Result.OK("切换成功!", obj);
    }

    /**
     * 获取海报模板
     */
    @AutoLog(value = "获取海报模板")
    @ApiOperation(value = "获取海报模板", notes = "获取海报模板")
    @GetMapping(value = "/getPosterTemplate")
    public Result<List<PosterTemplate>> getPosterTemplate() {
        List<PosterTemplate> posterTemplateList = posterTemplateService.list(new QueryWrapper<PosterTemplate>().lambda()
                .eq(PosterTemplate::getStatus, 1)
                .orderByDesc(PosterTemplate::getCreateTime));
        posterTemplateList.forEach(item -> {
            item.setBackgroundImage(redisUtil.get("SuperWords:config:DOMAIN") + "/super-words/sys/common/static/" + item.getBackgroundImage());
        });
        return Result.OK(posterTemplateList);
    }

    @AutoLog(value = "根据邀请码获取用户信息")
    @ApiOperation(value = "根据邀请码获取用户信息", notes = "根据邀请码获取用户信息")
    @GetMapping(value = "/findByInviteCode")
    public Result<UserFront> findByInviteCode(@RequestParam(name = "inviteCode") String inviteCode) {
        UserFront userFront = userFrontService.getOne(new QueryWrapper<UserFront>().lambda().eq(UserFront::getInviteCode, inviteCode));
        if (userFront == null) {
            return Result.error("用户不存在");
        }
        return Result.OK(userFront);
    }
}