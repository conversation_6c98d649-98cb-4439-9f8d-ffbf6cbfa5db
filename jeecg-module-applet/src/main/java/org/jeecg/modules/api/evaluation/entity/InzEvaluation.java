package org.jeecg.modules.api.evaluation.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.api.books.entity.WordBooks;
import org.jeecg.modules.api.evaluation_info.entity.InzEvaluationInfo;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: 测评
 * @Author: jeecg-boot
 * @Date:   2025-01-24
 * @Version: V1.0
 */
@Data
@TableName("inz_evaluation")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_evaluation对象", description="测评")
public class InzEvaluation implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**所属图书*/
	@Excel(name = "所属图书", width = 15)
    @ApiModelProperty(value = "所属图书")
    private String bookId;
	/**测评时间*/
	@Excel(name = "测评时间", width = 20, format = "yyyy-MM-dd HH:mm:ss")
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "测评时间")
    private Date evaluationTime;
	/**正确率*/
	@Excel(name = "正确率", width = 15)
    @ApiModelProperty(value = "正确率")
    private Integer turePercent;
	/**总用时*/
	@Excel(name = "总用时", width = 15)
    @ApiModelProperty(value = "总用时")
    private Double useTime;
	/**得分*/
	@Excel(name = "得分", width = 15)
    @ApiModelProperty(value = "得分")
    private Integer score;

    @TableField(exist = false)
    @ApiModelProperty(value = "测评详情")
    private List<InzEvaluationInfo> infoList;

    @TableField(exist = false)
    @ApiModelProperty(value = "图书信息")
    private WordBooks wordBooks;
}
