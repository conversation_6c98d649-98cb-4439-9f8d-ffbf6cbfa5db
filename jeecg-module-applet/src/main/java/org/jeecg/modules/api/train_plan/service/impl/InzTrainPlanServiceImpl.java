package org.jeecg.modules.api.train_plan.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.api.train_plan.entity.InzTrainPlan;
import org.jeecg.modules.api.train_plan.mapper.InzTrainPlanMapper;
import org.jeecg.modules.api.train_plan.service.IInzTrainPlanService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 训练计划表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Service
public class InzTrainPlanServiceImpl extends ServiceImpl<InzTrainPlanMapper, InzTrainPlan> implements IInzTrainPlanService {

    @Override
    public List<String> getExistingWordIdsByPlanId(String id, List<String> wordIds) {
        return baseMapper.getExistingWordIdsByPlanId(id, wordIds);
    }
}
