package org.jeecg.modules.api.book_words.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.book_words.entity.WordArticle;

import java.util.List;

/**
 * @Description: 单词短文表
 * @Author: jeecg-boot
 * @Date: 2023-07-22
 * @Version: V1.0
 */
public interface WordArticleMapper extends BaseMapper<WordArticle> {

    /**
     * 从指定章节中随机获取各组的一篇短文
     *
     * @param bookId 词书ID (可为null)
     * @param chapterId 章节ID
     * @return 短文列表，每组一篇
     */
    @Select({
        "<script>",
        "SELECT a.* FROM inz_word_article a",
        "INNER JOIN (",
        "  SELECT group_index, MIN(id) as min_id",
        "  FROM (",
        "    SELECT id, group_index, @rand := RAND() as rand_val",
        "    FROM inz_word_article",
        "    JOIN (SELECT @rand := 0) AS r",
        "    WHERE chapter_id = #{chapterId}",
        "    <if test='bookId != null'>",
        "      AND book_id = #{bookId}",
        "    </if>",
        "    ORDER BY group_index, rand_val",
        "  ) t",
        "  GROUP BY group_index",
        ") b ON a.id = b.min_id",
        "</script>"
    })
    List<WordArticle> getRandomArticlesForChapter(@Param("bookId") String bookId, @Param("chapterId") String chapterId);
    
    /**
     * 从指定章节中随机获取指定数量的短文
     *
     * @param bookId 词书ID (可为null)
     * @param chapterId 章节ID
     * @param limit 限制数量
     * @return 随机短文列表
     */
    @Select({
        "<script>",
        "SELECT * FROM inz_word_article",
        "WHERE chapter_id = #{chapterId}",
        "<if test='bookId != null'>",
        "  AND book_id = #{bookId}",
        "</if>",
        "ORDER BY RAND()",
        "<if test='limit > 0'>",
        "LIMIT #{limit}",
        "</if>",
        "</script>"
    })
    List<WordArticle> getRandomArticlesForChapterWithLimit(@Param("bookId") String bookId, 
                                                         @Param("chapterId") String chapterId, 
                                                         @Param("limit") int limit);

    /**
     * 从指定章节中随机获取包含指定单词的短文
     *
     * @param bookId 词书ID (可为null)
     * @param chapterId 章节ID
     * @param word 单词
     * @return 包含指定单词的短文
     */
    @Select({
        "<script>",
        "SELECT * FROM inz_word_article",
        "WHERE chapter_id = #{chapterId}",
        "<if test='bookId != null'>",
        "  AND book_id = #{bookId}",
        "</if>",
        "AND words LIKE CONCAT('%', #{word}, '%')",
        "ORDER BY RAND()",
        "LIMIT 1",
        "</script>"
    })
    WordArticle getRandomArticleWithWord(@Param("bookId") String bookId, @Param("chapterId") String chapterId, @Param("word") String word);
    
    /**
     * 从指定章节中随机获取包含指定单词列表中任意单词的短文
     *
     * @param bookId 词书ID (可为null)
     * @param chapterId 章节ID
     * @param words 单词列表
     * @return 包含指定单词的短文列表
     */
    @Select({
        "<script>",
        "SELECT * FROM inz_word_article",
        "WHERE chapter_id = #{chapterId}",
        "<if test='bookId != null'>",
        "  AND book_id = #{bookId}",
        "</if>",
        "AND (",
        "<foreach collection='words' item='word' separator=' OR '>",
        "words LIKE CONCAT('%', #{word}, '%')",
        "</foreach>",
        ")",
        "ORDER BY RAND()",
        "LIMIT #{limit}",
        "</script>"
    })
    List<WordArticle> getRandomArticlesWithWords(@Param("bookId") String bookId, 
                                               @Param("chapterId") String chapterId, 
                                               @Param("words") List<String> words,
                                               @Param("limit") int limit);
    
    /**
     * 根据单词ID列表查询包含任意这些单词的短文
     * 这个方法会同时检查word_ids字段中的完整匹配和部分匹配
     *
     * @param wordIds 单词ID列表
     * @param limit 限制返回数量
     * @return 包含指定单词ID的短文列表
     */
    @Select({
        "<script>",
        "SELECT * FROM inz_word_article",
        "WHERE (",
        "<foreach collection='wordIds' item='wordId' separator=' OR '>",
        "FIND_IN_SET(#{wordId}, word_ids) > 0 OR word_ids LIKE CONCAT('%', #{wordId}, '%')",
        "</foreach>",
        ")",
        "ORDER BY create_time DESC",
        "<if test='limit > 0'>",
        "LIMIT #{limit}",
        "</if>",
        "</script>"
    })
    List<WordArticle> getArticlesByWordIds(@Param("wordIds") List<String> wordIds, @Param("limit") int limit);
} 