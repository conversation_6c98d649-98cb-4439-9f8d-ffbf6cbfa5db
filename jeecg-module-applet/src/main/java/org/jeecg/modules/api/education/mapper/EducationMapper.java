package org.jeecg.modules.api.education.mapper;

import org.apache.ibatis.annotations.Param;
import org.jeecg.common.system.vo.SelectTreeModel;
import org.jeecg.modules.api.education.entity.Education;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.util.List;
import java.util.Map;

/**
 * @Description: 教育阶段
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
public interface EducationMapper extends BaseMapper<Education> {

    List<SelectTreeModel> queryListByPid(@Param("pid") String pid, @Param("query") Map<String, String> query);
}
