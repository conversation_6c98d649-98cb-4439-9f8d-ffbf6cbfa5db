package org.jeecg.modules.api.words.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_etymology.entity.WordsEtymology;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 单词表
 * @Author: jeecg-boot
 * @Date:   2025-01-02
 * @Version: V1.0
 */
@Data
@TableName("inz_words")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="words对象", description="单词表")
public class Words implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private java.util.Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private java.util.Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
    /**图书id*/
    @Excel(name = "图书id", width = 15)
    @ApiModelProperty(value = "图书id")
    private String bookId;
    /**章节id*/
    @Excel(name = "章节id", width = 15)
    @ApiModelProperty(value = "章节id")
    private String chapterId;
    /**单词*/
	@Excel(name = "单词", width = 15)
    @ApiModelProperty(value = "单词")
    private String word;
	/**英式发音音标*/
	@Excel(name = "英式发音音标", width = 15)
    @ApiModelProperty(value = "英式发音音标")
    private String ukIpa;
	/**美式发音音标*/
	@Excel(name = "美式发音音标", width = 15)
    @ApiModelProperty(value = "美式发音音标")
    private String usIpa;
	/**状态 1正常 0停用*/
	@Excel(name = "状态 1正常 0停用", width = 15)
    @ApiModelProperty(value = "状态 1正常 0停用")
    private Integer status;

    /**单独音标*/
    @Excel(name = "单独音标", width = 15)
    @ApiModelProperty(value = "单独音标")
    @JsonSerialize
    private String pronunciationGuide;

    /**拆分含义*/
    @Excel(name = "拆分含义", width = 15)
    @ApiModelProperty(value = "拆分含义")
    private String rootParticlesMean;

    /**拆分含义*/
    @Excel(name = "谐音", width = 15)
    @ApiModelProperty(value = "谐音")
    private String homophonic;

    /**拆分含义*/
    @Excel(name = "英式发音音频文件", width = 15)
    @ApiModelProperty(value = "英式发音音频文件")
    private String audioUrl;

    @Excel(name = "美式发音音频文件", width = 15)
    @ApiModelProperty(value = "美式发音音频文件")
    private String usAudioUrl;

    @Excel(name = "自然拼读音频文件", width = 15)
    @ApiModelProperty(value = "自然拼读音频文件")
    private String naturalAudioUrl;

    @Excel(name = "拆分单词音频文件", width = 15)
    @ApiModelProperty(value = "拆分单词音频文件")
    private String breakdownAudioUrl;


    @TableField(exist = false)
    @ApiModelProperty(value = "1生词 2学习中 3掌握")
    private Integer type;

    @TableField(exist = false)
    @ApiModelProperty(value = "集合")
    private List<WordCollocations> wordCollocations;

    @TableField(exist = false)
    @ApiModelProperty(value = "词源等信息")
    private WordsEtymology wordsEtymology;


    // 获取格式化后的音标
    public String[] getFormattedPronunciationGuide() {
        if (pronunciationGuide != null && !pronunciationGuide.isEmpty()) {
            // 清除不需要的字符（[、]、/）
            String cleanedGuide = pronunciationGuide.replaceAll("[\\[\\]]", "").trim();
            // 返回处理后的字符串数组
            return cleanedGuide.split(",\\s*");
        }
        // 如果没有发音指南，返回空数组
        return new String[0];
    }
}
