import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '图书id',
    align:"center",
    dataIndex: 'bookId'
   },
   {
    title: '单词',
    align:"center",
    dataIndex: 'wordId'
   },
   {
    title: '状态 1正常 0停用',
    align:"center",
    dataIndex: 'status'
   },
   {
    title: '章节id',
    align:"center",
    dataIndex: 'chapterId'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '图书id',
    field: 'bookId',
    component: 'Input',
  },
  {
    label: '单词',
    field: 'wordId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入单词!'},
          ];
     },
  },
  {
    label: '状态 1正常 0停用',
    field: 'status',
    component: 'InputNumber',
  },
  {
    label: '章节id',
    field: 'chapterId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入章节id!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  bookId: {title: '图书id',order: 0,view: 'text', type: 'string',},
  wordId: {title: '单词',order: 1,view: 'text', type: 'string',},
  status: {title: '状态 1正常 0停用',order: 2,view: 'number', type: 'number',},
  chapterId: {title: '章节id',order: 3,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}