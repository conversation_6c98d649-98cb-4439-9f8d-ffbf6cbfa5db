package org.jeecg.modules.api.book_words.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotEmpty;
import java.io.Serializable;

/**
 * @Description: inz_book_words
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Data
@TableName("inz_book_words")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_book_words对象", description="inz_book_words")
public class BookTestDto implements Serializable {
    private static final long serialVersionUID = 1L;


	/**图书id*/
    @NotEmpty(message = "所属图书不可为空")
	@Excel(name = "图书id", width = 15)
    @ApiModelProperty(value = "图书id",required = true)
    private String bookId;
}
