package org.jeecg.modules.api.train_plan_info.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.train_plan.entity.InzTrainPlan;
import org.jeecg.modules.api.train_plan.service.IInzTrainPlanService;
import org.jeecg.modules.api.train_plan_info.entity.InzTrainPlanInfo;
import org.jeecg.modules.api.train_plan_info.entity.InzTrainPlanInfoDto;
import org.jeecg.modules.api.train_plan_info.entity.UpdateTrainPlanInfoDto;
import org.jeecg.modules.api.train_plan_info.service.IInzTrainPlanInfoService;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_collections.service.WordCollocationsService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * @Description: 训练计划详情表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Api(tags="H5 - 训练计划详情表")
@RestController
@RequestMapping("/train_plan_info")
@Slf4j
public class InzTrainPlanInfoController extends JeecgController<InzTrainPlanInfo, IInzTrainPlanInfoService> {
	@Autowired
	private IInzTrainPlanInfoService inzTrainPlanInfoService;

	@Autowired
	private IInzTrainPlanService inzTrainPlanService;

	@Autowired
	private BookWordsService bookWordsService;

	@Autowired
	private WordCollocationsService wordCollocationsService;
	/**
	 * 分页列表查询
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "训练计划详情表-分页列表查询")
	@ApiOperation(value="训练计划详情表-列表查询", notes="训练计划详情表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzTrainPlanInfo>> queryPageList(InzTrainPlanInfoDto inzTrainPlanInfoDto,@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
		InzTrainPlanInfo inzTrainPlanInfo = new InzTrainPlanInfo();
		QueryWrapper<InzTrainPlanInfo> queryWrapper = QueryGenerator.initQueryWrapper(inzTrainPlanInfo, req.getParameterMap());
		Page<InzTrainPlanInfo> page = new Page<InzTrainPlanInfo>(pageNo, pageSize);
		IPage<InzTrainPlanInfo> pageList = inzTrainPlanInfoService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	@ApiOperation(value="今日计划 - 单词列表", notes="今日计划 - 单词列表")
	@GetMapping(value = "/listAll")
	public Result<List<BookWords>> queryPageList(InzTrainPlanInfoDto inzTrainPlanInfoDto) {
		try {
			// 1. 数据准备阶段
			InzTrainPlanInfo inzTrainPlanInfo = new InzTrainPlanInfo();
			BeanUtils.copyProperties(inzTrainPlanInfoDto, inzTrainPlanInfo);

			// 获取训练计划及关联书籍
			InzTrainPlan plan = inzTrainPlanService.getById(inzTrainPlanInfoDto.getPlanId());
			if (plan == null || plan.getBookId() == null) {
				return Result.error("训练计划信息不完整");
			}

			// 2. 批量预加载所有必要数据
			// 获取全书所有单词ID
			List<String> allWordIds = bookWordsService.getBaseMapper()
					.selectList(new QueryWrapper<BookWords>()
							.select("word_id")
							.eq("book_id", plan.getBookId()))
					.stream()
					.map(BookWords::getWordId)
					.collect(Collectors.toList());

			// 提前加载所有词性数据（一次查询优化）
			List<WordCollocations> allPartOfSpeech = wordCollocationsService.list(
					new QueryWrapper<WordCollocations>()
							.in("word_id", allWordIds)
							.eq("type", "part_of_speech")
			);

			// 构建快速查询结构
			Map<String, List<WordCollocations>> partOfSpeechMap = allPartOfSpeech.stream()
					.collect(Collectors.groupingBy(WordCollocations::getWordId));

			// 3. 获取当前计划的单词列表
			List<BookWords> currentList = inzTrainPlanInfoService.listWithWords(inzTrainPlanInfo);
			if (CollectionUtils.isEmpty(currentList)) {
				return Result.OK("未查询到有单词数据");
			}

			ThreadLocalRandom random = ThreadLocalRandom.current();
			currentList.forEach(currentWord -> {
				List<String> candidateIds = new ArrayList<>(allWordIds);
				candidateIds.remove(currentWord.getId());
				List<String> selectedIds = selectRandomIds(candidateIds, 3, random);
				List<String> allSelectedIds = new ArrayList<>(selectedIds);
				allSelectedIds.add(currentWord.getId());
				List<String> options = buildOptions(allSelectedIds, partOfSpeechMap);
				String correctAnswer = buildOptionForWord(currentWord.getId(), partOfSpeechMap);
				Collections.shuffle(options, new Random(random.nextLong()));
				currentWord.setTrueAnswer(correctAnswer);
				currentWord.setWordsOptions(options);
			});

			return Result.OK(currentList);
		} catch (Exception e) {
			log.error("获取单词列表失败", e);
			return Result.error("今日单词已经学习完成，休息下吧");
		}
	}


	// 新增辅助方法：构建单个单词的选项描述
	private String buildOptionForWord(String wordId,
									  Map<String, List<WordCollocations>> dataMap) {
		return dataMap.getOrDefault(wordId, Collections.emptyList()).stream()
				.map(coll -> String.format("%s. %s;",
						coll.getFormattedType(),
						Optional.ofNullable(coll.getChinese()).orElse("无中文释义")
				))
				.collect(Collectors.joining("\n"));
	}
	// 辅助方法：安全随机选择
	private List<String> selectRandomIds(List<String> source, int count, Random random) {
		if (source.size() <= count) {
			return new ArrayList<>(source);
		}

		List<String> shuffled = new ArrayList<>(source);
		Collections.shuffle(shuffled, random);
		return shuffled.subList(0, count);
	}

	// 辅助方法：构建选项内容
	private List<String> buildOptions(List<String> wordIds,
									  Map<String, List<WordCollocations>> dataMap) {
		List<String> options = new ArrayList<>(4);

		wordIds.forEach(wordId -> {
			StringBuilder sb = new StringBuilder();
			List<WordCollocations> collocations = dataMap.getOrDefault(wordId, Collections.emptyList());

			collocations.forEach(coll -> {
				String formatted = String.format("%s. %s;",
						coll.getFormattedType(),
						Optional.ofNullable(coll.getChinese()).orElse("")
				);
				sb.append(formatted).append("\n");
			});

			if (sb.length() > 0) {
				options.add(sb.toString().trim());
			} else {
				options.add("暂无词性信息");
			}
		});

		// 确保始终返回4个选项（当前单词+3随机）
		while (options.size() < 4) {
			options.add("无效选项");
		}

		return options;
	}

	/**
	 *  今日计划 - 记录/修改单词类型、学习状态
	 *
	 * @param inzTrainPlanInfo
	 * @return
	 */
	@AutoLog(value = "今日计划 - 记录/修改单词类型、学习状态")
	@ApiOperation(value="今日计划 - 记录/修改单词类型、学习状态", notes="今日计划 - 记录/修改单词类型、学习状态")
	@RequestMapping(value = "/handlePlanInfo", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@Valid @RequestBody UpdateTrainPlanInfoDto inzTrainPlanInfo) {
		InzTrainPlan trainPlan = inzTrainPlanService.getOne(new QueryWrapper<InzTrainPlan>().lambda().eq(InzTrainPlan::getId,inzTrainPlanInfo.getPlanId()));
		InzTrainPlanInfo trainPlanInfo = inzTrainPlanInfoService.getOne(new QueryWrapper<InzTrainPlanInfo>().lambda()
				.eq(InzTrainPlanInfo::getPlanId, inzTrainPlanInfo.getPlanId())
				.eq(InzTrainPlanInfo::getWordId, inzTrainPlanInfo.getWordId())
				.eq(InzTrainPlanInfo::getDay,trainPlan.getDay()));
		trainPlanInfo.setWordId(inzTrainPlanInfo.getWordId());
		if(inzTrainPlanInfo.getStatus() != null){
			if(inzTrainPlanInfo.getStatus() == 2){
				trainPlanInfo.setLearnTime(new Date());
				trainPlanInfo.setIsFinish(1);
				trainPlanInfo.setFinishDay(trainPlan.getDay());
			}

			trainPlanInfo.setStatus(inzTrainPlanInfo.getStatus());
		}
		if(inzTrainPlanInfo.getType() != null){
			trainPlanInfo.setType(inzTrainPlanInfo.getType());
		}

		inzTrainPlanInfoService.updateById(trainPlanInfo);
		long count = inzTrainPlanInfoService.count(new QueryWrapper<InzTrainPlanInfo>().lambda()
				.eq(InzTrainPlanInfo::getPlanId, inzTrainPlanInfo.getPlanId())
				.eq(InzTrainPlanInfo::getDay, trainPlan.getDay())
				.eq(InzTrainPlanInfo::getStatus, 1));
		//判断如果已经没有已学习的那就换到第下一天
		if(count == 0){
			trainPlan.setDay(trainPlan.getDay() + 1);
			//::todo 然后去处理下一天的单词
			inzTrainPlanInfoService.updatePlan(trainPlan);
		}
		return Result.OK("操作成功!");
	}
	//	::todo 增加一个定时任务在零点开放
	//	 锁住的单词
	@Transactional
	@Scheduled(cron = "0 0 0 * * ?")
	@AutoLog(value = "今日计划 - 记录/修改单词类型、学习状态")
	@ApiOperation(value="今日计划 - 记录/修改单词类型、学习状态", notes="今日计划 - 记录/修改单词类型、学习状态")
	@RequestMapping(value = "/handTest", method = {RequestMethod.PUT,RequestMethod.POST})
	public void openLock(){
		// 获取昨天的日期
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, -1);
		Date yesterday = calendar.getTime();
		List<InzTrainPlanInfo> list = inzTrainPlanInfoService.list(new QueryWrapper<InzTrainPlanInfo>().lambda()
				.eq(InzTrainPlanInfo::getIsLock, 0).last(" FOR UPDATE"));

		list.forEach(item -> {
			item.setIsLock(1);  // 设置锁定状态
			inzTrainPlanInfoService.updateById(item);
			// 获取 planId，查找同一批次的所有记录
			String planId = item.getPlanId();
//			List<InzTrainPlanInfo> batchList = inzTrainPlanInfoService.list(new QueryWrapper<InzTrainPlanInfo>().lambda()
//					.eq(InzTrainPlanInfo::getPlanId, planId));
//
//			// 查找同一批次中最晚的学习时间
//			InzTrainPlanInfo lastItem = batchList.stream()
//					.max(Comparator.comparing(InzTrainPlanInfo::getLearnTime))
//					.orElse(null);  // 获取学习时间最晚的记录
//
//			if (lastItem != null && isYesterday(lastItem.getLearnTime(), yesterday)) {
				// 如果最后一个学习时间是昨天，更新 day
				InzTrainPlan trainPlan = inzTrainPlanService.getOne(new QueryWrapper<InzTrainPlan>().lambda()
						.eq(InzTrainPlan::getId, planId).last(" FOR UPDATE"));

				if (trainPlan != null && !trainPlan.getDay().equals(item.getDay())) {
					trainPlan.setDay(item.getDay());
					inzTrainPlanService.updateById(trainPlan);
				}
//			}
		});
	}


	// 判断是否是昨天
	private boolean isYesterday(Date learnTime, Date yesterday) {
		Calendar learnCalendar = Calendar.getInstance();
		learnCalendar.setTime(learnTime);

		Calendar yesterdayCalendar = Calendar.getInstance();
		yesterdayCalendar.setTime(yesterday);

		return learnCalendar.get(Calendar.YEAR) == yesterdayCalendar.get(Calendar.YEAR)
				&& learnCalendar.get(Calendar.MONTH) == yesterdayCalendar.get(Calendar.MONTH)
				&& learnCalendar.get(Calendar.DAY_OF_MONTH) == yesterdayCalendar.get(Calendar.DAY_OF_MONTH);
	}

}
