package org.jeecg.modules.api.book_words.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.system.base.entity.FillInEntity;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.modules.api.book_words.entity.WordSentence;
import org.jeecg.modules.api.book_words.mapper.WordSentenceMapper;
import org.jeecg.modules.api.book_words.service.WordSentenceService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 单词例句表
 * @Author: jeecg-boot
 * @Date: 2023-07-22
 * @Version: V1.0
 */
@Service
public class WordSentenceServiceImpl extends ServiceImpl<WordSentenceMapper, WordSentence> implements WordSentenceService {

    private static final Logger logger = LoggerFactory.getLogger(WordSentenceServiceImpl.class);

    @Autowired
    private RedisUtil redisUtil;

    @Override
    public int generateAndSaveSentences(String wordId, String word, int count) {
        if (StringUtils.isBlank(wordId) || StringUtils.isBlank(word) || count <= 0) {
            return 0;
        }

        try {
            // 调用第三方API生成例句
            String thirdToken = ThirdRequestUtils.getThirdToken("infrabiz", "infrabiz", redisUtil);
            List<String> wordList = Collections.singletonList(word);
            List<FillInEntity> fillInEntities = ThirdRequestUtils.analyzeFillIn(wordList, thirdToken, redisUtil);
            
            if (CollectionUtils.isEmpty(fillInEntities)) {
                logger.error("为单词 {} 生成例句失败，API返回空结果", word);
                return 0;
            }

            // 截取所需数量的例句
            int sentencesToCreate = Math.min(count, fillInEntities.size());
            int savedCount = 0;
            
            for (int i = 0; i < sentencesToCreate; i++) {
                FillInEntity entity = fillInEntities.get(i);
                WordSentence sentence = new WordSentence();
                sentence.setWordId(wordId);
                sentence.setQuestion(entity.getQuestion());
                sentence.setChMeaning(entity.getChMeaning());
                sentence.setTrueAnswer(entity.getTrueAnswer());
                sentence.setOptions(JSON.toJSONString(entity.getOptions()));
                
                if (save(sentence)) {
                    savedCount++;
                }
            }
            
            return savedCount;
        } catch (Exception e) {
            logger.error("为单词 {} 生成例句时出现异常", word, e);
            return 0;
        }
    }

    @Override
    public Map<String, List<FillInEntity>> getSentencesForWords(List<String> wordIds) {
        if (CollectionUtils.isEmpty(wordIds)) {
            return Collections.emptyMap();
        }

        Map<String, List<FillInEntity>> result = new HashMap<>();
        
        LambdaQueryWrapper<WordSentence> query = new LambdaQueryWrapper<>();
        query.in(WordSentence::getWordId, wordIds);
        List<WordSentence> sentences = this.list(query);
        
        // 按单词ID分组
        Map<String, List<WordSentence>> sentencesByWordId = sentences.stream()
                .collect(Collectors.groupingBy(WordSentence::getWordId));
        
        // 转换为需要的格式
        sentencesByWordId.forEach((wordId, wordSentences) -> {
            List<FillInEntity> entities = wordSentences.stream()
                    .map(this::convertToFillInEntity)
                    .collect(Collectors.toList());
            result.put(wordId, entities);
        });
        
        return result;
    }

    @Override
    public List<FillInEntity> getRandomFillInSentences(List<String> wordIds, int limitPerWord) {
        if (CollectionUtils.isEmpty(wordIds)) {
            return Collections.emptyList();
        }

        try {
            // 使用mapper中的自定义查询方法获取随机例句
            List<WordSentence> allSentences = baseMapper.getRandomSentencesByWordIds(wordIds, limitPerWord);
            
            if (CollectionUtils.isEmpty(allSentences)) {
                logger.warn("未找到与单词匹配的例句: wordIds={}", wordIds);
                return Collections.emptyList();
            }
            
            // 按单词ID分组
            Map<String, List<WordSentence>> sentencesByWordId = allSentences.stream()
                    .collect(Collectors.groupingBy(WordSentence::getWordId));
            
            List<FillInEntity> result = new ArrayList<>();
            
            // 为每个单词选择最多limitPerWord个例句
            for (String wordId : wordIds) {
                List<WordSentence> wordSentences = sentencesByWordId.get(wordId);
                
                if (wordSentences != null && !wordSentences.isEmpty()) {
                    // 如果有多个例句，随机选一个
                    int sentencesToTake = Math.min(limitPerWord, wordSentences.size());
                    List<WordSentence> selectedSentences;
                    
                    if (wordSentences.size() > sentencesToTake) {
                        // 随机选择指定数量的例句
                        Collections.shuffle(wordSentences);
                        selectedSentences = wordSentences.subList(0, sentencesToTake);
                    } else {
                        selectedSentences = wordSentences;
                    }
                    
                    // 转换为FillInEntity并添加到结果
                    for (WordSentence sentence : selectedSentences) {
                        FillInEntity entity = convertToFillInEntity(sentence);
                        if (entity != null) {
                            result.add(entity);
                        }
                    }
                }
            }
            
            return result;
        } catch (Exception e) {
            logger.error("获取随机例句失败", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 将WordSentence对象转换为FillInEntity对象
     */
    private FillInEntity convertToFillInEntity(WordSentence sentence) {
        if (sentence == null) {
            return null;
        }
        
        FillInEntity entity = new FillInEntity();
        entity.setQuestion(sentence.getQuestion());
        entity.setChMeaning(sentence.getChMeaning());
        entity.setTrueAnswer(sentence.getTrueAnswer());
        
        try {
            List<String> options = JSON.parseArray(sentence.getOptions(), String.class);
            entity.setOptions(options);
        } catch (Exception e) {
            logger.error("解析例句选项失败: {}", sentence.getOptions(), e);
            // 如果解析失败，设置空列表
            entity.setOptions(Collections.emptyList());
        }
        
        return entity;
    }
} 