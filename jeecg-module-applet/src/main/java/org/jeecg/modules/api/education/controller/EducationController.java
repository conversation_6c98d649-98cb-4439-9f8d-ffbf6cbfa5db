package org.jeecg.modules.api.education.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.modules.api.education.entity.Education;
import org.jeecg.modules.api.education.service.EducationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @Description: 教育阶段
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Api(tags="H5 - 教育阶段")
@RestController
@RequestMapping("/education")
@Slf4j
public class EducationController extends JeecgController<Education, EducationService> {
	@Autowired
	private EducationService inzEducationService;

	 @ApiOperation(value="教育阶段-获取所有数据不带分页", notes="教育阶段-获取所有数据不带分页")
	 @GetMapping(value = "/listNP")
	 public Result<List<Education>> listNP() {
		 List<Education> list = inzEducationService.list(new QueryWrapper<Education>().lambda().eq(Education::getStatus,1).orderByAsc(Education::getSort));

		 if (list.isEmpty()) {
			 return Result.error("未找到对应数据");
		 }

		 List<Education> tree = inzEducationService.buildEducationTreeRecursive(list);
		 return Result.OK(tree);
	 }


}
