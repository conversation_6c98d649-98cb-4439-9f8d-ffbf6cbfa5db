<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.book_words.mapper.BookWordsMapper">
    <resultMap id="BookWordsResultMap" type="org.jeecg.modules.api.book_words.entity.BookWords">
        <!--    <resultMap id="BookWordsResultMap" type="org.jeecg.modules.api.words.entity.Words">-->
        <id property="id" column="word_id"/>
        <result property="bookId" column="book_id"/>
        <result property="wordId" column="word_id"/>
        <result property="status" column="word_status"/>
        <result property="chapterId" column="chapter_id"/>

        <result property="word" column="word_word"/>
        <result property="ukIpa" column="uk_ipa"/>
        <result property="usIpa" column="us_ipa"/>
        <result property="status" column="word_status"/>
        <result property="pronunciationGuide" column="pronunciation_guide"/>
        <result property="rootParticlesMean" column="root_particles_mean"/>
        <result property="type" column="type"/>
        <result property="audioUrl" column="word_audio_url"/>
        <result property="usAudioUrl" column="us_audio_url"/>
        <result property="naturalAudioUrl" column="natural_audio_url"/>
        <result property="breakdownAudioUrl" column="breakdown_audio_url"/>

        <collection property="wordCollocations" ofType="org.jeecg.modules.api.word_collections.entity.WordCollocations">
            <result property="id" column="collocations_id"/>
            <result property="createBy" column="collocations_create_by"/>
            <result property="createTime" column="collocations_create_time"/>
            <result property="updateBy" column="collocations_update_by"/>
            <result property="updateTime" column="collocations_update_time"/>
            <result property="sysOrgCode" column="collocations_sys_org_code"/>
            <result property="wordId" column="collocations_word_id"/>
            <result property="english" column="collocations_english"/>
            <result property="chinese" column="collocations_chinese"/>
            <result property="type" column="collocations_type"/>
            <result property="audioUrl" column="collocations_audio_url"/>
            <result property="sort" column="collocations_sort"/>
        </collection>
    </resultMap>


    <select id="listWithWords" resultMap="BookWordsResultMap">
        SELECT
        wc.id AS collocations_id,
        wc.create_by AS collocations_create_by,
        wc.create_time AS collocations_create_time,
        wc.update_by AS collocations_update_by,
        wc.update_time AS collocations_update_time,
        wc.sys_org_code AS collocations_sys_org_code,
        wc.word_id AS collocations_word_id,
        wc.english AS collocations_english,
        wc.chinese AS collocations_chinese,
        wc.type AS collocations_type,
        wc.audio_url as collocations_audio_url,
        wc.sort AS collocations_sort,
        w.id as word_id,
        w.word AS word_word,
        w.uk_ipa,
        w.us_ipa,
        w.status AS word_status,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.audio_url as word_audio_url,
        bw.book_id AS book_id,
        bw.chapter_id AS chapter_id,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        COALESCE(ull.status,0) AS type
        FROM inz_book_words bw
        INNER JOIN inz_word_collocations wc ON bw.word_id = wc.word_id
        INNER JOIN inz_words w ON bw.word_id = w.id
        <if test="bookWords.bookId != null and bookWords.bookId != ''">
            LEFT JOIN inz_word_book_chapter bc ON bw.chapter_id = bc.id
        </if>
        LEFT JOIN inz_user_learn_log ull ON w.id = ull.word_id
        <if test="bookWords.createBy != null and bookWords.createBy != ''">
            and ull.create_by = #{bookWords.createBy}
        </if>
        <where>
            wc.type in ("part_of_speech","root_particles","speak_naturl_phonics","root_breakdown")
            <if test="bookWords.bookId != null and bookWords.bookId != ''">
               and bw.book_id = #{bookWords.bookId}
            </if>
            <if test="bookWords.chapterId != null and bookWords.chapterId != ''">
                and bw.chapter_id = #{bookWords.chapterId}
            </if>
            <if test="bookWords.wordIds != null and bookWords.wordIds.size() > 0">
                AND bw.word_id IN
                <foreach item="item" index="index" collection="bookWords.wordIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <choose>
                <when test="bookWords.type == 1">
                    AND ull.status = 1
                </when>
                <when test="bookWords.type == 2">
                    AND ull.status = 2
                </when>
                <when test="bookWords.type == 3">
                    AND ull.status = 3
                </when>
            </choose>

        </where>
        ORDER BY
        <choose>
            <when test="bookWords.sortBy != null and bookWords.sortBy == 'rand'">
                RAND()
            </when>
            <when test="bookWords.sortBy != null and bookWords.sortBy == 'asc'">
                LEFT(w.word, 1)
            </when>
        </choose>
        ,wc.sort;
    </select>
    <select id="getInfoById" resultMap="BookWordsResultMap">
        SELECT
        wc.id AS collocations_id,
        wc.create_by AS collocations_create_by,
        wc.create_time AS collocations_create_time,
        wc.update_by AS collocations_update_by,
        wc.update_time AS collocations_update_time,
        wc.sys_org_code AS collocations_sys_org_code,
        wc.word_id AS collocations_word_id,
        wc.english AS collocations_english,
        wc.chinese AS collocations_chinese,
        wc.type AS collocations_type,
        wc.sort AS collocations_sort,
        w.id as word_id,
        w.word AS word_word,
        w.uk_ipa,
        w.us_ipa,
        w.status AS word_status,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        bw.book_id AS book_id,
        bw.chapter_id AS chapter_id
        FROM inz_book_words bw
        LEFT JOIN inz_word_collocations wc ON bw.word_id = wc.word_id
        LEFT JOIN inz_words w ON bw.word_id = w.id
        <where>
            bw.id = #{id}
        </where>
        ORDER BY wc.sort;
    </select>
    <select id="listWithWordsLimit" resultMap="BookWordsResultMap">
        SELECT
        wc.id AS collocations_id,
        wc.create_by AS collocations_create_by,
        wc.create_time AS collocations_create_time,
        wc.update_by AS collocations_update_by,
        wc.update_time AS collocations_update_time,
        wc.sys_org_code AS collocations_sys_org_code,
        wc.word_id AS collocations_word_id,
        wc.english AS collocations_english,
        wc.chinese AS collocations_chinese,
        wc.type AS collocations_type,
        wc.audio_url AS collocations_audio_url,
        wc.sort AS collocations_sort,
        w.id AS word_id,
        w.word AS word_word,
        w.uk_ipa,
        w.us_ipa,
        w.STATUS AS word_status,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.audio_url AS word_audio_url,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        bw.book_id AS book_id,
        bw.chapter_id AS chapter_id,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        COALESCE(ull.STATUS, 0) AS type
        FROM
        (
        SELECT
        bw.book_id,
        bw.chapter_id,
        bw.word_id
        FROM
        inz_book_words bw
        <where>
            1=1
            <if test="bookWords.bookId != null and bookWords.bookId != ''">
                and bw.book_id = #{bookWords.bookId}
            </if>
            <if test="bookWords.chapterId != null and bookWords.chapterId != ''">
                and bw.chapter_id = #{bookWords.chapterId}
            </if>
            <if test="bookWords.wordIds != null and bookWords.wordIds.size() > 0">
                AND bw.word_id IN
                <foreach item="item" index="index" collection="bookWords.wordIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="bookWords.wordNIds != null and bookWords.wordNIds.size() > 0">
                AND bw.word_id NOT IN
                <foreach item="item" index="index" collection="bookWords.wordNIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="limit != null and limit != null and limit != 0">
            LIMIT #{limit}
        </if>
        ) AS bw
        INNER JOIN inz_word_collocations wc ON bw.word_id = wc.word_id
        INNER JOIN inz_words w ON bw.word_id = w.id
        <if test="bookWords.bookId != null and bookWords.bookId != ''">
            LEFT JOIN inz_word_book_chapter bc ON bw.chapter_id = bc.id
        </if>
        LEFT JOIN inz_user_learn_log ull ON w.id = ull.word_id
        <if test="bookWords.createBy != null and bookWords.createBy != ''">
            and ull.create_by = #{bookWords.createBy}
        </if>
        WHERE
            1 = 1
        <if test="types != null and types.size() > 0">
            and wc.type IN
            <foreach item="item" index="index" collection="types" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="bookWords.key != null and bookWords.key != ''">
            and w.word like concat(concat('%',#{bookWords.key}),'%')
        </if>
        <choose>
            <when test="bookWords.type == 1">
                AND ull.status = 1
            </when>
            <when test="bookWords.type == 2">
                AND ull.status = 2
            </when>
            <when test="bookWords.type == 3">
                AND ull.status = 3
            </when>
        </choose>
        ORDER BY LOWER(LEFT(w.word, 1)),wc.sort;
    </select>

    <select id="listWithWordsOffset" resultMap="BookWordsResultMap">
        SELECT
        wc.id AS collocations_id,
        wc.create_by AS collocations_create_by,
        wc.create_time AS collocations_create_time,
        wc.update_by AS collocations_update_by,
        wc.update_time AS collocations_update_time,
        wc.sys_org_code AS collocations_sys_org_code,
        wc.word_id AS collocations_word_id,
        wc.english AS collocations_english,
        wc.chinese AS collocations_chinese,
        wc.type AS collocations_type,
        wc.audio_url AS collocations_audio_url,
        wc.sort AS collocations_sort,
        w.id AS word_id,
        w.word AS word_word,
        w.uk_ipa,
        w.us_ipa,
        w.status AS word_status,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.audio_url AS word_audio_url,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        limited_bw.book_id AS book_id,
        limited_bw.chapter_id AS chapter_id,
        COALESCE(ull.status,0) AS type
        FROM
        (
        SELECT
        bw.book_id,bw.chapter_id,bw.word_id
        FROM inz_book_words bw
        WHERE
        1 = 1
        <if test="bookWords.bookId != null and bookWords.bookId != ''">
            and bw.book_id = #{bookWords.bookId}
        </if>
        <if test="bookWords.chapterId != null and bookWords.chapterId != ''">
            and bw.chapter_id = #{bookWords.chapterId}
        </if>
        <if test="bookWords.wordIds != null and bookWords.wordIds.size() > 0">
            AND bw.word_id IN
            <foreach item="item" index="index" collection="bookWords.wordIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by RAND()
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
        ) AS limited_bw
        LEFT JOIN inz_word_collocations wc ON limited_bw.word_id = wc.word_id
        LEFT JOIN inz_words w ON limited_bw.word_id = w.id
        LEFT JOIN inz_user_learn_log ull ON w.id = ull.word_id
        <where>
            wc.type = 'part_of_speech'
            <if test="bookWords.createBy != null and bookWords.createBy != ''">
                and ull.create_by = #{bookWords.createBy}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="bookWords.sortBy != null and bookWords.sortBy == 'rand'">
                RAND(),
            </when>
            <when test="bookWords.sortBy != null and bookWords.sortBy == 'asc'">
                LEFT(w.word, 1),
            </when>
        </choose>
        wc.sort;
    </select>
    <select id="listWithWordsLimitNotIn" resultType="org.jeecg.modules.api.book_words.entity.BookWords">
        SELECT
        bw.book_id,
        bw.chapter_id,
        bw.word_id
        FROM
        inz_book_words bw
        <where>
            <if test="bookWords.bookId != null and bookWords.bookId != ''">
                bw.book_id = #{bookWords.bookId}
            </if>
            <if test="bookWords.chapterId != null and bookWords.chapterId != ''">
                and bw.chapter_id = #{bookWords.chapterId}
            </if>
            <if test="bookWords.wordIds != null and bookWords.wordIds.size() > 0">
                AND bw.word_id NOT IN
                <foreach item="item" index="index" collection="bookWords.wordIds" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        <if test="dailyWordsCount != null and dailyWordsCount != null and dailyWordsCount != 0">
            LIMIT #{dailyWordsCount}
        </if>
    </select>

    <select id="listWithWordsPage" resultMap="BookWordsResultMap">
        SELECT
        wc.id AS collocations_id,
        wc.create_by AS collocations_create_by,
        wc.create_time AS collocations_create_time,
        wc.update_by AS collocations_update_by,
        wc.update_time AS collocations_update_time,
        wc.sys_org_code AS collocations_sys_org_code,
        wc.word_id AS collocations_word_id,
        wc.english AS collocations_english,
        wc.chinese AS collocations_chinese,
        wc.type AS collocations_type,
        wc.audio_url AS collocations_audio_url,
        wc.sort AS collocations_sort,
        w.id AS word_id,
        w.word AS word_word,
        w.uk_ipa,
        w.us_ipa,
        w.status AS word_status,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.audio_url AS word_audio_url,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        limited_bw.book_id AS book_id,
        limited_bw.chapter_id AS chapter_id
        FROM
        (
        SELECT
        bw.book_id,bw.chapter_id,bw.word_id
        FROM inz_book_words bw
        WHERE
        1 = 1
        <if test="bookWords.bookId != null and bookWords.bookId != ''">
            and bw.book_id = #{bookWords.bookId}
        </if>
        <if test="bookWords.chapterId != null and bookWords.chapterId != ''">
            and bw.chapter_id = #{bookWords.chapterId}
        </if>
        <if test="bookWords.wordIds != null and bookWords.wordIds.size() > 0">
            AND bw.word_id IN
            <foreach item="item" index="index" collection="bookWords.wordIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="offset != null and pageSize != null">
            LIMIT #{offset}, #{pageSize}
        </if>
        ) AS limited_bw
        LEFT JOIN inz_word_collocations wc ON limited_bw.word_id = wc.word_id
        LEFT JOIN inz_words w ON limited_bw.word_id = w.id
        ORDER BY LEFT(w.word, 1),wc.sort;
    </select>
</mapper>