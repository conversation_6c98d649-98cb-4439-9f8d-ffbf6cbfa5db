package org.jeecg.modules.api.test_question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.entity.TestQuestionEntity;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.test_question.entity.InzTestQuestion;
import org.jeecg.modules.api.test_question.entity.InzTestQuestionInfo;
import org.jeecg.modules.api.test_question.entity.InzWordQuestion;
import org.jeecg.modules.api.test_question.mapper.InzTestQuestionMapper;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionInfoService;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionService;
import org.jeecg.modules.api.test_question.service.IInzWordQuestionService;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Service
@Slf4j
public class InzTestQuestionServiceImpl extends ServiceImpl<InzTestQuestionMapper, InzTestQuestion> implements IInzTestQuestionService {
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IInzTestQuestionInfoService inzTestQuestionInfoService;

    @Autowired
    private BookWordsService bookWordsService;

    @Autowired
    private WordsFrontService wordsFrontService;

    @Autowired
    private IInzWordQuestionService iInzWordQuestionService;


    @Override
    @Async
    public void genderQuestionOld(InzTestQuestion inzTestQuestion) {
        List<BookWords> list = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzTestQuestion.getBookId()).eq(BookWords::getChapterId, inzTestQuestion.getUnitId()));
        List<String> wordIds = list.stream().map(BookWords::getWordId).collect(Collectors.toList());
        List<Words> wordsList = wordsFrontService.list(new QueryWrapper<Words>().lambda().in(Words::getId, wordIds));
        // 提取词汇名称并拼接
        String wordList = wordsList.stream()
                .map(Words::getWord)
                .collect(Collectors.joining(","));

        // 获取题型
        String questionType = inzTestQuestion.getQuestionType();
        List<String> questionTypes = getQuestionTypes(questionType);

        // 判断是否需要答案
        String isNeedAnswer = inzTestQuestion.getIsNeedAnswer() == 1 ? "需要" : "不需要";

        // 拼接消息
        String otherNeeds = StringUtils.isNotBlank(inzTestQuestion.getOtherNeeds()) ? inzTestQuestion.getOtherNeeds() : "无";
        String sendMessage = String.format("单词：%s。题型：%s。参数答案：%s。题目数量：%s。其他需求：%s",
                wordList,
                String.join(",", questionTypes),
                isNeedAnswer,
                inzTestQuestion.getQuestionCount(),
                otherNeeds);

        String thirdToken = ThirdRequestUtils.getThirdToken("infrabiz", "infrabiz", redisUtil);
        List<TestQuestionEntity> wordEntities = ThirdRequestUtils.analyzeQuestion("",sendMessage,thirdToken,redisUtil);
        ArrayList<InzTestQuestionInfo> inzTestQuestionInfos = new ArrayList<>();
        int sort = 1;
        for (TestQuestionEntity wordEntity : wordEntities) {
            InzTestQuestionInfo inzTestQuestionInfo = new InzTestQuestionInfo();
            BeanUtils.copyProperties(wordEntity,inzTestQuestionInfo);
            inzTestQuestionInfo.setSort(sort);
            inzTestQuestionInfo.setTestQuestionId(inzTestQuestion.getId());
            inzTestQuestionInfo.setOptions(wordEntity.getOptions().toString());
            inzTestQuestionInfo.setOtherContent(wordEntity.getOptions().toString());
            sort++;
            inzTestQuestionInfos.add(inzTestQuestionInfo);
        }
        inzTestQuestionInfoService.saveBatch(inzTestQuestionInfos);
        inzTestQuestion.setFinishTime(new Date());
        update(inzTestQuestion,new QueryWrapper<InzTestQuestion>().lambda().eq(InzTestQuestion::getId, inzTestQuestion.getId()));
    }

    @Override
    public Result<List<InzTestQuestionInfo>> genderQuestion(InzTestQuestion inzTestQuestion) {
        List<String> questionTypes = getQuestionTypes(inzTestQuestion.getQuestionType());
    
        // 查询题库，随机取题（只查询主题目，parent_id为'0'或null）
        List<InzWordQuestion> mainQuestions = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
                .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
                .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId())
                .in(InzWordQuestion::getQuestionType, questionTypes)
                .and(wrapper -> wrapper.eq(InzWordQuestion::getParentId, "0").or().isNull(InzWordQuestion::getParentId))
                .last("ORDER BY RAND() LIMIT " + inzTestQuestion.getQuestionCount()));

        // 按 questionTypes 顺序排序（题型分组）
        mainQuestions.sort(Comparator.comparingInt(o -> questionTypes.indexOf(o.getQuestionType())));

        // 收集所有题目（包括应用题的子题目）
        List<InzWordQuestion> allQuestions = new ArrayList<>();
        for (InzWordQuestion mainQuestion : mainQuestions) {
            // 添加主题目
            allQuestions.add(mainQuestion);

            // 如果是应用题，查询并添加其子题目
            if ("应用题".equals(mainQuestion.getQuestionType())) {
                List<InzWordQuestion> childQuestions = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
                        .eq(InzWordQuestion::getParentId, mainQuestion.getId())
                        .orderByAsc(InzWordQuestion::getSort));
                allQuestions.addAll(childQuestions);
                log.info("应用题 {} 包含 {} 个子题目", mainQuestion.getQuestion(), childQuestions.size());
            }
        }

        // 组装题目内容
        ArrayList<InzTestQuestionInfo> inzTestQuestionInfos = new ArrayList<>();
        int sort = 1;
        for (InzWordQuestion wordEntity : allQuestions) {
            InzTestQuestionInfo inzTestQuestionInfo = new InzTestQuestionInfo();

            // 手动设置需要的字段，避免复制 id
            inzTestQuestionInfo.setQuestion(wordEntity.getQuestion());
            inzTestQuestionInfo.setQuestionType(wordEntity.getQuestionType());
            inzTestQuestionInfo.setTrueAnswer(wordEntity.getTrueAnswer());
            inzTestQuestionInfo.setParentId(wordEntity.getParentId());

            inzTestQuestionInfo.setSort(sort++);
            inzTestQuestionInfo.setTestQuestionId(inzTestQuestion.getId());
            inzTestQuestionInfo.setOptions(wordEntity.getOptions());
            inzTestQuestionInfo.setOtherContent(wordEntity.getOptions());
            inzTestQuestionInfos.add(inzTestQuestionInfo);
        }
        // 批量保存
        inzTestQuestionInfoService.saveBatch(inzTestQuestionInfos);
        // 更新完成状态
        inzTestQuestion.setFinishTime(new Date());
        update(inzTestQuestion, new QueryWrapper<InzTestQuestion>().lambda().eq(InzTestQuestion::getId, inzTestQuestion.getId()));

        log.info("精选试题生成完成，共生成 {} 道题目（包含应用题子题目）", inzTestQuestionInfos.size());
        return Result.OK(inzTestQuestionInfos);
    }

    // 提取题型处理逻辑到一个单独的方法
    private List<String> getQuestionTypes(String questionType) {
        List<String> questionTypes = new ArrayList<>();

        // 定义题型映射表 (类型编号 -> 题型名称列表)
        Map<String, List<String>> typeMap = new HashMap<>();
        typeMap.put("1", Collections.singletonList("单选题"));
        typeMap.put("2", Collections.singletonList("多选题"));
        typeMap.put("3", Collections.singletonList("判断题"));
        typeMap.put("4", Collections.singletonList("填空题"));
        typeMap.put("5", Collections.singletonList("问答题"));
        typeMap.put("6", Collections.singletonList("应用题"));

        if ("0".equals(questionType)) {
            // 如果是0，返回所有题型
            typeMap.values().forEach(questionTypes::addAll);
        } else {
            // 分割逗号分隔的题型编号（例如 "1,2,3"）
            String[] typeCodes = questionType.split(",");
            for (String code : typeCodes) {
                String trimmedCode = code.trim();
                List<String> types = typeMap.get(trimmedCode);
                if (types != null) {
                    questionTypes.addAll(types);
                }
            }
        }
        return questionTypes;
    }
}
