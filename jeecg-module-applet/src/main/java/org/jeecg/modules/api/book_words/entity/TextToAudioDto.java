package org.jeecg.modules.api.book_words.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

@Data
public class TextToAudioDto {

    @NotEmpty(message = "请输入需要转换的文本")
    @ApiModelProperty(value = "需要转换的文本内容")
    private String content;

    @ApiModelProperty(value = "语言类型，默认自动识别，可选值：en(英文), zh(中文)")
    private String langType;
    
    @ApiModelProperty(value = "是否按句子切分，默认true")
    private Boolean splitBySentence = true;
} 