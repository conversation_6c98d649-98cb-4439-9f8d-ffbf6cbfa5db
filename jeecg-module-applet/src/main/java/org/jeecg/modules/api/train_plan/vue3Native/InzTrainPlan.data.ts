import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '所属图书',
    align: "center",
    dataIndex: 'bookId'
  },
  {
    title: '每日单词数量',
    align: "center",
    dataIndex: 'dailyWordsCount'
  },
  {
    title: '完成天数',
    align: "center",
    dataIndex: 'finishDays'
  },
];

// 高级查询数据
export const superQuerySchema = {
  bookId: {title: '所属图书',order: 0,view: 'text', type: 'string',},
  dailyWordsCount: {title: '每日单词数量',order: 1,view: 'number', type: 'number',},
  finishDays: {title: '完成天数',order: 2,view: 'number', type: 'number',},
};
