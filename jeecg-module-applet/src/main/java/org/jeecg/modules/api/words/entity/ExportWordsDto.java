package org.jeecg.modules.api.words.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;


@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="导出单词Dto", description="导出单词Dto")
public class ExportWordsDto implements Serializable {

    @ApiModelProperty(value = "图书id")
    private String bookId;
    @ApiModelProperty(value = "章节id")
    private String chapterId;
    @ApiModelProperty(value = "单词，用逗号拼接")
    private String ids;
    @ApiModelProperty(value = "类型，all代表所有，word代表仅导出单词，mean代表只导出中文含义")
    private String type;
    @ApiModelProperty(value = "是否需要音标 1需要 0不需要")
    private String useIpa;
    @ApiModelProperty(value = "拆分，1不拆分 2拆分 3自然拼读")
    private String splitType;
    @ApiModelProperty(value = "1串词成文，2抗遗忘复习")
    private String exportType;
    @ApiModelProperty(value = "短文内容")
    private String storyContent;
    @ApiModelProperty(value = "短文中的单词ID列表，用逗号拼接")
    private String storyWordIds;
    @ApiModelProperty(value = "a-纯英文短文，b-中英混合短文（默认）")
    private String contentType;
}
