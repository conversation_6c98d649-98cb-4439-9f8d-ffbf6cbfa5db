{"success": true, "message": "操作成功！", "code": 0, "result": {"records": [{"taskId": "48701", "name": "start", "taskBeginTime": "2019-03-07 09:33:04", "taskEndTime": "2019-03-08 04:03:01", "principal": "测试体验账号", "result": "已完成"}, {"taskId": "48702", "name": "部门领导审批", "taskBeginTime": "2019-03-07 09:33:04", "taskEndTime": "2019-03-08 04:03:01", "principal": "测试体验账号", "result": "已完成"}, {"taskId": "48703", "name": "调整申请", "taskBeginTime": "2019-03-07 09:33:04", "taskEndTime": "2019-03-08 04:03:01", "principal": "测试体验账号", "result": "已完成"}, {"taskId": "48704", "name": "人事审批", "taskBeginTime": "2019-03-07 09:33:04", "taskEndTime": "2019-03-08 04:03:01", "principal": "测试体验账号", "result": "已完成"}, {"taskId": "48705", "name": "end", "taskBeginTime": "2019-03-07 09:33:04", "taskEndTime": "2019-03-08 04:03:01", "principal": "测试体验账号", "result": "已完成"}], "total": 0, "size": 10, "current": 1, "searchCount": true, "pages": 0}, "timestamp": 1551922394641}