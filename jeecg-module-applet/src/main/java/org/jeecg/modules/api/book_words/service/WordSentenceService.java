package org.jeecg.modules.api.book_words.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.system.base.entity.FillInEntity;
import org.jeecg.modules.api.book_words.entity.WordSentence;

import java.util.List;
import java.util.Map;

/**
 * @Description: 单词例句表
 * @Author: jeecg-boot
 * @Date: 2023-07-22
 * @Version: V1.0
 */
public interface WordSentenceService extends IService<WordSentence> {

    /**
     * 为单词生成例句并保存到数据库
     *
     * @param wordId 单词ID
     * @param word 单词内容
     * @param count 需要生成的例句数量，默认为5
     * @return 成功生成的例句数量
     */
    int generateAndSaveSentences(String wordId, String word, int count);

    /**
     * 批量获取单词的例句
     * 
     * @param wordIds 单词ID列表
     * @return 每个单词的例句列表，使用单词ID作为key
     */
    Map<String, List<FillInEntity>> getSentencesForWords(List<String> wordIds);
    
    /**
     * 批量获取单词的例句，转换为FillInEntity对象
     * 
     * @param wordIds 单词ID列表
     * @param limitPerWord 每个单词获取的例句数量限制
     * @return 填空句对象列表
     */
    List<FillInEntity> getRandomFillInSentences(List<String> wordIds, int limitPerWord);
} 