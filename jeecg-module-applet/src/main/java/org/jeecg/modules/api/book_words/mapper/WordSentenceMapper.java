package org.jeecg.modules.api.book_words.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.jeecg.modules.api.book_words.entity.WordSentence;

import java.util.List;

/**
 * @Description: 单词例句表
 * @Author: jeecg-boot
 * @Date: 2023-07-22
 * @Version: V1.0
 */
public interface WordSentenceMapper extends BaseMapper<WordSentence> {

    /**
     * 根据单词ID列表随机获取例句
     *
     * @param wordIds      单词ID列表
     * @param limitPerWord 每个单词最多获取的例句数量
     * @return 例句列表
     */
    @Select({
            "<script>",
            "SELECT ws.* FROM inz_word_sentence ws",
            "JOIN (",
            "  SELECT s.id, (@rn := IF(@current_word = s.word_id, @rn + 1, 1)) AS rn,",
            "  (@current_word := s.word_id) AS current_word",
            "  FROM inz_word_sentence s",
            "  CROSS JOIN (SELECT @rn := 0, @current_word := '') AS vars",
            "  WHERE s.word_id IN",
            "  <foreach collection='wordIds' item='wordId' open='(' separator=',' close=')'>",
            "    #{wordId}",
            "  </foreach>",
            "  ORDER BY s.word_id, RAND()",
            ") AS ranked ON ws.id = ranked.id",
            "WHERE ranked.rn &lt;= #{limitPerWord}",
            "</script>"
    })
    List<WordSentence> getRandomSentencesByWordIds(@Param("wordIds") List<String> wordIds, @Param("limitPerWord") int limitPerWord);
}