package org.jeecg.modules.api.book_words.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.FillInEntity;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * @Description: inz_book_words
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
@Data
@TableName("inz_book_words")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_book_words对象", description="inz_book_words")
public class BookWords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "id")
    private String id;
	/**图书id*/
	@Excel(name = "图书id", width = 15)
    @ApiModelProperty(value = "图书id",required = true)
    @NotNull
    private String bookId;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**单词*/
	@Excel(name = "单词", width = 15)
    @ApiModelProperty(value = "单词")
    private String wordId;
	/**状态 1正常 0停用*/
	@Excel(name = "状态 1正常 0停用", width = 15)
    @ApiModelProperty(value = "状态 1正常 0停用")
    private Integer status;
	/**章节id*/
	@Excel(name = "章节id", width = 15)
    @ApiModelProperty(value = "章节id")
    private String chapterId;

    @TableField(exist = false)
    @ApiModelProperty(value = "排序")
    private String sortBy;

//    @TableField(exist = false)
//    @ApiModelProperty(value = "单词")
//    private Words words;

    @TableField(exist = false)
    @ApiModelProperty(value = "单词额外数据")
    private List<WordCollocations> wordCollocations;

    @TableField(exist = false)
    @ApiModelProperty(value = "1生词 2学习中 3掌握")
    private Integer type;


    /**单词*/
    @TableField(exist = false)
    @ApiModelProperty(value = "单词")
    private String word;
    /**英式发音音标*/
    @TableField(exist = false)
    @ApiModelProperty(value = "英式发音音标")
    private String ukIpa;
    /**美式发音音标*/
    @TableField(exist = false)
    @ApiModelProperty(value = "美式发音音标")
    private String usIpa;

    /**单独音标*/
    @TableField(exist = false)
    @ApiModelProperty(value = "单独音标")
    private String pronunciationGuide;

    /**拆分含义*/
    @TableField(exist = false)
    @ApiModelProperty(value = "拆分含义")
    private String rootParticlesMean;

    @TableField(exist = false)
    @ApiModelProperty(value = "整书测评，选项")
    private List<String> wordsOptions;

    @TableField(exist = false)
    @ApiModelProperty(value = "整书测评，正确答案")
    private String trueAnswer;

    @TableField(exist = false)
    @ApiModelProperty(value = "例句填空")
    private FillInEntity fillInEntity;

    @TableField(exist = false)
    @ApiModelProperty(value = "单词拼接")
    private List<String> wordIds;

    @TableField(exist = false)
    @ApiModelProperty(value = "单词拼接")
    private List<String> wordNIds;

    @TableField(exist = false)
    @ApiModelProperty(value = "计划单词id")
    private String planInfoId;

    @TableField(exist = false)
    @ApiModelProperty(value = "计划单词类型 0重点词 1需会写 2需会认 3标熟")
    private String planInfoStatus;

    @TableField(exist = false)
    @ApiModelProperty(value = "计划学习类型 1新学 2复习")
    private String planInfoLearnType;

    @TableField(exist = false)
    @ApiModelProperty(value = "音频文件")
    private String audioUrl;

    @TableField(exist = false)
    @ApiModelProperty(value = "美式发音音频文件")
    private String usAudioUrl;

    @TableField(exist = false)
    @ApiModelProperty(value = "自然拼读音频文件")
    private String naturalAudioUrl;

    @TableField(exist = false)
    @ApiModelProperty(value = "拆分单词音频文件")
    private String breakdownAudioUrl;

    @TableField(exist = false)
    @ApiModelProperty(value = "key")
    private String key;

    public String[] getFormattedPronunciationGuide() {
        if (pronunciationGuide != null && !pronunciationGuide.isEmpty()) {
            // 清除不需要的字符（[、]、/）
            String cleanedGuide = pronunciationGuide.replaceAll("[\\[\\]]", "").trim();
            // 返回处理后的字符串数组
            return cleanedGuide.split(",\\s*");
        }
        // 如果没有发音指南，返回空数组
        return new String[0];
    }

}
