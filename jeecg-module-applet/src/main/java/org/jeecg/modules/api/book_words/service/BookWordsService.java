package org.jeecg.modules.api.book_words.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.api.book_words.entity.BookWords;

import java.util.List;

/**
 * @Description: inz_book_words
 * @Author: jeecg-boot
 * @Date:   2025-01-17
 * @Version: V1.0
 */
public interface BookWordsService extends IService<BookWords> {

    List<BookWords> listWithWords(BookWords queryWrapper, int limit, List<String> types);

    BookWords getInfoById(String id);

    List<BookWords> listWithWordsLimit(BookWords inzBookWords,int limit,List<String> types);

    List<BookWords> listWithWordsOffset(BookWords inzBookWords, Integer offset, Integer pageSize);

    List<BookWords> listWithWordsLimitNotIn(BookWords bookWords, Integer dailyWordsCount, List<String> types);

    List<BookWords> listWithWordsPage(BookWords inzBookWords, int page, int limit);
}
