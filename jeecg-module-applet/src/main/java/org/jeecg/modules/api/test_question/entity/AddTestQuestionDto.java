package org.jeecg.modules.api.test_question.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_test_question对象", description="精选试题")
public class AddTestQuestionDto implements Serializable {

	/**所属图书*/
    @NotEmpty(message = "图书未选择，请先选择图书")
    @ApiModelProperty(value = "所属图书",required = true)
    private String bookId;
	/**所属单元*/
    @NotEmpty(message = "所属单元不可为空，请先选择单元")
    @ApiModelProperty(value = "所属单元",required = true)
    private String unitId;

    @NotNull(message = "试题数量为必填")
    @Min(value = 1, message = "试题数量最少是1")
    @Max(value = 30,message = "试题数量最多是30")
    @ApiModelProperty(value = "试题数量",required = true)
    private String questionCount;
	/**试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5问答题 6应用题*/
    @NotEmpty(message = "题型不可为空")
    @ApiModelProperty(value = "试题类型选择题型 0不限题型  1单选题 2多选题 3判断题 4填空题 5问答题 6应用题",required = true)
    private String questionType;
	/**参考答案1需要 0不需要*/
    @NotNull(message = "是否需要参考答案不可为空")
    @ApiModelProperty(value = "参考答案1需要 0不需要",required = true)
    private Integer isNeedAnswer;
	/**其他需求*/

    @ApiModelProperty(value = "其他需求")
    private String otherNeeds;

    @ApiModelProperty(value = "用户")
    private String createBy;

    @ApiModelProperty(value = "题目详情")
    private List<QuestionDetail> questionDetail;

    @Data
    public static class QuestionDetail {
        @ApiModelProperty(value = "问题内容", required = true)
        private String question;

        @ApiModelProperty(value = "选项列表（选择题适用）")
        private List<String> options;

        @ApiModelProperty(value = "问题类型", required = true)
        private String question_type;

        @ApiModelProperty(value = "正确答案", required = true)
        private Object true_answer; // 根据题型可能为String或List<String>

        @ApiModelProperty(value = "其他内容")
        private Map<String, Object> other_content;

        @ApiModelProperty(value = "子问题")
        private List<?> children;
    }

}
