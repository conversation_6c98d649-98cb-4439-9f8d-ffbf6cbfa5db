package org.jeecg.modules.api.words.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.lowagie.text.*;
import com.lowagie.text.Font;
import com.lowagie.text.Image;
import com.lowagie.text.pdf.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.query.QueryRuleEnum;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_collections.service.WordCollocationsService;
import org.jeecg.modules.api.words.entity.ExportWordsDto;
import org.jeecg.modules.api.words.entity.PhoneticSymbols;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.PhoneticSymbolsService;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 单词表
 * @Author: jeecg-boot
 * @Date: 2025-01-02
 * @Version: V1.0
 */
@Api(tags = "H5 - 单词表", hidden = true)
@RestController
@RequestMapping("/words")
@Slf4j
public class WordsFrontController extends JeecgController<Words, WordsFrontService> {
    @Autowired
    private WordsFrontService wordsFrontService;

//	@Autowired
//	private IseService iseService;

    @Autowired
    private WordCollocationsService wordCollocationsService;

    @Autowired
    private PhoneticSymbolsService phoneticSymbolsService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 分页列表查询
     *
     * @param words
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "单词表-分页列表查询")
    @ApiOperation(value = "单词表-分页列表查询", notes = "单词表-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<Words>> queryPageList(Words words,
                                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                              HttpServletRequest req) {
        // 自定义查询规则
        QueryWrapper<Words> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(StringUtils.isNotBlank(words.getBookId()), Words::getBookId, words.getBookId());
        queryWrapper.lambda().like(StringUtils.isNotBlank(words.getWord()), Words::getWord, words.getWord()).groupBy(Words::getWord);
        Page<Words> page = new Page<Words>(pageNo, pageSize);
        IPage<Words> pageList = wordsFrontService.page(page, queryWrapper);
        pageList.getRecords().forEach(item -> {
            item.setWordCollocations(wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda().eq(WordCollocations::getWordId, item.getId()).orderBy(true, true, WordCollocations::getSort)));
        });
        return Result.OK(pageList);
    }

    /**
     * 分页列表查询
     *
     * @param req
     * @return
     */
    //@AutoLog(value = "单词表-分页列表查询")
    @ApiOperation(value = "单词表-首页搜索", notes = "单词表-首页搜索")
    @GetMapping(value = "/listNP")
    public Result<List<Words>> queryPageList(@RequestParam(name = "word", required = true) String word, HttpServletRequest req) {
        Words words = new Words();
        words.setWord(word);
        // 自定义查询规则
        Map<String, QueryRuleEnum> customeRuleMap = new HashMap<>();
        // 自定义多选的查询规则为：LIKE_WITH_OR
        customeRuleMap.put("status", QueryRuleEnum.LIKE_WITH_OR);
        QueryWrapper<Words> queryWrapper = QueryGenerator.initQueryWrapper(words, req.getParameterMap(), customeRuleMap);
        queryWrapper.lambda().like(StringUtils.isNotBlank(words.getWord()), Words::getWord, words.getWord()).groupBy(Words::getWord);
        List<Words> pageList = wordsFrontService.listWithType(words);
        return Result.OK(pageList);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "单词表-通过id查询")
    @ApiOperation(value = "单词表-通过id查询", notes = "单词表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Words> queryById(@RequestParam(name = "id", required = true) String id, @RequestParam(name = "bookId", required = true) String bookId, @RequestParam(name = "chapterId", required = true) String chapterId) {
        Words words = wordsFrontService.getOneWithCollection(id);
        words.setBookId(bookId);
        words.setChapterId(chapterId);
        if (words == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(words);
    }

    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    @ApiOperation(value = "导出pdf", notes = "导出pdf")
    @PostMapping(value = "/exportPdf")
    public Result<String> exportPdf(@RequestBody ExportWordsDto wordsDto, HttpServletResponse response) {
        List<Words> wordsList = wordsFrontService.list(new QueryWrapper<Words>().lambda()
                .eq(StringUtils.isNotBlank(wordsDto.getBookId()), Words::getBookId, wordsDto.getBookId())
                .eq(StringUtils.isNotBlank(wordsDto.getChapterId()), Words::getChapterId, wordsDto.getChapterId())
                .in(StringUtils.isNotBlank(wordsDto.getIds()), Words::getId, Arrays.asList(wordsDto.getIds().split(","))));

        if (wordsList == null || wordsList.isEmpty()) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return Result.error("未找到对应数据");
        }

        List<String> wordIds = wordsList.stream().map(Words::getId).collect(Collectors.toList());
        List<Words> wordsNewList = wordsFrontService.getAllWithCollection(wordIds, CommonUtils.getUserIdByToken(), "");

        Document document = new Document(PageSize.A4, 36, 36, 50, 50);
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yy.MM.dd"));
        String suffix = ("review".equals(wordsDto.getExportType()) || wordsDto.getExportType().isEmpty())
                ? "复习"
                : "阅读";
        String fileName = "伴学霸火" + currentDate + suffix + ".pdf";
        log.info("输出文件路径：" + uploadpath + File.separator + fileName);

        try {
            File uploadDir = new File(uploadpath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            String filePath = uploadpath + File.separator + fileName;
            FileOutputStream fos = new FileOutputStream(filePath);

            // 字体加载
            URL fontUrl = getClass().getClassLoader().getResource("font/NotoSansSC-VariableFont_wght.ttf");
            if (fontUrl == null) throw new Exception("字体文件未找到！");
            File tempFontFile = new File("tempFont.ttf");
            if (!tempFontFile.exists()) {
                try (InputStream in = fontUrl.openStream(); FileOutputStream out = new FileOutputStream(tempFontFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) out.write(buffer, 0, bytesRead);
                }
            }

            // 背景图加载
            URL bgUrl = getClass().getClassLoader().getResource("pdfTemplate/bg.jpg");
            if (bgUrl == null) throw new Exception("背景图片未找到！");
            Image bgImage = Image.getInstance(bgUrl);
            bgImage.scaleAbsolute(PageSize.A4.getWidth(), PageSize.A4.getHeight());
            bgImage.setAbsolutePosition(0, 0);

            // 设置 Writer 并添加背景事件处理器
            PdfWriter writer = PdfWriter.getInstance(document, fos);
            writer.setPageEvent(new PdfPageEventHelper() {
                @Override
                public void onEndPage(PdfWriter writer, Document doc) {
                    try {
                        PdfContentByte canvas = writer.getDirectContentUnder();
                        bgImage.setAbsolutePosition(0, 0);
                        canvas.addImage(bgImage);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });

            document.open();

            String titleText = redisUtil.get("SuperWords:config:EXPORT_TITLE").toString();
            BaseFont baseFont = BaseFont.createFont(tempFontFile.getAbsolutePath(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font titleFont = new Font(baseFont, 16, Font.BOLD);
            Paragraph title = new Paragraph(titleText, titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(5f);
            document.add(title);

            PdfPTable table = new PdfPTable(2);
            table.setWidths(new float[]{2, 4});
            Font chineseFont = new Font(baseFont, 12, Font.BOLD);

            PdfPCell headerCell1 = new PdfPCell(new Phrase("单词", chineseFont));
            PdfPCell headerCell2 = new PdfPCell(new Phrase("翻译", chineseFont));

            PdfPCell[] headerCells = new PdfPCell[]{headerCell1, headerCell2};
            for (PdfPCell cell : headerCells) {
                cell.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell.setPaddingTop(10f);
                cell.setPaddingBottom(10f);
                table.addCell(cell);
            }

            for (Words words : wordsNewList) {
                PdfPCell cell1 = new PdfPCell();
                PdfPCell cell2 = new PdfPCell();

                // 第一列内容处理
                if ("all".equals(wordsDto.getType()) || "word".equals(wordsDto.getType())) {
                    Phrase phrase = new Phrase();
                    String splitType = StringUtils.defaultIfEmpty(wordsDto.getSplitType(), "1");

                    if ("1".equals(splitType)) {
                        phrase.add(new Chunk(words.getWord()));
                    } else if ("2".equals(splitType)) {
                        StringBuilder root = new StringBuilder();
                        words.getWordCollocations().stream()
                                .filter(item -> "root_particles".equals(item.getType()))
                                .forEach(item -> {
                                    if (!item.getEnglish().equals(words.getWord())) root.append(item.getEnglish()).append("·");
                                });
                        if (StringUtils.isBlank(root.toString())) root.append(words.getWord()).append("·");
                        root.deleteCharAt(root.length() - 1);
                        phrase.add(new Chunk(root.toString(), chineseFont));
                    } else if ("3".equals(splitType)) {
                        Font blueFont = new Font(baseFont, 12, Font.NORMAL, Color.BLUE);
                        Font redFont = new Font(baseFont, 12, Font.NORMAL, Color.RED);
                        words.getWordCollocations().stream()
                                .filter(item -> "natural_phonics".equals(item.getType()))
                                .forEach(item -> {
                                    boolean isVowel = item.getEnglish().toLowerCase().matches("^[aeiou]$");
                                    phrase.add(new Chunk(item.getEnglish(), isVowel ? blueFont : redFont));
                                });
                    }

                    if ("1".equals(wordsDto.getUseIpa())) {
                        phrase.add(Chunk.NEWLINE);
                        phrase.add(new Chunk(words.getUkIpa()));
                    }

                    cell1.setPhrase(phrase);
                }
                cell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell1.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell1.setPaddingTop(10f);
                cell1.setPaddingBottom(10f);

                // 第二列内容处理
                if ("all".equals(wordsDto.getType()) || "mean".equals(wordsDto.getType())) {
                    Phrase phrase2 = new Phrase();
                    words.getWordCollocations().stream()
                            .filter(item -> "part_of_speech".equals(item.getType()))
                            .forEach(item -> {
                                phrase2.add(new Chunk(item.getFormattedType() + ":" + item.getChinese(), chineseFont));
                                phrase2.add(Chunk.NEWLINE);
                            });
                    cell2.setPhrase(phrase2);
                }
                cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                cell2.setVerticalAlignment(Element.ALIGN_MIDDLE);
                cell2.setPaddingTop(10f);
                cell2.setPaddingBottom(10f);

                table.addCell(cell1);
                table.addCell(cell2);
            }

            document.add(table);
            document.add(new Chunk("\n"));

            String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
            String fileUrl = domain + "/super-words/sys/common/static/" + fileName;
            return Result.OK(fileUrl);
        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } finally {
            document.close();
        }
        return Result.error("导出失败");
    }


    @ApiOperation(value = "导出串词成文短文pdf", notes = "导出串词成文短文pdf，支持纯英文模式和中英混合模式，通过contentType参数控制：a-纯英文短文，b-中英混合短文（默认）")
    @PostMapping(value = "/exportStoryPdf")
    public Result<String> exportStoryPdf(@RequestBody ExportWordsDto wordsDto, HttpServletResponse response) {
        if (StringUtils.isBlank(wordsDto.getStoryContent())) {
            response.setStatus(HttpServletResponse.SC_BAD_REQUEST);
            return Result.error("短文内容不能为空");
        }

        String cleanContent = cleanHtmlTags(wordsDto.getStoryContent());
        String contentType = wordsDto.getContentType();
        boolean isEnglishOnly = "a".equals(contentType);

        List<Words> wordsList = new ArrayList<>();
        if (StringUtils.isNotBlank(wordsDto.getStoryWordIds())) {
            List<String> wordIds = Arrays.asList(wordsDto.getStoryWordIds().split(","));
            wordsList = wordsFrontService.getAllWithCollection(wordIds, CommonUtils.getUserIdByToken(), "");
        }

        Document document = new Document(PageSize.A4);
        String currentDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yy.MM.dd"));
        String suffix = "阅读";
        String fileName = "伴学霸火" + currentDate + suffix + ".pdf";
        log.info("输出文件路径：" + uploadpath + File.separator + fileName);

        try {
            File uploadDir = new File(uploadpath);
            if (!uploadDir.exists()) uploadDir.mkdirs();

            String filePath = uploadpath + File.separator + fileName;
            FileOutputStream fos = new FileOutputStream(filePath);

            // 加载字体
            URL fontUrl = getClass().getClassLoader().getResource("font/NotoSansSC-VariableFont_wght.ttf");
            if (fontUrl == null) throw new Exception("字体文件未找到！");
            File tempFontFile = new File("tempFont.ttf");
            if (!tempFontFile.exists()) {
                try (InputStream in = fontUrl.openStream(); FileOutputStream out = new FileOutputStream(tempFontFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                    System.out.println("字体文件复制成功");
                } catch (IOException e) {
                    throw new Exception("字体文件复制失败: " + e.getMessage());
                }
            }

            response.setContentType("application/pdf");
            String encodedFilename = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFilename);

            // 加载背景图
            URL bgUrl = getClass().getClassLoader().getResource("pdfTemplate/bg.jpg");
            if (bgUrl == null) throw new Exception("背景图片未找到！");
            Image bgImage = Image.getInstance(bgUrl);
            bgImage.scaleAbsolute(PageSize.A4.getWidth(), PageSize.A4.getHeight());
            bgImage.setAbsolutePosition(0, 0);

            // 设置背景图事件
            PdfWriter writer = PdfWriter.getInstance(document, fos);
            writer.setPageEvent(new PdfPageEventHelper() {
                @Override
                public void onEndPage(PdfWriter writer, Document document) {
                    try {
                        PdfContentByte canvas = writer.getDirectContentUnder();
                        canvas.addImage(bgImage);
                    } catch (Exception e) {
                        log.error("添加背景图失败", e);
                    }
                }
            });

            document.open();

            BaseFont baseFont = BaseFont.createFont(tempFontFile.getAbsolutePath(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font titleFont = new Font(baseFont, 16, Font.BOLD);
            Font contentFont = new Font(baseFont, 12);
            Font subtitleFont = new Font(baseFont, 14, Font.BOLD);
            Font wordFont = new Font(baseFont, 12, Font.BOLD);
            Font normalFont = new Font(baseFont, 12);

            Paragraph title = new Paragraph("阅读理解", titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(15f);
            document.add(title);

            Paragraph storyTitle = new Paragraph(isEnglishOnly ? "Article" : "短文内容", subtitleFont);
            storyTitle.setAlignment(Element.ALIGN_LEFT);
            storyTitle.setSpacingAfter(10f);
            document.add(storyTitle);

            Paragraph content = new Paragraph(cleanContent, contentFont);
            content.setAlignment(Element.ALIGN_LEFT);
            content.setFirstLineIndent(20f);
            content.setSpacingAfter(20f);
            content.setLeading(20f);
            document.add(content);

            if (!wordsList.isEmpty()) {
                Paragraph wordsTitle = new Paragraph(isEnglishOnly ? "Vocabulary" : "阅读理解", subtitleFont);
                wordsTitle.setAlignment(Element.ALIGN_LEFT);
                wordsTitle.setSpacingAfter(10f);
                document.add(wordsTitle);

                PdfPTable table = new PdfPTable(3);
                table.setWidthPercentage(100);
                table.setWidths(new float[]{2, 2, 4});

                PdfPCell headerCell1 = new PdfPCell(new Phrase(isEnglishOnly ? "Word" : "单词", wordFont));
                PdfPCell headerCell2 = new PdfPCell(new Phrase(isEnglishOnly ? "Phonetic" : "音标", wordFont));
                PdfPCell headerCell3 = new PdfPCell(new Phrase(isEnglishOnly ? "Meaning" : "释义", wordFont));
                headerCell1.setHorizontalAlignment(Element.ALIGN_CENTER);
                headerCell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                headerCell3.setHorizontalAlignment(Element.ALIGN_CENTER);
                headerCell1.setPadding(8f);
                headerCell2.setPadding(8f);
                headerCell3.setPadding(8f);
                table.addCell(headerCell1);
                table.addCell(headerCell2);
                table.addCell(headerCell3);

                for (Words word : wordsList) {
                    PdfPCell cell1 = new PdfPCell(new Phrase(word.getWord(), normalFont));
                    cell1.setHorizontalAlignment(Element.ALIGN_LEFT);
                    cell1.setPadding(8f);
                    table.addCell(cell1);

                    PdfPCell cell2 = new PdfPCell(new Phrase(word.getUkIpa() != null ? word.getUkIpa() : "", normalFont));
                    cell2.setHorizontalAlignment(Element.ALIGN_CENTER);
                    cell2.setPadding(8f);
                    table.addCell(cell2);

                    StringBuilder meanings = new StringBuilder();
                    if (word.getWordCollocations() != null && !word.getWordCollocations().isEmpty()) {
                        word.getWordCollocations().forEach(item -> {
                            if ("part_of_speech".equals(item.getType())) {
                                meanings.append(item.getFormattedType()).append(": ").append(item.getChinese()).append("\n");
                            }
                        });
                    }
                    PdfPCell cell3 = new PdfPCell(new Phrase(meanings.toString(), normalFont));
                    cell3.setHorizontalAlignment(Element.ALIGN_LEFT);
                    cell3.setPadding(8f);
                    table.addCell(cell3);
                }

                document.add(table);
            }

            document.add(new Chunk("\n"));
            Paragraph footer = new Paragraph("", normalFont);
            footer.setAlignment(Element.ALIGN_CENTER);
            document.add(footer);

            Object domainObj = redisUtil.get("SuperWords:config:DOMAIN");
            String domain = domainObj != null ? domainObj.toString() : "http://localhost:10000";
            String fileUrl = domain + "/super-words/sys/common/static/" + fileName;

            return Result.OK(fileUrl);

        } catch (Exception e) {
            log.error("导出PDF失败", e);
            System.out.println(e.getMessage());
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } finally {
            document.close();
        }

        return Result.error("导出失败");
    }


    /**
     * 清理HTML标签，只保留文本内容
     *
     * @param html 包含HTML标签的文本
     * @return 清理后的纯文本
     */
    private String cleanHtmlTags(String html) {
        if (StringUtils.isBlank(html)) {
            return "";
        }

        // 移除所有HTML标签
        String noHtml = html.replaceAll("<[^>]*>", "");

        // 替换HTML实体
        String result = noHtml.replaceAll("&nbsp;", " ")
                .replaceAll("&lt;", "<")
                .replaceAll("&gt;", ">")
                .replaceAll("&amp;", "&")
                .replaceAll("&quot;", "\"")
                .replaceAll("&apos;", "'");

        return result;
    }


//	 @ApiOperation(value="单词表-通过id查询", notes="单词表-通过id查询")
//	 @GetMapping(value = "/testIse")
//	 public Result<Words> testIse(@RequestParam(name="id",required=true) String id) throws IOException, SignatureException {

    /// /		 iseService.processSpeechEvaluation();
//		 return Result.OK("");
//	 }
    @ApiOperation(value = "单词表-获取音标音视频地址", notes = "单词表-获取音标音视频地址")
    @GetMapping(value = "/phonetic_symbols")
    public Result<List<PhoneticSymbols>> testIse(@RequestParam(name = "phonetics", required = true) String phonetics) {
        phonetics = phonetics.trim().replace("/", "").toLowerCase();
        List<String> split = Arrays.asList(phonetics.split(","));
        List<PhoneticSymbols> list = phoneticSymbolsService.list(new QueryWrapper<PhoneticSymbols>().lambda().in(PhoneticSymbols::getName, split));
        list.forEach(item -> {
            String baseUrl = redisUtil.get("SuperWords:config:DOMAIN") + "/super-words/sys/common/static/";
            item.setFullUrl(baseUrl + (item.getAudioUrl() != null ? item.getAudioUrl() : ""));
        });
        return Result.OK(list);
    }
}
