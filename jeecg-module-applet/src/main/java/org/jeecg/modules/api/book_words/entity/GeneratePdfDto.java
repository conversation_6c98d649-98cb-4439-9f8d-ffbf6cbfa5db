package org.jeecg.modules.api.book_words.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import javax.validation.constraints.NotEmpty;
import java.util.List;

@Data
public class GeneratePdfDto {

    @NotEmpty(message = "请输入需要生成的内容")
    @ApiModelProperty(value = "需要生成的内容")
    @JsonProperty("construction")
    private List<ConstructionItem> construction;

}