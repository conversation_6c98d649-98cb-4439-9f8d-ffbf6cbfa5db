package org.jeecg.modules.api.test_question.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang.StringUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.api.book_words.entity.BookWords;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.books.entity.WordBooks;
import org.jeecg.modules.api.books.service.WordBooksService;
import org.jeecg.modules.api.test_question.entity.*;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionInfoService;
import org.jeecg.modules.api.test_question.service.IInzTestQuestionService;
import org.jeecg.modules.api.test_question.service.IInzWordQuestionService;
import org.jeecg.modules.api.word_book_chapter.entity.WordBookChapter;
import org.jeecg.modules.api.word_book_chapter.service.WordBookChapterService;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 精选试题
 * @Author: jeecg-boot
 * @Date: 2025-01-25
 * @Version: V1.0
 */
@Api(tags = "H5 - 精选试题")
@RestController
@RequestMapping("/test_question")
@Slf4j
public class InzTestQuestionController extends JeecgController<InzTestQuestion, IInzTestQuestionService> {
    @Autowired
    private IInzTestQuestionService inzTestQuestionService;

    @Autowired
    private IInzTestQuestionInfoService iInzTestQuestionInfoService;

    @Autowired
    private BookWordsService bookWordsService;

    @Autowired
    private WordsFrontService wordsFrontService;

    @Autowired
    private WordBooksService wordBooksService;

    @Autowired
    private WordBookChapterService wordBookChapterService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IInzWordQuestionService iInzWordQuestionService;

    @Autowired
    private WordsFrontService wordsService;

    /**
     * 分页列表查询
     *
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    // @AutoLog(value = "精选试题-分页列表查询")
    @ApiOperation(value = "精选试题-分页列表查询", notes = "精选试题-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzTestQuestion>> queryPageList(
            @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
            @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
            HttpServletRequest req) {
        InzTestQuestion inzTestQuestion = new InzTestQuestion();
        QueryWrapper<InzTestQuestion> queryWrapper = QueryGenerator.initQueryWrapper(inzTestQuestion,
                req.getParameterMap());
        queryWrapper.lambda().eq(InzTestQuestion::getCreateBy, CommonUtils.getUserIdByToken())
                .orderByDesc(InzTestQuestion::getCreateTime);
        Page<InzTestQuestion> page = new Page<InzTestQuestion>(pageNo, pageSize);
        IPage<InzTestQuestion> pageList = inzTestQuestionService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @return
     */
    @AutoLog(value = "精选试题-添加")
    @ApiOperation(value = "精选试题-添加", notes = "精选试题-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@Valid @RequestBody AddTestQuestionDto addTestQuestionDto) {
        InzTestQuestion inzTestQuestion = new InzTestQuestion();
        BeanUtils.copyProperties(addTestQuestionDto, inzTestQuestion);
        inzTestQuestion.setCreateBy(CommonUtils.getUserIdByToken());

        // 查询题库，随机取题
        long count = iInzWordQuestionService.count(new QueryWrapper<InzWordQuestion>().lambda()
                .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
                .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId()));
        if (count < Long.parseLong(inzTestQuestion.getQuestionCount())) {
            return Result.error("题库不足！");
        }

        // 先保存主记录
        inzTestQuestionService.save(inzTestQuestion);

        // 生成题目并保存详情记录
        Result<List<InzTestQuestionInfo>> result = inzTestQuestionService.genderQuestion(inzTestQuestion);

        // 返回生成的题目数据
        return Result.OK(result.getResult());
    }

    /**
     * 添加试题与试题详情
     *
     * @param addTestQuestionDto
     * @return
     */
    @AutoLog(value = "精选试题-sse后调用添加")
    @ApiOperation(value = "精选试题-sse后调用添加", notes = "精选试题-sse后调用添加")
    @PostMapping(value = "/saveQuestion")
    public Result<String> saveQuestion(@Valid @RequestBody AddTestQuestionDto addTestQuestionDto) {
        InzTestQuestion inzTestQuestion = new InzTestQuestion();
        BeanUtils.copyProperties(addTestQuestionDto, inzTestQuestion);
        inzTestQuestionService.save(inzTestQuestion);
        addTestQuestionDto.getQuestionDetail().forEach(item -> {
            InzTestQuestionInfo inzTestQuestionInfo = new InzTestQuestionInfo();
            inzTestQuestionInfo.setTestQuestionId(inzTestQuestion.getId());
            inzTestQuestionInfo.setOptions(String.valueOf(item.getOptions()));
            inzTestQuestionInfo.setTrueAnswer(String.valueOf(item.getTrue_answer()));
            inzTestQuestionInfo.setQuestion(item.getQuestion());
            inzTestQuestionInfo.setQuestionType(item.getQuestion_type());
            inzTestQuestionInfo.setOtherContent(String.valueOf(item.getOther_content()));
            iInzTestQuestionInfoService.save(inzTestQuestionInfo);
        });
        return Result.OK("添加成功");
    }

    private void saveQuestionInfo(@Valid @RequestBody AddTestQuestionDto addTestQuestionDto) {
        // 查找现有记录而不是创建新记录
        InzTestQuestion existingQuestion = inzTestQuestionService.getOne(
                new QueryWrapper<InzTestQuestion>().lambda()
                        .eq(InzTestQuestion::getBookId, addTestQuestionDto.getBookId())
                        .eq(InzTestQuestion::getUnitId, addTestQuestionDto.getUnitId())
                        .eq(InzTestQuestion::getCreateBy, addTestQuestionDto.getCreateBy())
                        .orderByDesc(InzTestQuestion::getCreateTime)
                        .last("LIMIT 1"));

        if (existingQuestion != null) {
            // 更新现有记录的完成时间
            existingQuestion.setFinishTime(new Date());
            inzTestQuestionService.updateById(existingQuestion);

            // 使用现有记录的ID保存详情
            addTestQuestionDto.getQuestionDetail().forEach(item -> {
                InzTestQuestionInfo inzTestQuestionInfo = new InzTestQuestionInfo();
                inzTestQuestionInfo.setTestQuestionId(existingQuestion.getId()); // 使用现有ID
                inzTestQuestionInfo.setOptions(String.valueOf(item.getOptions()));
                inzTestQuestionInfo.setTrueAnswer(String.valueOf(item.getTrue_answer()));
                inzTestQuestionInfo.setQuestion(item.getQuestion());
                inzTestQuestionInfo.setQuestionType(item.getQuestion_type());
                inzTestQuestionInfo.setOtherContent(String.valueOf(item.getOther_content()));
                iInzTestQuestionInfoService.save(inzTestQuestionInfo);
            });
        }
    }

    @AutoLog(value = "获取精选试题")
    @ApiOperation(value = "获取精选试题", notes = "获取精选试题")
    @PostMapping(value = "/getSimpleTestQuestion")
    public Result<List<InzTestQuestionInfo>> getSimpleTestQuestion(
            @Valid @RequestBody AddTestQuestionDto addTestQuestionDto) {
        try {
            // 1. 先查询主试题记录获取ID
            LambdaQueryWrapper<InzTestQuestion> mainQueryWrapper = new LambdaQueryWrapper<>();
            mainQueryWrapper.eq(InzTestQuestion::getBookId, addTestQuestionDto.getBookId())
                    .eq(InzTestQuestion::getUnitId, addTestQuestionDto.getUnitId());

            if (addTestQuestionDto.getIsNeedAnswer() != null) {
                mainQueryWrapper.eq(InzTestQuestion::getIsNeedAnswer, addTestQuestionDto.getIsNeedAnswer());
            }

            mainQueryWrapper.orderByDesc(InzTestQuestion::getCreateTime);

            List<InzTestQuestion> testQuestions = inzTestQuestionService.list(mainQueryWrapper);

            if (testQuestions.isEmpty()) {
                return Result.OK("暂无符合条件的试题数据", new ArrayList<>());
            }

            // 2. 获取试题ID列表
            List<String> testQuestionIds = testQuestions.stream()
                    .map(InzTestQuestion::getId)
                    .collect(Collectors.toList());

            // 3. 处理题型筛选
            List<String> questionTypes = new ArrayList<>();
            if (!"0".equals(addTestQuestionDto.getQuestionType())) {
                questionTypes = Arrays.asList(addTestQuestionDto.getQuestionType().split(","));
            }

            // 4. 查询试题详情
            LambdaQueryWrapper<InzTestQuestionInfo> detailQueryWrapper = new LambdaQueryWrapper<>();
            detailQueryWrapper.in(InzTestQuestionInfo::getTestQuestionId, testQuestionIds);

            // 如果指定了题型，则按题型筛选
            if (!questionTypes.isEmpty()) {
                // 将数字题型转换为题型名称
                List<String> typeNames = new ArrayList<>();
                for (String type : questionTypes) {
                    List<String> names = getQuestionTypes(type);
                    if (!names.isEmpty()) {
                        typeNames.add(names.get(0));
                    }
                }
                if (!typeNames.isEmpty()) {
                    detailQueryWrapper.in(InzTestQuestionInfo::getQuestionType, typeNames);
                }
            }

            detailQueryWrapper.orderByAsc(InzTestQuestionInfo::getTestQuestionId)
                    .orderByAsc(InzTestQuestionInfo::getSort);

            List<InzTestQuestionInfo> allQuestionDetails = iInzTestQuestionInfoService.list(detailQueryWrapper);

            if (allQuestionDetails.isEmpty()) {
                return Result.OK("暂无符合条件的试题详情", new ArrayList<>());
            }

            // 5. 随机筛选指定数量的试题
            List<InzTestQuestionInfo> finalResult = randomSelectQuestions(
                    allQuestionDetails,
                    addTestQuestionDto.getQuestionCount() != null
                            ? Integer.parseInt(addTestQuestionDto.getQuestionCount())
                            : 10,
                    questionTypes.isEmpty());

            return Result.OK("查询成功", finalResult);

        } catch (Exception e) {
            log.error("查询试题详情失败：", e);
            return Result.error("查询失败：" + e.getMessage());
        }
    }

    /**
     * 随机筛选试题
     * 
     * @param allQuestions 所有试题
     * @param targetCount  目标数量
     * @param isAllTypes   是否包含所有题型
     * @return 筛选后的试题列表
     */
    private List<InzTestQuestionInfo> randomSelectQuestions(List<InzTestQuestionInfo> allQuestions,
            Integer targetCount,
            boolean isAllTypes) {
        if (allQuestions.isEmpty() || targetCount <= 0) {
            return new ArrayList<>();
        }

        // 如果总数不超过目标数量，直接返回所有试题
        if (allQuestions.size() <= targetCount) {
            Collections.shuffle(allQuestions);
            return allQuestions;
        }

        if (isAllTypes) {
            // 如果是所有题型，直接随机选择
            Collections.shuffle(allQuestions);
            return allQuestions.subList(0, targetCount);
        } else {
            // 按题型分组
            Map<String, List<InzTestQuestionInfo>> questionsByType = allQuestions.stream()
                    .collect(Collectors.groupingBy(InzTestQuestionInfo::getQuestionType));

            List<InzTestQuestionInfo> result = new ArrayList<>();
            List<String> types = new ArrayList<>(questionsByType.keySet());
            Random random = new Random();

            int remainingCount = targetCount;
            int remainingTypes = types.size();

            for (int i = 0; i < types.size() && remainingCount > 0; i++) {
                String type = types.get(i);
                List<InzTestQuestionInfo> typeQuestions = questionsByType.get(type);

                // 计算当前题型应该选择的数量
                int currentTypeCount;
                if (i == types.size() - 1) {
                    // 最后一个题型，分配剩余的所有数量
                    currentTypeCount = remainingCount;
                } else {
                    // 随机分配数量，但至少保证每个剩余题型能分到1个
                    int maxForCurrentType = remainingCount - (remainingTypes - 1);
                    currentTypeCount = random.nextInt(maxForCurrentType) + 1;
                }

                // 确保不超过该题型的实际数量
                currentTypeCount = Math.min(currentTypeCount, typeQuestions.size());

                // 随机选择该题型的试题
                Collections.shuffle(typeQuestions);
                result.addAll(typeQuestions.subList(0, currentTypeCount));

                remainingCount -= currentTypeCount;
                remainingTypes--;
            }

            // 最终随机打乱结果顺序
            Collections.shuffle(result);
            return result;
        }
    }

    @AutoLog(value = "精选试题-sse")
    @ApiOperation(value = "精选试题-sse", notes = "精选试题-sse")
    @PostMapping(value = "/sseTest", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter test(@Valid @RequestBody AddTestQuestionDto addTestQuestionDto) {
        // 创建 SSE Emitter
        SseEmitter emitter = new SseEmitter(30 * 60 * 1000L);

        // 创建内容收集容器（线程安全）
        StringBuilder contentBuilder = new StringBuilder();

        // 保存试题
        InzTestQuestion inzTestQuestion = new InzTestQuestion();
        BeanUtils.copyProperties(addTestQuestionDto, inzTestQuestion);
        addTestQuestionDto.setCreateBy(CommonUtils.getUserIdByToken());
        // inzTestQuestionService.save(inzTestQuestion);

        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/sseTest";
        String roomId = redisUtil.get("SuperWords:config:QUESTIONKEY").toString();
        List<BookWords> list = bookWordsService
                .list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzTestQuestion.getBookId())
                        .eq(BookWords::getChapterId, inzTestQuestion.getUnitId()));

        WordBooks books = wordBooksService
                .getOne(new QueryWrapper<WordBooks>().lambda().eq(WordBooks::getId, inzTestQuestion.getBookId()));
        WordBookChapter bookChapter = wordBookChapterService.getOne(
                new QueryWrapper<WordBookChapter>().lambda().eq(WordBookChapter::getId, inzTestQuestion.getUnitId()));

        List<String> wordIds = list.stream().map(BookWords::getWordId).collect(Collectors.toList());
        List<Words> wordsList = wordsFrontService.list(new QueryWrapper<Words>().lambda().in(Words::getId, wordIds));
        // 使用线程池处理异步任务
        ExecutorService executor = Executors.newSingleThreadExecutor();
        executor.execute(() -> {
            try {
                // 提取词汇名称并拼接
                String wordList = wordsList.stream()
                        .map(Words::getWord)
                        .collect(Collectors.joining(","));

                // 获取题型
                String questionType = inzTestQuestion.getQuestionType();
                List<String> questionTypes = getQuestionTypes(questionType);

                // 判断是否需要答案
                String isNeedAnswer = inzTestQuestion.getIsNeedAnswer() == 1 ? "需要" : "不需要";

                // 拼接消息
                String otherNeeds = StringUtils.isNotBlank(inzTestQuestion.getOtherNeeds())
                        ? inzTestQuestion.getOtherNeeds()
                        : "无";
                String sendMessage = String.format("单词：%s。题型：%s。参数答案：%s。题目数量：%s。其他需求：%s。词书prompt：%s，章节prompt:%s",
                        wordList,
                        String.join(",", questionTypes),
                        isNeedAnswer,
                        inzTestQuestion.getQuestionCount(),
                        otherNeeds,
                        books.getPrompt(),
                        bookChapter.getPrompt());

                // 先对消息内容进行URL编码
                String encodedMessage = URLEncoder.encode(
                        String.format("{\"content\":\"%s\",\"role\":\"user\"}", sendMessage),
                        StandardCharsets.UTF_8.toString());

                // 动态生成SSE URL
                String sseUrl = String.format(url +
                        "?ai_platform=CloseAi&" +
                        "big_module=gpt-4o-mini&" +
                        "message=%s&" + // 使用编码后的消息
                        "room_id=%s", // 使用动态生成的roomId
                        encodedMessage,
                        roomId); // 使用从Redis获取的roomId
                OkHttpClient client = new OkHttpClient.Builder()
                        .connectTimeout(30, TimeUnit.SECONDS)
                        .readTimeout(0, TimeUnit.SECONDS) // 禁用读取超时
                        .writeTimeout(30, TimeUnit.SECONDS)
                        .retryOnConnectionFailure(true)
                        .build();

                Request request = new Request.Builder()
                        .url(sseUrl)
                        .header("Accept", "text/event-stream")
                        .header("Cache-Control", "no-cache")
                        .header("Connection", "keep-alive")
                        .build();

                try (Response response = client.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        BufferedReader reader = new BufferedReader(response.body().charStream());
                        String line;
                        try {
                            while ((line = reader.readLine()) != null) {
                                // 检查是否为结束标记
                                if (line.startsWith("data: [DONE]")) {
                                    log.info("接收到结束信号");
                                    break; // 主动跳出循环
                                }

                                // 发送数据给客户端
                                if (!line.isEmpty()) {
                                    emitter.send(SseEmitter.event().data(line));
                                }

                                // 收集有效数据
                                if (line.startsWith("data: ")) {
                                    String jsonData = line.substring(5).trim();
                                    try {

                                        ObjectMapper mapper = new ObjectMapper();
                                        JsonNode rootNode = mapper.readTree(jsonData);

                                        // 提取content路径：choices[0].delta.content
                                        JsonNode choices = rootNode.path("choices");
                                        if (choices.isArray() && choices.size() > 0) {
                                            JsonNode delta = choices.get(0).path("delta");
                                            String content = delta.path("content").asText("");

                                            // 只保存非空内容
                                            if (!content.isEmpty()) {
                                                contentBuilder.append(content);
                                                log.debug("提取到内容: {}", content);
                                            }
                                        }
                                    } catch (Exception e) {
                                        log.error("JSON解析失败 | 数据: {} | 错误: {}", jsonData, e.getMessage());
                                    }
                                }
                            }
                        } finally {
                            // 最终处理（无论是否正常结束）
                            String fullContent = contentBuilder.toString();
                            // 1. 分割内容
                            String[] parts = fullContent.split("===END===\\s*");
                            if (parts.length < 2) {
                                throw new IllegalArgumentException("内容格式错误");
                            }
                            // 2. 清理JSON代码块标记
                            String jsonContent = parts[1]
                                    .replaceAll("```json", "") // 移除开始标记
                                    .replaceAll("```", "") // 移除结束标记
                                    .trim();

                            // 2. JSON解析配置
                            ObjectMapper mapper = new ObjectMapper();
                            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

                            // 3. 类型转换
                            JavaType type = mapper.getTypeFactory().constructParametricType(
                                    List.class,
                                    AddTestQuestionDto.QuestionDetail.class);

                            List<AddTestQuestionDto.QuestionDetail> details = mapper.readValue(jsonContent, type);

                            // 4. 构建完整DTO
                            AddTestQuestionDto saveDto = new AddTestQuestionDto();
                            BeanUtils.copyProperties(addTestQuestionDto, saveDto);
                            saveDto.setQuestionDetail(details);

                            // 5. 调用保存接口
                            saveQuestionInfo(saveDto);

                            log.info("完整收集内容：\n{}", fullContent);
                        }

                        // 正常完成
                        emitter.complete();
                    } else {
                        emitter.completeWithError(new RuntimeException("SSE 请求失败: " + response.code()));
                    }
                } catch (IOException e) {
                    emitter.completeWithError(e);
                }
            } catch (Exception e) {
                emitter.completeWithError(e);
            } finally {
                executor.shutdown();
            }
        });

        return emitter;
    }

    private List<String> getQuestionTypes(String questionType) {
        List<String> questionTypes = new ArrayList<>();

        // 定义题型映射表 (类型编号 -> 题型名称列表)
        Map<String, List<String>> typeMap = new HashMap<>();
        typeMap.put("1", Collections.singletonList("单选题"));
        typeMap.put("2", Collections.singletonList("多选题"));
        typeMap.put("3", Collections.singletonList("判断题"));
        typeMap.put("4", Collections.singletonList("填空题"));
        typeMap.put("5", Collections.singletonList("应用题"));

        if ("0".equals(questionType)) {
            // 如果是0，返回所有题型
            typeMap.values().forEach(questionTypes::addAll);
        } else {
            // 分割逗号分隔的题型编号（例如 "1,2,3"）
            String[] typeCodes = questionType.split(",");
            for (String code : typeCodes) {
                String trimmedCode = code.trim();
                List<String> types = typeMap.get(trimmedCode);
                if (types != null) {
                    questionTypes.addAll(types);
                }
            }
        }
        return questionTypes;
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    // @AutoLog(value = "精选试题-通过id查询")
    @ApiOperation(value = "精选试题-通过id查询", notes = "精选试题-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzTestQuestion> queryById(@RequestParam(name = "id", required = true) String id) {
        InzTestQuestion inzTestQuestion = inzTestQuestionService.getById(id);
        if (inzTestQuestion == null) {
            return Result.error("未找到对应数据");
        }
        iInzTestQuestionInfoService.list(new QueryWrapper<InzTestQuestionInfo>().lambda()
                .eq(InzTestQuestionInfo::getTestQuestionId, id).orderBy(true, true, InzTestQuestionInfo::getSort));
        return Result.OK(inzTestQuestion);
    }

    /**
     * 导出pdf
     *
     * @param inzTestQuestion
     */
    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    @ApiOperation(value = "导出pdf", notes = "导出pdf")
    @PostMapping(value = "/exportPdf")
    public Result<String> exportPdf(@Valid @RequestBody ExportTestQuestionDto inzTestQuestion,
            HttpServletResponse response) {
        InzTestQuestion testQuestion = inzTestQuestionService.getById(inzTestQuestion.getId());

        List<InzTestQuestionInfo> list = iInzTestQuestionInfoService.list(
                new QueryWrapper<InzTestQuestionInfo>().lambda()
                        .eq(InzTestQuestionInfo::getTestQuestionId, inzTestQuestion.getId())
                        .orderBy(true, true, InzTestQuestionInfo::getSort));

        if (list == null || list.isEmpty()) {
            return Result.error("未找到对应数据");
        }

        log.info("开始处理PDF导出，共 {} 道题目", list.size());

        // 重组数据：将平铺的题目列表重组为层级结构
        Map<String, List<InzTestQuestionInfo>> hierarchicalData = reorganizeQuestions(list);
        log.info("数据重组完成，共 {} 个题型分组", hierarchicalData.size());

        Document document = new Document(PageSize.A4, 50, 50, 50, 50);
        String fileName = "精选试题.pdf";
        log.info("输出文件路径：" + uploadpath + File.separator + fileName);

        try {
            // 确保 uploadpath 目录存在
            File uploadDir = new File(uploadpath);
            if (!uploadDir.exists()) {
                uploadDir.mkdirs();
            }

            String filePath = uploadpath + File.separator + fileName;
            PdfWriter writer = PdfWriter.getInstance(document, new FileOutputStream(filePath));

            // 加载字体
            URL fontUrl = getClass().getClassLoader().getResource("font/NotoSansSC-VariableFont_wght.ttf");
            if (fontUrl == null)
                throw new Exception("字体文件未找到！");
            File tempFontFile = new File("tempFont.ttf");
            if (!tempFontFile.exists()) {
                try (InputStream in = fontUrl.openStream(); FileOutputStream out = new FileOutputStream(tempFontFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                }
            }

            // 加载背景图片
            URL bgUrl = getClass().getClassLoader().getResource("pdfTemplate/bg.jpg");
            if (bgUrl == null)
                throw new Exception("背景图片未找到！");
            Image bgImage = Image.getInstance(bgUrl);
            bgImage.scaleAbsolute(PageSize.A4.getWidth(), PageSize.A4.getHeight()); // 全页大小
            bgImage.setAbsolutePosition(0, 0); // 左下角开始

            document.open();

            // 字体设置
            BaseFont baseFont = BaseFont.createFont(tempFontFile.getAbsolutePath(), BaseFont.IDENTITY_H,
                    BaseFont.EMBEDDED);
            Font titleFont = new Font(baseFont, 16, Font.BOLD);
            Font sectionFont = new Font(baseFont, 14, Font.BOLD);
            Font questionFont = new Font(baseFont, 12, Font.NORMAL);
            Font optionsFont = new Font(baseFont, 12, Font.NORMAL);
            Font answerFont = new Font(baseFont, 12, Font.NORMAL);

            // 标题
            Paragraph title = new Paragraph("精选试题", titleFont);
            title.setAlignment(Element.ALIGN_CENTER);
            title.setSpacingAfter(20f);
            document.add(title);

            // ✅ 插入背景图
            PdfContentByte canvas = writer.getDirectContentUnder();
            canvas.addImage(bgImage);

            // 使用重组后的层级数据生成PDF
            generateHierarchicalPDF(document, hierarchicalData, questionFont, optionsFont, sectionFont);

            // 显示答案部分（仅当需要显示答案时）
            if (testQuestion.getIsNeedAnswer() == 1) {
                generateHierarchicalAnswers(document, hierarchicalData, answerFont, sectionFont);
            }

            String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
            String fileUrl = domain + "/super-words/sys/common/static/" + fileName;
            return Result.OK(fileUrl);

        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return Result.error("导出失败");
        } finally {
            document.close();
        }
    }

    // 辅助方法：获取题型标题
    private String getSectionTitle(int sectionNumber, String questionType, int questionCount) {
        String chineseNumber;
        switch (sectionNumber) {
            case 1:
                chineseNumber = "一";
                break;
            case 2:
                chineseNumber = "二";
                break;
            case 3:
                chineseNumber = "三";
                break;
            case 4:
                chineseNumber = "四";
                break;
            case 5:
                chineseNumber = "五";
            case 6:
                chineseNumber = "六";
                break;
            default:
                chineseNumber = String.valueOf(sectionNumber);
        }

        int scorePerQuestion = getScorePerQuestion(questionType);
        int totalScore = questionCount * scorePerQuestion;

        return String.format("%s、%s（每小题%d分，共%d分）",
                chineseNumber, questionType, scorePerQuestion, totalScore);
    }

    // 辅助方法：获取每题分数
    private int getScorePerQuestion(String questionType) {
        switch (questionType) {
            case "单选题":
                return 2;
            case "多选题":
                return 3;
            case "判断题":
                return 2;
            case "填空题":
                return 3;
            case "应用题":
                return 15;
            default:
                return 2;
        }
    }

    private String getAnswerSectionTitle(int sectionNumber, String questionType) {
        String chineseNumber;
        switch (sectionNumber) {
            case 1:
                chineseNumber = "一";
                break;
            case 2:
                chineseNumber = "二";
                break;
            case 3:
                chineseNumber = "三";
                break;
            case 4:
                chineseNumber = "四";
                break;
            case 5:
                chineseNumber = "五";
                break;
            case 6:
                chineseNumber = "六";
            default:
                chineseNumber = String.valueOf(sectionNumber);
        }
        return String.format("%s、%s", chineseNumber, questionType);
    }

    /**
     * 重组题目数据：将平铺的题目列表重组为层级结构
     *
     * @param questions 平铺的题目列表
     * @return 层级结构的题目数据 Map<题型, List<题目>>
     */
    private Map<String, List<InzTestQuestionInfo>> reorganizeQuestions(List<InzTestQuestionInfo> questions) {
        log.info("开始重组题目数据，原始题目数量: {}", questions.size());

        // 分离父题目和子题目
        Map<String, InzTestQuestionInfo> parentQuestions = new HashMap<>();
        Map<String, List<InzTestQuestionInfo>> childQuestionsByParent = new HashMap<>();
        List<InzTestQuestionInfo> regularQuestions = new ArrayList<>();

        for (InzTestQuestionInfo question : questions) {
            String parentId = question.getParentId();

            if ("0".equals(parentId) || parentId == null) {
                // 父题目（应用题主题目）或普通题目
                if ("应用题".equals(question.getQuestionType())) {
                    parentQuestions.put(question.getId(), question);
                    log.info("发现应用题父题目: id={}, question={}",
                             question.getId(),
                             question.getQuestion().length() > 50 ? question.getQuestion().substring(0, 50) + "..." : question.getQuestion());
                } else {
                    regularQuestions.add(question);
                }
            } else {
                // 子题目
                childQuestionsByParent.computeIfAbsent(parentId, k -> new ArrayList<>()).add(question);
                log.info("发现子题目: parentId={}, question={}",
                         parentId,
                         question.getQuestion().length() > 30 ? question.getQuestion().substring(0, 30) + "..." : question.getQuestion());
            }
        }

        log.info("数据分类完成 - 应用题父题目: {}, 普通题目: {}, 子题目组: {}",
                 parentQuestions.size(), regularQuestions.size(), childQuestionsByParent.size());

        // 构建最终的层级结构
        Map<String, List<InzTestQuestionInfo>> result = new LinkedHashMap<>();

        // 处理普通题目（按题型分组）
        Map<String, List<InzTestQuestionInfo>> regularByType = regularQuestions.stream()
                .collect(Collectors.groupingBy(InzTestQuestionInfo::getQuestionType, LinkedHashMap::new, Collectors.toList()));

        for (Map.Entry<String, List<InzTestQuestionInfo>> entry : regularByType.entrySet()) {
            String questionType = entry.getKey();
            List<InzTestQuestionInfo> typeQuestions = entry.getValue();
            typeQuestions.sort(Comparator.comparingInt(InzTestQuestionInfo::getSort));
            result.put(questionType, typeQuestions);
            log.info("添加普通题型: {}, 题目数量: {}", questionType, typeQuestions.size());
        }

        // 处理应用题（每个应用题作为一个独立的组）
        for (Map.Entry<String, InzTestQuestionInfo> entry : parentQuestions.entrySet()) {
            String parentId = entry.getKey();
            InzTestQuestionInfo parentQuestion = entry.getValue();

            List<InzTestQuestionInfo> hierarchicalGroup = new ArrayList<>();
            hierarchicalGroup.add(parentQuestion);

            // 添加子题目
            List<InzTestQuestionInfo> children = childQuestionsByParent.get(parentId);
            if (children != null && !children.isEmpty()) {
                children.sort(Comparator.comparingInt(InzTestQuestionInfo::getSort));
                hierarchicalGroup.addAll(children);
                log.info("应用题 {} 包含 {} 个子题目", parentId, children.size());
            }

            // 使用特殊的键来标识应用题组
            String groupKey = "应用题_" + parentQuestion.getSort() + "_" + parentId;
            result.put(groupKey, hierarchicalGroup);
            log.info("添加应用题组: {}, 总题目数量: {}", groupKey, hierarchicalGroup.size());
        }

        log.info("题目重组完成，共生成 {} 个分组", result.size());
        return result;
    }