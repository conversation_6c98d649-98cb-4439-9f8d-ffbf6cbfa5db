package org.jeecg.modules.api.books.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description: 词书表
 * @Author: jeecg-boot
 * @Date:   2025-01-14
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_word_books对象", description="词书表")
public class WordBooksDto implements Serializable {
    private static final long serialVersionUID = 1L;

	/**词书名称*/
    @ApiModelProperty(value = "词书名称")
    private String name;
    @ApiModelProperty(value = "教育层次id")
    private String educationId;
}
