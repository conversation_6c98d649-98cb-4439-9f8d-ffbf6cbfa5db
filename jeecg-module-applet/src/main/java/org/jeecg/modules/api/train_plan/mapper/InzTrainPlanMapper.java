package org.jeecg.modules.api.train_plan.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.api.train_plan.entity.InzTrainPlan;

import java.util.List;

/**
 * @Description: 训练计划表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
public interface InzTrainPlanMapper extends BaseMapper<InzTrainPlan> {

    List<String> getExistingWordIdsByPlanId(@Param("id") String id, @Param("wordIds") List<String> wordIds);
}
