package org.jeecg.modules.api.train_plan.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Description: 训练计划表
 * @Author: jeecg-boot
 * @Date:   2025-01-21
 * @Version: V1.0
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_train_plan对象", description="训练计划表")
public class SaveTrainPlanDto implements Serializable {

	/**所属图书*/
    @NotEmpty(message = "所属图书不可为空")
    @ApiModelProperty(value = "所属图书",required = true)
    private String bookId;
	/**每日单词数量*/
    @NotNull(message = "每日单词数据不可为空")
    @ApiModelProperty(value = "每日单词数量",required = true)
    private Integer dailyWordsCount;
	/**完成天数*/
    @NotNull(message = "完成天数不可为空")
    @ApiModelProperty(value = "完成天数",required = true)
    private Integer finishDays;
}
