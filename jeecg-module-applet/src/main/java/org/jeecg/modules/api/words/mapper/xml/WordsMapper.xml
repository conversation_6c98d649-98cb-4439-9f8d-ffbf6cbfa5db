<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.jeecg.modules.api.words.mapper.WordsFrontMapper">

    <resultMap id="WordsResultMap" type="org.jeecg.modules.api.words.entity.Words">
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sysOrgCode" column="sys_org_code"/>
        <result property="bookId" column="book_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="word" column="word"/>
        <result property="ukIpa" column="uk_ipa"/>
        <result property="usIpa" column="us_ipa"/>
        <result property="status" column="status"/>
        <!-- 映射处理 ull.staus 到 type -->
        <result property="type" column="type"/>
        <result property="pronunciationGuide" column="pronunciation_guide"/>
        <result property="rootParticlesMean" column="root_particles_mean"/>
        <result property="homophonic" column="homophonic"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="usAudioUrl" column="us_audio_url"/>
        <result property="naturalAudioUrl" column="natural_audio_url"/>
        <result property="breakdownAudioUrl" column="breakdown_audio_url"/>
    </resultMap>
    <resultMap id="WordsWordCollocationsResultMap" type="org.jeecg.modules.api.words.entity.Words">
        <!-- Basic fields for Words -->
        <id property="id" column="id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="sysOrgCode" column="sys_org_code"/>
        <result property="bookId" column="book_id"/>
        <result property="chapterId" column="chapter_id"/>
        <result property="word" column="word"/>
        <result property="ukIpa" column="uk_ipa"/>
        <result property="usIpa" column="us_ipa"/>
        <result property="status" column="status"/>
        <result property="type" column="type"/>
        <result property="pronunciationGuide" column="pronunciation_guide"/>
        <result property="rootParticlesMean" column="root_particles_mean"/>
        <result property="homophonic" column="homophonic"/>
        <result property="audioUrl" column="audio_url"/>
        <result property="usAudioUrl" column="us_audio_url"/>
        <result property="naturalAudioUrl" column="natural_audio_url"/>
        <result property="breakdownAudioUrl" column="breakdown_audio_url"/>

        <!-- Nested field for WordCollocations -->
        <collection property="wordCollocations" ofType="org.jeecg.modules.api.word_collections.entity.WordCollocations">
            <id property="id" column="collocation_id"/>
            <result property="createBy" column="collocation_create_by"/>
            <result property="createTime" column="collocation_create_time"/>
            <result property="updateBy" column="collocation_update_by"/>
            <result property="updateTime" column="collocation_update_time"/>
            <result property="sysOrgCode" column="collocation_sys_org_code"/>
            <result property="wordId" column="collocation_word_id"/>
            <result property="english" column="collocation_english"/>
            <result property="chinese" column="collocation_chinese"/>
            <result property="type" column="collocation_type"/>
            <result property="audioUrl" column="collocation_audio_url"/>
            <result property="sort" column="collection_sort"/>
        </collection>
        <collection property="wordsEtymology" ofType="org.jeecg.modules.api.word_etymology.entity.WordsEtymology">
            <id property="id" column="etymology_id"/>
            <result property="createBy" column="etymology_create_by"/>
            <result property="createTime" column="etymology_create_time"/>
            <result property="updateBy" column="etymology_update_by"/>
            <result property="updateTime" column="etymology_update_time"/>
            <result property="wordId" column="etymology_word_id"/>
            <result property="originCh" column="origin_ch"/>
            <result property="meaningEvolution" column="meaning_evolution"/>
        </collection>

    </resultMap>
    <select id="listWithType" resultMap="WordsWordCollocationsResultMap">
        SELECT
        w.id,
        w.create_by,
        w.create_time,
        w.update_by,
        w.update_time,
        w.sys_org_code,
        w.book_id,
        w.chapter_id,
        w.word,
        w.uk_ipa,
        w.us_ipa,
        w.status,
        w.audio_url,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        iwc.id AS collocation_id,
        iwc.create_by AS collocation_create_by,
        iwc.create_time AS collocation_create_time,
        iwc.update_by AS collocation_update_by,
        iwc.update_time AS collocation_update_time,
        iwc.sys_org_code AS collocation_sys_org_code,
        iwc.word_id AS collocation_word_id,
        iwc.english AS collocation_english,
        iwc.chinese AS collocation_chinese,
        iwc.type AS collocation_type,
        iwc.audio_url AS collocation_audio_url,
        iwc.sort AS collection_sort,
        COALESCE(ull.status,0) AS type
        FROM inz_words w
        LEFT JOIN inz_user_learn_log ull ON w.id = ull.word_id
        LEFT JOIN inz_word_collocations iwc ON w.id = iwc.word_id
        <where>
            1=1
            <if test="words.bookId != null and words.bookId != ''">
                and w.book_id = #{words.bookId}
            </if>
            <if test="words.word != null and words.word != ''">
                and w.word like concat(concat('%',#{words.word}),'%')
            </if>
            <if test="words.createBy != null and words.createBy != ''">
                and ull.create_by = #{words.createBy}
            </if>
            <choose>
                <when test="words.type == 1">
                    AND ull.status = 1
                </when>
                <when test="words.type == 2">
                    AND ull.status = 2
                </when>
                <when test="words.type == 3">
                    AND ull.status = 3
                </when>
            </choose>
        </where>
        group by w.word
        order by LEFT(w.word, 1), iwc.sort asc
    </select>
    <select id="getOneWithCollection" resultMap="WordsWordCollocationsResultMap">
        SELECT
        w.id,
        w.create_by,
        w.create_time,
        w.update_by,
        w.update_time,
        w.sys_org_code,
        w.book_id,
        w.chapter_id,
        w.word,
        w.uk_ipa,
        w.us_ipa,
        w.status,
        w.audio_url,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.homophonic,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        iwc.id AS collocation_id,
        iwc.create_by AS collocation_create_by,
        iwc.create_time AS collocation_create_time,
        iwc.update_by AS collocation_update_by,
        iwc.update_time AS collocation_update_time,
        iwc.sys_org_code AS collocation_sys_org_code,
        iwc.word_id AS collocation_word_id,
        iwc.english AS collocation_english,
        iwc.chinese AS collocation_chinese,
        iwc.type AS collocation_type,
        iwc.audio_url AS collocation_audio_url,
        iwc.sort AS collection_sort,
        wety.id AS etymology_id,
        wety.word_id AS etymology_word_id,
        wety.origin_ch AS origin_ch,
        wety.meaning_evolution AS meaning_evolution
        FROM
        inz_words w
        LEFT JOIN inz_word_collocations iwc ON w.id = iwc.word_id
        left join inz_words_etymology wety ON w.id = wety.word_id
        <where>
            w.id = #{wordsId}
--         and iwc.type in ('part_of_speech','root_particles','natural_phonics')
        </where>
        order by iwc.sort asc
    </select>
    <select id="getAllWithCollection" resultMap="WordsWordCollocationsResultMap">
        SELECT
        w.id,
        w.create_by,
        w.create_time,
        w.update_by,
        w.update_time,
        w.sys_org_code,
        w.book_id,
        w.chapter_id,
        w.word,
        w.uk_ipa,
        w.us_ipa,
        w.status,
        w.audio_url,
        w.pronunciation_guide,
        w.root_particles_mean,
        w.us_audio_url,
        w.natural_audio_url,
        w.breakdown_audio_url,
        COALESCE(ull.status,0) AS type,
        iwc.id AS collocation_id,
        iwc.create_by AS collocation_create_by,
        iwc.create_time AS collocation_create_time,
        iwc.update_by AS collocation_update_by,
        iwc.update_time AS collocation_update_time,
        iwc.sys_org_code AS collocation_sys_org_code,
        iwc.word_id AS collocation_word_id,
        iwc.english AS collocation_english,
        iwc.chinese AS collocation_chinese,
        iwc.type AS collocation_type,
        iwc.audio_url AS collocation_audio_url,
        iwc.sort AS collection_sort
        FROM
        inz_words w
        LEFT JOIN inz_word_collocations iwc ON w.id = iwc.word_id
        LEFT JOIN inz_user_learn_log ull ON w.id = ull.word_id
        <if test="userIdByToken != null and userIdByToken != ''">
            and ull.create_by = #{userIdByToken}
        </if>
        <where>
            iwc.type in ('part_of_speech','root_particles','natural_phonics')
            <if test="ids != null and ids.size() > 0">
                AND w.id IN
                <foreach item="item" index="index" collection="ids" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="type!='' and type != null">
                and ull.status = #{type}
            </if>
        </where>
        order by iwc.sort asc
    </select>


</mapper>
