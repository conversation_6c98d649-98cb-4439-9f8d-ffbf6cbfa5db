import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '所属图书',
    align: "center",
    dataIndex: 'bookId'
  },
  {
    title: '测评时间',
    align: "center",
    dataIndex: 'evaluationTime'
  },
  {
    title: '正确率',
    align: "center",
    dataIndex: 'turePercent'
  },
  {
    title: '总用时',
    align: "center",
    dataIndex: 'useTime'
  },
  {
    title: '得分',
    align: "center",
    dataIndex: 'score'
  },
];

// 高级查询数据
export const superQuerySchema = {
  bookId: {title: '所属图书',order: 0,view: 'text', type: 'string',},
  evaluationTime: {title: '测评时间',order: 1,view: 'datetime', type: 'string',},
  turePercent: {title: '正确率',order: 2,view: 'number', type: 'number',},
  useTime: {title: '总用时',order: 3,view: 'number', type: 'number',},
  score: {title: '得分',order: 4,view: 'number', type: 'number',},
};
