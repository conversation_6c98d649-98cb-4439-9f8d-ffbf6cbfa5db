package org.jeecg.modules.api.book_words.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecg.common.system.base.entity.QuestionEntity;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Description: 单词短文表
 * @Author: jeecg-boot
 * @Date: 2023-07-22
 * @Version: V1.0
 */
@Data
@TableName("inz_word_article")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "inz_word_article对象", description = "单词短文表")
public class WordArticle implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * 词书id
     */
    @Excel(name = "词书id", width = 15)
    @ApiModelProperty(value = "词书id")
    private String bookId;

    /**
     * 章节id
     */
    @Excel(name = "章节id", width = 15)
    @ApiModelProperty(value = "章节id")
    private String chapterId;

    /**
     * 单词组ID列表，以逗号分隔
     */
    @Excel(name = "单词组ID列表", width = 15)
    @ApiModelProperty(value = "单词组ID列表")
    private String wordIds;

    /**
     * 单词组单词列表，以逗号分隔
     */
    @Excel(name = "单词组单词列表", width = 15)
    @ApiModelProperty(value = "单词组单词列表")
    private String words;

    /**
     * 生成的短文内容
     */
    @Excel(name = "短文内容", width = 15)
    @ApiModelProperty(value = "短文内容")
    private String content;

    /**
     * 中文释义
     */
    @Excel(name = "中文释义", width = 15)
    @ApiModelProperty(value = "中文释义")
    private java.lang.String chineseTranslation;

    /**
     * 短文标题
     */
    @Excel(name = "短文标题", width = 15)
    @ApiModelProperty(value = "短文标题")
    private String title;

    /**
     * 组序号（同一章节内的组序号）
     */
    @Excel(name = "组序号", width = 15)
    @ApiModelProperty(value = "组序号")
    private Integer groupIndex;

    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;

    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;

    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;

    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;

    /**
     * 所属部门
     */
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;

    @ApiModelProperty(value = "问题列表")
    @TableField(exist = false)
    private List<QuestionEntity> questionList;

    /**
     * 问题列表的JSON字符串形式，用于数据库存储
     */
    @Excel(name = "问题列表JSON", width = 15)
    @ApiModelProperty(value = "问题列表JSON")
    private String questionsJson;

    @Excel(name = "question", width = 15)
    @ApiModelProperty(value = "question")
    private String question;

    @Excel(name = "options", width = 15)
    @ApiModelProperty(value = "options")
    private Map<String, Objects> options;

    @Excel(name = "answer", width = 15)
    @ApiModelProperty(value = "answer")
    private String answer;

    @Excel(name = "questionChMeaning", width = 15)
    @ApiModelProperty(value = "questionChMeaning")
    private String questionChMeaning;

    /**
     * 是否包含特定单词，用于非数据库字段
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "是否包含特定单词")
    private Boolean containsWord;
} 