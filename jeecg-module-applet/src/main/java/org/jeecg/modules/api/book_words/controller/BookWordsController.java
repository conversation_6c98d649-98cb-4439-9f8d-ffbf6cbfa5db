package org.jeecg.modules.api.book_words.controller;

import cn.xfyun.api.IseClient;
import cn.xfyun.model.response.ise.IseResponseData;
import cn.xfyun.service.ise.AbstractIseWebSocketListener;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.xml.XmlMapper;
import com.lowagie.text.*;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.ColumnText;
import com.lowagie.text.pdf.PdfWriter;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.WebSocket;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.constant.ThirdRequestConstant;
import org.jeecg.common.system.base.controller.JeecgController;
import org.jeecg.common.system.base.entity.ConstructionEntity;
import org.jeecg.common.system.base.entity.FillInEntity;
import org.jeecg.common.system.base.entity.QuestionEntity;
import org.jeecg.common.util.CommonUtils;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.modules.api.book_words.entity.*;
import org.jeecg.modules.api.book_words.service.BookWordsService;
import org.jeecg.modules.api.book_words.service.WordArticleService;
import org.jeecg.modules.api.book_words.service.WordSentenceService;
import org.jeecg.modules.api.books.service.WordBooksService;
import org.jeecg.modules.api.user_collection_words.entity.InzUserCollectionWords;
import org.jeecg.modules.api.user_collection_words.service.IInzUserCollectionWordsService;
import org.jeecg.modules.api.user_learn_log.entity.InzUserLearnLog;
import org.jeecg.modules.api.user_learn_log.service.IInzUserLearnLogService;
import org.jeecg.modules.api.user_wrong_book.entity.InzUserWrongBook;
import org.jeecg.modules.api.user_wrong_book.service.IInzUserWrongBookService;
import org.jeecg.modules.api.word_book_chapter.entity.WordBookChapter;
import org.jeecg.modules.api.word_book_chapter.service.WordBookChapterService;
import org.jeecg.modules.api.word_collections.entity.WordCollocations;
import org.jeecg.modules.api.word_collections.service.WordCollocationsService;
import org.jeecg.modules.api.words.entity.Words;
import org.jeecg.modules.api.words.service.WordsFrontService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.util.*;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * @Description: inz_book_words
 * @Author: jeecg-boot
 * @Date: 2025-01-17
 * @Version: V1.0
 */
@Api(tags = "H5 - 单词表")
@RestController
@RequestMapping("/book_words")
@Slf4j
public class BookWordsController extends JeecgController<BookWords, BookWordsService> {
    @Autowired
    private BookWordsService bookWordsService;

    @Autowired
    private WordsFrontService wordsFrontService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private IInzUserLearnLogService inzUserLearnLogService;

    @Autowired
    private WordCollocationsService wordCollocationsService;

    @Autowired
    private WordBookChapterService wordBookChapterService;

    @Autowired
    private IInzUserCollectionWordsService inzUserCollectionWordsService;

    @Autowired
    private IInzUserWrongBookService inzUserWrongBookService;

    @Autowired
    private WordArticleService wordArticleService;

    @Autowired
    private WordSentenceService wordSentenceService;
    @Autowired
    private WordBooksService wordBooksService;

    /**
     * @param inzBookWords
     * @param req
     * @return
     */
    //@AutoLog(value = "inz_book_words-分页列表查询")
    @ApiOperation(value = "列表", notes = "列表")
    @GetMapping(value = "/listAll")
    public Result<List<BookWords>> queryPageList(BookWords inzBookWords,
                                                 @RequestParam(name = "type", required = false, defaultValue = "5") int type,
                                                 @RequestParam(name = "learnType", required = false, defaultValue = "") String learnType,
                                                 @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                 @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                 @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                 HttpServletRequest req) {
        if (StringUtils.isBlank(inzBookWords.getSortBy())) {
            inzBookWords.setSortBy("asc");
        }
        if (StringUtils.isNotBlank(learnType)) {
            inzBookWords.setType(Integer.valueOf(learnType));
        }
        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
        List<String> allWordIds;
        if (type != 5) {
            if (type == 1) {
                // 获取所有相关单词的列表
                if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                    inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                }
                List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()));
                allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            } else if (type == 2) {
                inzBookWords.setBookId(null);
                inzBookWords.setType(null);
                List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                        .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
                if (pageList.isEmpty()) {
                    return Result.OK("当前日期您还没有学习内容噢~~~");
                }
                allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            } else if (type == 3) {
                inzBookWords.setBookId(null);
                inzBookWords.setType(null);
                List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
                if (list.isEmpty()) {
                    return Result.OK("您还没有加入任何错题噢~~~");
                }
                allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            } else if (type == 4) {
                inzBookWords.setBookId(null);
                inzBookWords.setType(null);
                if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                    return Result.OK("参数错误");
                }
                List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                        .eq(InzUserCollectionWords::getUnitId, unitId));
                if (list.isEmpty()) {
                    return Result.OK("您还没有加入任何收藏本噢~~~");
                }
                allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            } else {
                allWordIds = Collections.emptyList();
            }
            inzBookWords.setWordIds(allWordIds);
        } else {
            // 获取所有相关单词的列表
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
            }
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }

        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWords(inzBookWords, 0, types);
        return Result.OK(list);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "inz_book_words-通过id查询")
    @ApiOperation(value = "通过id查询", notes = "通过id查询")
    @GetMapping(value = "/queryById")
    public Result<BookWords> queryById(@RequestParam(name = "id", required = true) String id) {
        BookWords inzBookWords = bookWordsService.getById(id);
        if (inzBookWords == null) {
            return Result.error("未找到对应数据");
        }
        BookWords bookWords = bookWordsService.getInfoById(id);
        return Result.OK(bookWords);
    }


    /**
     * 整书测评
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "整书测评", notes = "整书测评")
    @GetMapping(value = "/bookTest")
    public Result<List<BookWords>> bookTest(@RequestParam(name = "bookId", required = true) String bookId, HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        inzBookWords.setBookId(bookId);
        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());

        // 获取书籍总单词数量
        long totalWordCount = bookWordsService.count(new QueryWrapper<BookWords>().lambda()
                .eq(BookWords::getBookId, bookId));

        // 计算需要抽取的单词数量：至少50%，但每组最多50个
        int targetCount = Math.min(50, Math.max((int) (totalWordCount * 0.5), 10));


        // 查询单词，使用随机排序并限制数量
        List<BookWords> list = bookWordsService.list(new QueryWrapper<BookWords>().lambda()
                .eq(BookWords::getBookId, bookId)
                .last("ORDER BY RAND() LIMIT " + targetCount));

        // 将查询结果传递给listWithWordsLimit方法获取详细信息
        if (list != null && !list.isEmpty()) {
            List<String> wordIds = list.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(wordIds);
            list = bookWordsService.listWithWordsLimit(inzBookWords, targetCount, types);
        }

        if (list == null || list.isEmpty()) {
            return Result.error("没有找到相关的单词数据");
        }

        List<BookWords> listAll;
        long count = bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()));

        if (count >= 50) {
            listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).last("limit 50"));
        } else {
            listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()));
        }

        List<String> allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());

        List<Words> allWords = wordsFrontService.list(new QueryWrapper<Words>().lambda().in(Words::getId, allWordIds));

        Map<String, Words> wordsMap = allWords.stream().collect(Collectors.toMap(Words::getId, word -> word));

        // 创建独立的 SplittableRandom 实例
        SplittableRandom splittableRandom = new SplittableRandom();
        list.parallelStream().forEach(item -> {
            List<String> selectedWordIds = new ArrayList<>(allWordIds);
            selectedWordIds.remove(item.getWordId());
            Collections.shuffle(selectedWordIds, new Random(splittableRandom.nextLong()));  // 使用线程安全的随机数生成器
            selectedWordIds = selectedWordIds.subList(0, 3);
            selectedWordIds.add(item.getWordId());

            List<Words> selectedWords = selectedWordIds.stream()
                    .map(wordId -> wordsMap.get(wordId))  // 直接获取
                    .filter(Objects::nonNull)  // 过滤掉 null 值
                    .collect(Collectors.toList());

            List<String> options = selectedWords.stream()
                    .map(Words::getWord)
                    .collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            wordsList -> {
                                // 每个线程使用独立的随机实例
                                SplittableRandom localRandom = splittableRandom.split();
                                Collections.shuffle(wordsList, new Random(localRandom.nextLong()));
                                return wordsList.stream();
                            }))
                    .collect(Collectors.toList());

            item.setWordsOptions(options);
            item.setTrueAnswer(item.getWord());
        });
        return Result.OK(list);
    }


    /**
     * 学单元词汇表
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "学单元词汇表", notes = "学单元词汇表")
    @GetMapping(value = "/unitVocabularyData")
    public Result<List<BookWords>> unitVocabularyData(
            @RequestParam(name = "bookId", required = true) String bookId,
            @RequestParam(name = "chapterId", required = false) String chapterId,
            @RequestParam(name = "limit", defaultValue = "0") int limit, HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        inzBookWords.setBookId(bookId);
        inzBookWords.setChapterId(chapterId);
        if (StringUtils.isBlank(inzBookWords.getChapterId())) {
            inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
            if (limit == 0) {
                limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
            }
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        return Result.OK(list);
    }

    @ApiOperation(value = "学单元词汇表 - 自然拼读", notes = "学单元词汇表 - 自然拼读")
    @GetMapping(value = "/naturalData")
    public Result<List<BookWords>> naturalData(@RequestParam(name = "bookId", required = false) String bookId,
                                               @RequestParam(name = "phonetics", required = true) String phonetics,
                                               HttpServletRequest req) {
        List<String> allWordIds = null;
        if (StringUtils.isNotBlank(bookId)) {
            List<BookWords> list = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, bookId));
            allWordIds = list.stream().map(BookWords::getWordId).collect(Collectors.toList());
        }
        BookWords inzBookWords = new BookWords();
        List<Words> words = wordsFrontService.list(new QueryWrapper<Words>().lambda().like(Words::getWord, phonetics).in(allWordIds != null, Words::getId, allWordIds));
        if (!words.isEmpty()) {
            inzBookWords.setWordIds(words.stream().map(Words::getId).distinct().collect(Collectors.toList()));
            List<String> types = new ArrayList<>();
            types.add("part_of_speech");
            inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
            List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, 50, types);
            return Result.OK(list);
        } else {
            return Result.OK("未找到对应数据");
        }
    }

    @ApiOperation(value = "学单元词汇表 - 音标联动", notes = "学单元词汇表 - 音标联动")
    @GetMapping(value = "/phoneticSymbolsData")
    public Result<List<BookWords>> phoneticSymbolsData(@RequestParam(name = "phonetics", required = true) String phonetics,
                                                       @RequestParam(name = "bookId", required = false) String bookId,
                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                       HttpServletRequest req) {
        // 处理 phonetics 参数：无 / 则添加，已有则保持
        if (!phonetics.startsWith("/") || !phonetics.endsWith("/")) {
            // 移除可能存在的旧斜杠，再统一添加新斜杠
            String cleaned = phonetics.replaceAll("^/|/$", "");
            phonetics = "/" + cleaned + "/";
        }
        List<String> allWordIds = null;
        if (StringUtils.isNotBlank(bookId)) {
            List<BookWords> list = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, bookId));
            allWordIds = list.stream().map(BookWords::getWordId).collect(Collectors.toList());
        }

        BookWords inzBookWords = new BookWords();
        List<WordCollocations> naturalPhonics = wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda()
                .eq(WordCollocations::getType, "natural_phonics")
                .eq(WordCollocations::getChinese, phonetics).in(allWordIds != null, WordCollocations::getWordId, allWordIds));
        if (!naturalPhonics.isEmpty()) {
            inzBookWords.setWordIds(naturalPhonics.stream().map(WordCollocations::getWordId).distinct().collect(Collectors.toList()));
            List<String> types = new ArrayList<>();
            types.add("part_of_speech");
            inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
            List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, 50, types);
            return Result.OK(list);
        } else {
            return Result.OK("查询无数据");
        }
    }

    @ApiOperation(value = "学单元词汇表 - 同类词缀", notes = "学单元词汇表 - 同类词缀")
    @GetMapping(value = "/wordsSymbolsData")
    public Result<List<BookWords>> wordsSymbolsData(@RequestParam(name = "key", required = true) String key, HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        inzBookWords.setKey(key);
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, 50, types);
        if (list.isEmpty()) {
            return Result.OK("查询无数据");
        }
        return Result.OK(list);
    }

    /**
     * 学练一体
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "学练一体", notes = "学练一体")
    @GetMapping(value = "/studyPracticeUnit")
    public Result<List<BookWords>> studyPracticeUnit(@RequestParam(name = "bookId", required = false) String bookId,
                                                     @RequestParam(name = "chapterId", required = false) String chapterId,
                                                     @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                                     @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                     @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                     @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                     @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                     @RequestParam(name = "status", required = false, defaultValue = "") String status,
                                                     HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds;
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken()).eq(InzUserLearnLog::getStatus,status)
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else {
            allWordIds = Collections.emptyList();
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        // 获取限制数量的单词
        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);

        // 随机生成 partOfSpeech 数据（一次查询，减少数据库查询）
        Random random = new Random();
        List<WordCollocations> partOfSpeech = wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda()
                .in(WordCollocations::getWordId, allWordIds)
                .eq(WordCollocations::getType, "part_of_speech")
                .orderBy(true, true, WordCollocations::getWordId));

        // 将查询结果转换为 Map，方便根据 wordId 查找
        Map<String, List<WordCollocations>> wordCollocationsMap = partOfSpeech.stream()
                .collect(Collectors.groupingBy(WordCollocations::getWordId));

        list.parallelStream().forEach(item -> {
            List<String> selectedWordIds = new ArrayList<>(allWordIds);
            selectedWordIds.remove(item.getWordId());
            Collections.shuffle(selectedWordIds, random);
            // 安全截取最多3个错误选项
            int endIndex = Math.min(3, selectedWordIds.size());
            selectedWordIds = selectedWordIds.subList(0, endIndex);

            // 添加正确答案，确保至少4个选项
            selectedWordIds.add(item.getWordId());

            // 若选项不足4个，补充至4个（示例：随机选择其他单词）
            Map<String, List<WordCollocations>> wordCollocationsMaps = null;
            if (selectedWordIds.size() < 4) {
                // 获取所有相关单词的列表
                List<BookWords> bookList = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, item.getBookId()));
                List<String> collect = bookList.stream().map(BookWords::getWordId).collect(Collectors.toList());
                List<String> remaining = new ArrayList<>(collect);
                remaining.removeAll(selectedWordIds); // 排除已选
                Collections.shuffle(remaining, random);
                int needed = 4 - selectedWordIds.size();
                selectedWordIds.addAll(remaining.subList(0, Math.min(needed, remaining.size())));

                List<WordCollocations> partOfSpeechs = wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda()
                        .in(WordCollocations::getWordId, selectedWordIds)
                        .eq(WordCollocations::getType, "part_of_speech")
                        .orderBy(true, true, WordCollocations::getWordId));

                // 将查询结果转换为 Map，方便根据 wordId 查找
                wordCollocationsMaps = partOfSpeechs.stream()
                        .collect(Collectors.groupingBy(WordCollocations::getWordId));
            }
            List<WordCollocations> selectedPartOfSpeech;
            if (wordCollocationsMaps != null) {
                Map<String, List<WordCollocations>> finalWordCollocationsMaps = wordCollocationsMaps;
                selectedPartOfSpeech = selectedWordIds.stream()
                        .flatMap(id -> finalWordCollocationsMaps.getOrDefault(id, Collections.emptyList()).stream())
                        .collect(Collectors.toList());
            } else {
                selectedPartOfSpeech = selectedWordIds.stream()
                        .flatMap(id -> wordCollocationsMap.getOrDefault(id, Collections.emptyList()).stream())
                        .collect(Collectors.toList());
            }


            Map<String, List<WordCollocations>> groupedByWordId = selectedPartOfSpeech.stream()
                    .collect(Collectors.groupingBy(WordCollocations::getWordId));
            List<String> combinedTexts = new ArrayList<>();
            selectedWordIds.forEach(wordId -> {
                List<WordCollocations> collocations = groupedByWordId.get(wordId);
                if (collocations != null) {
                    StringBuilder combined = new StringBuilder();
                    collocations.forEach(collocation -> {
                        String english = collocation.getEnglish();
                        String chinese = collocation.getChinese();
                        String formattedType = collocation.getFormattedType();
                        // 拼接英文和中文
                        if (english != null && chinese != null) {
                            combined.append(formattedType).append(". ").append(chinese).append(";");
                        }
                    });
                    if (combined.length() > 0) {
                        combinedTexts.add(combined.toString());
                    }
                    if (wordId.equals(item.getWordId())) {
                        item.setTrueAnswer(combined.toString());
                    }
                }
            });
            while (combinedTexts.size() < 4) {
                combinedTexts.add("No option available");
            }
            Collections.shuffle(combinedTexts, new Random(random.nextLong()));
            item.setWordsOptions(combinedTexts);
        });

        return Result.OK(list);
    }


    /**
     * 英文选义
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "英文选义", notes = "英文选义")
    @GetMapping(value = "/selectEnglishMeaning")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> selectEnglishMeaning(@RequestParam(name = "bookId", required = false) String bookId,
                                                        @RequestParam(name = "chapterId", required = false) String chapterId,
                                                        @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                                        @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                        @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                        @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                        @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                        HttpServletRequest req) {

        BookWords inzBookWords = new BookWords();

        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");

        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);

        // 随机生成 partOfSpeech 数据（一次查询，减少数据库查询）
        Random random = new Random();
        List<WordCollocations> partOfSpeech = wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda()
                .in(WordCollocations::getWordId, allWordIds)
                .eq(WordCollocations::getType, "part_of_speech")
                .orderBy(true, true, WordCollocations::getWordId));

        // 将查询结果转换为 Map，方便根据 wordId 查找
        Map<String, List<WordCollocations>> wordCollocationsMap = partOfSpeech.stream()
                .collect(Collectors.groupingBy(WordCollocations::getWordId));

        List<String> finalAllWordIds = allWordIds;
        list.parallelStream().forEach(item -> {
            List<String> selectedWordIds = new ArrayList<>(finalAllWordIds);
            selectedWordIds.remove(item.getWordId());
            Collections.shuffle(selectedWordIds, random);
            // 安全截取最多3个错误选项
            int endIndex = Math.min(3, selectedWordIds.size());
            selectedWordIds = selectedWordIds.subList(0, endIndex);

            // 添加正确答案，确保至少4个选项
            selectedWordIds.add(item.getWordId());

            // 若选项不足4个，补充至4个（示例：随机选择其他单词）
            Map<String, List<WordCollocations>> wordCollocationsMaps = null;
            if (selectedWordIds.size() < 4) {
                // 获取所有相关单词的列表
                List<BookWords> bookList = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, item.getBookId()));
                List<String> collect = bookList.stream().map(BookWords::getWordId).collect(Collectors.toList());
                List<String> remaining = new ArrayList<>(collect);
                remaining.removeAll(selectedWordIds); // 排除已选
                Collections.shuffle(remaining, random);
                int needed = 4 - selectedWordIds.size();
                selectedWordIds.addAll(remaining.subList(0, Math.min(needed, remaining.size())));

                List<WordCollocations> partOfSpeechs = wordCollocationsService.list(new QueryWrapper<WordCollocations>().lambda()
                        .in(WordCollocations::getWordId, selectedWordIds)
                        .eq(WordCollocations::getType, "part_of_speech")
                        .orderBy(true, true, WordCollocations::getWordId));

                // 将查询结果转换为 Map，方便根据 wordId 查找
                wordCollocationsMaps = partOfSpeechs.stream()
                        .collect(Collectors.groupingBy(WordCollocations::getWordId));
            }
            List<WordCollocations> selectedPartOfSpeech;
            if (wordCollocationsMaps != null) {
                Map<String, List<WordCollocations>> finalWordCollocationsMaps = wordCollocationsMaps;
                selectedPartOfSpeech = selectedWordIds.stream()
                        .flatMap(id -> finalWordCollocationsMaps.getOrDefault(id, Collections.emptyList()).stream())
                        .collect(Collectors.toList());
            } else {
                selectedPartOfSpeech = selectedWordIds.stream()
                        .flatMap(id -> wordCollocationsMap.getOrDefault(id, Collections.emptyList()).stream())
                        .collect(Collectors.toList());
            }

            Map<String, List<WordCollocations>> groupedByWordId = selectedPartOfSpeech.stream()
                    .collect(Collectors.groupingBy(WordCollocations::getWordId));
            List<String> combinedTexts = new ArrayList<>();
            selectedWordIds.forEach(wordId -> {
                List<WordCollocations> collocations = groupedByWordId.get(wordId);
                if (collocations != null) {
                    StringBuilder combined = new StringBuilder();
                    collocations.forEach(collocation -> {
                        String english = collocation.getEnglish();
                        String chinese = collocation.getChinese();
                        String formattedType = collocation.getFormattedType();
                        // 拼接英文和中文
                        if (english != null && chinese != null) {
                            combined.append(formattedType).append(". ").append(chinese).append(";");
                        }
                    });
                    if (combined.length() > 0) {
                        combinedTexts.add(combined.toString());
                    }
                    if (wordId.equals(item.getWordId())) {
                        item.setTrueAnswer(combined.toString());
                    }
                }
            });
            while (combinedTexts.size() < 4) {
                combinedTexts.add("No option available");
            }
            // 随机化选项顺序
            Collections.shuffle(combinedTexts, random);
            item.setWordsOptions(combinedTexts);
        });

        return Result.OK(list);
    }

    /**
     * 中文选义
     *
     * @param req
     * @return
     */
    @ApiOperation(value = "中文选义", notes = "中文选义")
    @GetMapping(value = "/selectChineseMeaning")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> selectChineseMeaning(@RequestParam(name = "bookId", required = false) String bookId,
                                                        @RequestParam(name = "chapterId", required = false) String chapterId,
                                                        @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                                        @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                        @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                        @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                        @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                        HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        List<Words> allWords = wordsFrontService.list(new QueryWrapper<Words>().lambda().in(Words::getId, allWordIds));
        Map<String, Words> wordsMap = allWords.stream().collect(Collectors.toMap(Words::getId, word -> word));
        Random random = new Random();
        List<String> finalAllWordIds = allWordIds;
        list.parallelStream().forEach(item -> {
            List<String> combinedTexts = new ArrayList<>();
            if (item.getWordCollocations() != null) {
                StringBuilder combined = new StringBuilder();
                item.getWordCollocations().forEach(collocation -> {
                    String english = collocation.getEnglish();
                    String chinese = collocation.getChinese();
                    String formattedType = collocation.getFormattedType();
                    // 拼接英文和中文
                    if (english != null && chinese != null) {
                        combined.append(formattedType).append(". ").append(chinese).append(";");
                    }
                });
                if (combined.length() > 0) {
                    combinedTexts.add(combined.toString());
                }
            }
            List<String> selectedWordIds = new ArrayList<>(finalAllWordIds);
            selectedWordIds.remove(item.getWordId());
            Collections.shuffle(selectedWordIds, random);
            // 安全截取最多3个错误选项
            int endIndex = Math.min(3, selectedWordIds.size());
            selectedWordIds = selectedWordIds.subList(0, endIndex);

            // 添加正确答案，确保至少4个选项
            selectedWordIds.add(item.getWordId());

            // 补充选项逻辑
            if (selectedWordIds.size() < 4) {
                // 获取同一本书的其他单词ID
                List<BookWords> bookList = bookWordsService.list(
                        new QueryWrapper<BookWords>()
                                .lambda()
                                .eq(BookWords::getBookId, item.getBookId())
                );
                List<String> bookWordIds = bookList.stream()
                        .map(BookWords::getWordId)
                        .collect(Collectors.toList());

                // 筛选出未被选中的单词ID
                List<String> remaining = new ArrayList<>(bookWordIds);
                remaining.removeAll(selectedWordIds);
                Collections.shuffle(remaining, random);
                int needed = 4 - selectedWordIds.size();
                List<String> supplementaryIds = remaining.subList(0, Math.min(needed, remaining.size()));
                selectedWordIds.addAll(supplementaryIds);

                // 查询补充的单词数据（仅查询缺失的ID）
                List<String> missingIds = supplementaryIds.stream()
                        .filter(id -> !wordsMap.containsKey(id))
                        .collect(Collectors.toList());
                if (!missingIds.isEmpty()) {
                    List<Words> newWords = wordsFrontService.list(
                            new QueryWrapper<Words>()
                                    .lambda()
                                    .in(Words::getId, missingIds)
                    );
                    newWords.forEach(word -> wordsMap.put(word.getId(), word));
                }
            }

            List<Words> selectedWords = selectedWordIds.stream()
                    .map(wordsMap::get)
                    .collect(Collectors.toList());

            List<String> options = selectedWordIds.stream()
                    .map(wordsMap::get)
                    .filter(Objects::nonNull)
                    .map(word -> Optional.ofNullable(word.getWord()).orElse("未知单词"))
                    .collect(Collectors.collectingAndThen(
                            Collectors.toList(),
                            wordsList -> {
                                // 动态补充选项
                                if (wordsList.size() < 4) {
                                    int remain = 4 - wordsList.size();
                                    List<String> dynamicOptions = generateDynamicOptions(remain);
                                    wordsList.addAll(dynamicOptions);
                                }
                                Collections.shuffle(wordsList, random);
                                return wordsList.stream();
                            }))
                    .limit(4)
                    .collect(Collectors.toList());
            item.setWordsOptions(options);
        });
        return Result.OK(list);
    }

    // 动态选项生成方法
    private List<String> generateDynamicOptions(int count) {
        return IntStream.range(0, count)
                .mapToObj(i -> "备选选项" + (char) ('A' + i))
                .collect(Collectors.toList());
    }

    /**
     * 解码
     */
    final static Base64.Decoder decoder = Base64.getDecoder();

    @ApiOperation(value = "朗读测评", notes = "朗读测评")
    @GetMapping(value = "/pronunciationAssessment")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> pronunciationAssessment(@RequestParam(name = "bookId", required = false) String bookId,
                                                           @RequestParam(name = "chapterId", required = false) String chapterId,
                                                           @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                                           @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                           @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                           @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                           @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                           HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        return Result.OK(list);
    }

    @ApiOperation(value = "单词剪纸盒", notes = "单词剪纸盒")
    @GetMapping(value = "/wordPaperBox")
    public Result<Map<String, Object>> wordPaperBox(@RequestParam(name = "bookId") String bookId,
                                                    @RequestParam(name = "chapterId", required = false) String chapterId,
                                                    @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                    @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                    HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        inzBookWords.setBookId(bookId);
        if (StringUtils.isNotBlank(chapterId)) {
            inzBookWords.setChapterId(chapterId);
        }
        
        /*// 查询该书籍的章节数量
        QueryWrapper<WordBookChapter> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WordBookChapter::getBookId, bookId);
        int chapterCount = (int) wordBookChapterService.count(queryWrapper);
        
        // 如果章节数量大于1，检查选择的章节单词数量是否小于5
        if (chapterCount > 1 && StringUtils.isNotBlank(chapterId)) {
            // 查询该章节的单词数量
            QueryWrapper<BookWords> wordCountQuery = new QueryWrapper<>();
            wordCountQuery.lambda().eq(BookWords::getBookId, bookId).eq(BookWords::getChapterId, chapterId);
            int wordCount = (int) bookWordsService.count(wordCountQuery);
            
            // 如果单词数量小于5，提示用户换个章节学习
            if (wordCount < 5) {
                Map<String, Object> result = new HashMap<>();
                result.put("code", 201); // 自定义状态码
                result.put("message", "当前章节单词数量较少，换个章节学习吧!");
                result.put("wordCount", wordCount);
                result.put("chapterCount", chapterCount);
                return Result.OK(result);
            }
        }*/

        inzBookWords.setSortBy("rand");
        // 计算分页的起始位置
        Integer offset = (pageNo - 1) * pageSize;
        // 获取单词列表，限制为分页大小
//        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
        List<BookWords> list = bookWordsService.listWithWordsOffset(inzBookWords, offset, pageSize);
        // 获取总记录数
        int totalCount = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda()
                .eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
        // 计算总页数
        int totalPages = (totalCount + pageSize - 1) / pageSize; // 向上取整
        // 创建返回结果的Map
        Map<String, Object> result = new HashMap<>();
        result.put("list", list);        // 当前页的数据
        result.put("totalCount", totalCount); // 总记录数
        result.put("totalPages", totalPages); // 总页数
        result.put("pageNo", pageNo);    // 当前页码
        result.put("pageSize", pageSize);  // 每页大小
        return Result.OK(result);
    }


    @ApiOperation(value = "自然拼读", notes = "自然拼读")
    @GetMapping(value = "/phonicsTest")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String", defaultValue = "6012fcfd18a5fe51a8f2f5b5fc61e9b5"),
            @ApiImplicitParam(name = "wordId", value = "单词id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> phonicsTest(@RequestParam(name = "bookId", required = false) String bookId,
                                               @RequestParam(name = "chapterId", required = false) String chapterId,
                                               @RequestParam(name = "wordId", required = false) String wordId,
                                               @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                               @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                               @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                               @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                               @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                               HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds = Collections.emptyList();
        if (StringUtils.isNotBlank(wordId)) {
            inzBookWords.setWordIds(Collections.singletonList(wordId));
        } else {
            if (type == 1) {
                inzBookWords.setBookId(bookId);
                inzBookWords.setChapterId(chapterId);
                if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                    inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                    if (limit == 0) {
                        limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                    }
                }
                // 获取所有相关单词的列表
                List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
                allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            } else if (type == 2) {
                List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                        .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
                if (pageList.isEmpty()) {
                    return Result.OK("当前日期您还没有学习内容噢~~~");
                }
                allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            } else if (type == 3) {
                List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
                if (list.isEmpty()) {
                    return Result.OK("您还没有加入任何错题噢~~~");
                }
                allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            } else if (type == 4) {
                if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                    return Result.OK("参数错误");
                }
                List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                        .eq(InzUserCollectionWords::getUnitId, unitId));
                if (list.isEmpty()) {
                    return Result.OK("您还没有加入任何收藏本噢~~~");
                }
                allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
                inzBookWords.setWordIds(allWordIds);
            }
        }


        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("speak_naturl_phonics");
        types.add("natural_phonics");
        types.add("root_particles");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        return Result.OK(list);
    }

    @ApiOperation(value = "单词对对碰", notes = "单词对对碰")
    @GetMapping(value = "/wordMatchingGame")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "pageNo", value = "", required = false, defaultValue = "1", dataType = "String"),
            @ApiImplicitParam(name = "pageSize", value = "", required = false, defaultValue = "5", dataType = "String"),
    })
    public Result<Map<String, Object>> wordMatchingGame(@RequestParam(name = "bookId", required = false) String bookId,
                                                        @RequestParam(name = "chapterId", required = false) String chapterId,
                                                        @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                        @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                        @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                        @RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize,
                                                        @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                        @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                        HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();

        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        int totalCount = allWordIds.size();
        int totalPages = (totalCount + pageSize - 1) / pageSize;
        // 计算分页偏移量
        int offset = (pageNo - 1) * pageSize;
        if (offset >= totalCount) {
            offset = (totalPages - 1) * pageSize;  // 最后一页的偏移量
            pageNo = totalPages;  // 设置为最后一页
        }
        // 获取单词列表
        List<BookWords> list = bookWordsService.listWithWordsOffset(inzBookWords, offset, pageSize);

        Map<String, Object> result = new HashMap<>();

        // 构造分页信息
        HashMap<String, Object> pageInfo = new HashMap<>();
        pageInfo.put("totalCount", totalCount); // 总记录数
        pageInfo.put("totalPages", totalPages); // 总页数
        pageInfo.put("pageNo", pageNo);         // 当前页码
        pageInfo.put("pageSize", pageSize);     // 每页大小

        // 存放左侧和右侧选项的 List
        List<Map<String, Object>> leftOptions = new ArrayList<>();
        List<Map<String, Object>> rightOptions = new ArrayList<>();

        // 填充左侧和右侧选项
        list.forEach(obj -> {
            ArrayList<String> strings = new ArrayList<>();
            obj.getWordCollocations().forEach(collocation -> {
                String english = collocation.getEnglish();
                String chinese = collocation.getChinese();
                StringBuilder formattedType = new StringBuilder(collocation.getFormattedType());
                // 拼接英文和中文
                if (english != null && chinese != null) {
                    // 这里是英文和中文的拼接格式
                    formattedType.append(". ").append(chinese).append(";");
                    // 将拼接后的结果加入到列表
                    strings.add(formattedType.toString());
                }
            });
            // 左侧选项
            HashMap<String, Object> leftOption = new HashMap<>();
            leftOption.put("wordId", obj.getWordId());
            leftOption.put("option", obj.getWord());
            leftOptions.add(leftOption);  // 将选项加入 List

            // 右侧选项
            HashMap<String, Object> rightOption = new HashMap<>();
            rightOption.put("wordId", obj.getWordId());
            rightOption.put("option", String.join(" ", strings));
            rightOptions.add(rightOption); // 将选项加入 List
        });
        Collections.shuffle(leftOptions);
        Collections.shuffle(rightOptions);
        // 将分页信息和选项加入结果
        result.put("pageInfo", pageInfo);
        result.put("leftOption", leftOptions);  // 左侧选项 List
        result.put("rightOption", rightOptions); // 右侧选项 List
        return Result.OK(result);
    }

    /**
     * 创建空结果，带有提示消息
     */
    private Map<String, Object> createEmptyResult(String message) {
        Map<String, Object> result = new HashMap<>();
        result.put("message", message);

        HashMap<String, Object> pageInfo = new HashMap<>();
        pageInfo.put("totalCount", 0);
        pageInfo.put("totalPages", 0);
        pageInfo.put("pageNo", 1);
        pageInfo.put("pageSize", 5);

        result.put("pageInfo", pageInfo);
        result.put("leftOption", Collections.emptyList());
        result.put("rightOption", Collections.emptyList());
        return result;
    }

    @ApiOperation(value = "例句填空", notes = "例句填空")
    @GetMapping(value = "/sentenceFillIn")
    public Result<List<BookWords>> sentenceFillIn(BookWords inzBookWords, @RequestParam(name = "limit", required = true) int limit, HttpServletRequest req) {
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        inzBookWords.setCreateBy(CommonUtils.getUserIdByToken());
        List<BookWords> allWords = bookWordsService.listWithWordsLimit(inzBookWords, 0, types);
        List<BookWords> selectedWords;
        if (allWords.size() > limit) {
            Collections.shuffle(allWords); // 随机打乱顺序
            selectedWords = allWords.subList(0, limit); // 取前limit个
        } else {
            selectedWords = allWords;
        }

        // 获取所有选中单词的ID
        List<String> wordIds = selectedWords.stream().map(BookWords::getWordId).collect(Collectors.toList());

        // 从数据库获取例句，而不是调用API
        List<FillInEntity> fillInEntities = wordSentenceService.getRandomFillInSentences(wordIds, 1);

        // 为每个单词设置对应的填空例句
        selectedWords.forEach(item -> {
            item.setFillInEntity(fillInEntities.stream()
                    .filter(fillInEntity -> fillInEntity.getTrueAnswer().equals(item.getWord()))
                    .findFirst()
                    .orElse(null));
        });

        return Result.OK(selectedWords);
    }

    @ApiOperation(value = "串词生文", notes = "串词生文")
    @PostMapping(value = "/sentenceConstruction")
    public Result<Map<String, Object>> sentenceConstruction(@RequestBody BookWords inzBookWords, HttpServletRequest req) {
        Map<String, Object> stringObjectMap = new HashMap<>();
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");

        List<WordArticle> articles = wordArticleService.getRandomArticlesForChapterWithLimit(inzBookWords.getBookId(), inzBookWords.getChapterId(), 1);
        if (articles == null || articles.isEmpty()) {
            return Result.error("未找到相关短文");
        }

        List<String> wordIds = Arrays.asList(articles.get(0).getWordIds().split(","));

        // 查询特定单词的详细信息
        BookWords bookWords = new BookWords();
        bookWords.setBookId(inzBookWords.getBookId());
        bookWords.setChapterId(inzBookWords.getChapterId());
        bookWords.setWordIds(wordIds); // 设置从文章中提取的单词ID
        bookWords.setCreateBy(CommonUtils.getUserIdByToken());

        List<BookWords> wordsList = bookWordsService.listWithWordsLimit(bookWords, 0, types);

        // 将 WordArticle 转换为 ConstructionEntity 以保持前端兼容性
        List<ConstructionEntity> constructionEntities = wordArticleService.convertToConstructionEntities(articles);
        stringObjectMap.put("construction", constructionEntities);
        stringObjectMap.put("words", wordsList);

        return Result.OK(stringObjectMap);
    }

    @ApiOperation(value = "拆分拼写", notes = "拆分拼写")
    @GetMapping(value = "/wordSpellingSplit")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String", defaultValue = "6012fcfd18a5fe51a8f2f5b5fc61e9b5"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> wordSpellingSplit(@RequestParam(name = "bookId", required = false) String bookId,
                                                     @RequestParam(name = "chapterId", required = false) String chapterId,
                                                     @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                                     @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                     @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                     @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                     @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                     HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");

        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        return Result.OK(list);
    }

    @ApiOperation(value = "单词听写", notes = "单词听写")
    @GetMapping(value = "/wordDictation")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> wordDictation(@RequestParam(name = "bookId", required = false) String bookId,
                                                 @RequestParam(name = "chapterId", required = false) String chapterId,
                                                 @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                                 @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                                 @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                                 @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                                 @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                                 HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        return Result.OK(list);
    }

    @ApiOperation(value = "单词随身听", notes = "单词随身听")
    @GetMapping(value = "/wordOnTheGo")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "bookId", value = "图书id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "chapterId", value = "章节id", required = false, dataType = "String"),
            @ApiImplicitParam(name = "limit", value = "限制数量 可不传", required = false, defaultValue = "0", dataType = "int"),
            @ApiImplicitParam(name = "type", value = "类型 1首页（默认可不传）,2抗遗忘复习,3错题本 4收藏本", required = false, defaultValue = "1", dataType = "int"),
            @ApiImplicitParam(name = "date", value = "type为2时必填 日期格式 Y-m-d", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "collectionId", value = "type为4时必填 收藏本id", required = false, defaultValue = "", dataType = "String"),
            @ApiImplicitParam(name = "unitId", value = "type为4时必填 收藏单元id", required = false, defaultValue = "", dataType = "String")
    })
    public Result<List<BookWords>> wordOnTheGo(@RequestParam(name = "bookId", required = false) String bookId,
                                               @RequestParam(name = "chapterId", required = false) String chapterId,
                                               @RequestParam(name = "limit", required = false, defaultValue = "0") int limit,
                                               @RequestParam(name = "type", required = false, defaultValue = "1") int type,
                                               @RequestParam(name = "date", required = false, defaultValue = "") String date,
                                               @RequestParam(name = "collectionId", required = false, defaultValue = "") String collectionId,
                                               @RequestParam(name = "unitId", required = false, defaultValue = "") String unitId,
                                               HttpServletRequest req) {
        BookWords inzBookWords = new BookWords();
        List<String> allWordIds = Collections.emptyList();
        if (type == 1) {
            inzBookWords.setBookId(bookId);
            inzBookWords.setChapterId(chapterId);
            if (StringUtils.isBlank(inzBookWords.getChapterId())) {
                inzBookWords.setChapterId(getChapterId(inzBookWords.getBookId()));
                if (limit == 0) {
                    limit = (int) bookWordsService.count(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(BookWords::getChapterId, inzBookWords.getChapterId()));
                }
            }
            // 获取所有相关单词的列表
            List<BookWords> listAll = bookWordsService.list(new QueryWrapper<BookWords>().lambda().eq(BookWords::getBookId, inzBookWords.getBookId()).eq(StringUtils.isNotBlank(inzBookWords.getChapterId()), BookWords::getChapterId, inzBookWords.getChapterId()));
            allWordIds = listAll.stream().map(BookWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 2) {
            List<InzUserLearnLog> pageList = inzUserLearnLogService.list(new QueryWrapper<InzUserLearnLog>().lambda().eq(InzUserLearnLog::getCreateBy, CommonUtils.getUserIdByToken())
                    .likeRight(StringUtils.isNotBlank(date), InzUserLearnLog::getCreateTime, date));
            if (pageList.isEmpty()) {
                return Result.OK("当前日期您还没有学习内容噢~~~");
            }
            allWordIds = pageList.stream().map(InzUserLearnLog::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 3) {
            List<InzUserWrongBook> list = inzUserWrongBookService.list(new QueryWrapper<InzUserWrongBook>().lambda().eq(InzUserWrongBook::getCreateBy, CommonUtils.getUserIdByToken()));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何错题噢~~~");
            }
            allWordIds = list.stream().map(InzUserWrongBook::getWordsId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        } else if (type == 4) {
            if (StringUtils.isBlank(collectionId) || StringUtils.isBlank(unitId)) {
                return Result.OK("参数错误");
            }
            List<InzUserCollectionWords> list = inzUserCollectionWordsService.list(new QueryWrapper<InzUserCollectionWords>().lambda().eq(InzUserCollectionWords::getCollectionId, collectionId)
                    .eq(InzUserCollectionWords::getUnitId, unitId));
            if (list.isEmpty()) {
                return Result.OK("您还没有加入任何收藏本噢~~~");
            }
            allWordIds = list.stream().map(InzUserCollectionWords::getWordId).collect(Collectors.toList());
            inzBookWords.setWordIds(allWordIds);
        }
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("root_particles");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        List<BookWords> list = bookWordsService.listWithWordsLimit(inzBookWords, limit, types);
        return Result.OK(list);
    }

    @ApiOperation(value = "获取语音测评结果", notes = "获取语音测评结果")
    @PostMapping(value = "/test")
    public Result<Object> test(
            @RequestParam("file") MultipartFile file,
            @RequestParam("word") String word,
            @RequestParam("category") String category,
            HttpServletRequest req) throws IOException, SignatureException, InterruptedException, URISyntaxException {

        // ================= 文件存储准备 =================
        Path uploadDir = Paths.get(uploadPath + "/temp/test/");
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }

        String originalFilename = file.getOriginalFilename();
        String baseName = FilenameUtils.getBaseName(originalFilename);
        String extension = FilenameUtils.getExtension(originalFilename);
        long timeMillis = System.currentTimeMillis();

        // 生成唯一文件名
        String uniqueFileName = baseName + "_" + timeMillis + "." + extension;
        Path inputPath = uploadDir.resolve(uniqueFileName);

        // 保存原始文件
        file.transferTo(inputPath);
        if (!Files.exists(inputPath)) {
            return Result.error(500, "文件上传失败");
        }

        // ================= 音频格式转换 =================
        String wavFileName = baseName + "_" + timeMillis + "_converted.wav";
        Path wavPath = uploadDir.resolve(wavFileName);

        // 构建FFmpeg命令
        List<String> ffmpegCommand = Arrays.asList(
                "ffmpeg",
                "-i", inputPath.toString(),
                "-ar", "16000",        // 采样率16kHz
                "-ac", "1",            // 单声道
                "-acodec", "pcm_s16le",// 16位PCM编码
                "-y",                  // 覆盖输出文件
                wavPath.toString()
        );

        // 执行转换命令
        try {
            ProcessBuilder processBuilder = new ProcessBuilder(ffmpegCommand);
            processBuilder.redirectErrorStream(true);

            Process process = processBuilder.start();

            // 实时读取FFmpeg输出
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(process.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    System.out.println("[FFmpeg] " + line);
                }
            }

            int exitCode = process.waitFor();
            if (exitCode != 0) {
                throw new RuntimeException("FFmpeg转换失败，错误码: " + exitCode);
            }

            // 验证转换结果
            if (!Files.exists(wavPath) || Files.size(wavPath) == 0) {
                throw new IOException("WAV文件生成失败");
            }

        } catch (IOException | InterruptedException e) {
            Files.deleteIfExists(inputPath);
            Files.deleteIfExists(wavPath);
            return Result.error(500, "音频转换失败: " + e.getMessage());
        }

        // ================= 讯飞接口调用 =================
        String appId = redisUtil.get("SuperWords:config:XUNFEI_APPID").toString();
        String apiKey = redisUtil.get("SuperWords:config:XUNFEI_APPKEY").toString();
        String apiSecret = redisUtil.get("SuperWords:config:XUNFEI_APPSECRET").toString();

        // 初始化讯飞客户端
        IseClient client = new IseClient.Builder()
                .signature(appId, apiKey, apiSecret)
                .addSub("ise")
                .addEnt("en_vip")
                .addCategory(category)
                .addTte("gbk")
                .addText("[word]\n" + word)
                .addRst("entirety")
                .addIseUnite("0")
                .addPlev("0")
                .build();

        // 异步处理控制
        final CountDownLatch latch = new CountDownLatch(1);
        AtomicReference<String> jsonResponseRef = new AtomicReference<>();

        try (InputStream wavInputStream = Files.newInputStream(wavPath)) {
            client.send(wavInputStream, new AbstractIseWebSocketListener() {
                @Override
                public void onSuccess(WebSocket webSocket, IseResponseData iseResponseData) {
                    try {
                        String xmlResponse = new String(
                                Base64.getDecoder().decode(iseResponseData.getData().getData()),
                                StandardCharsets.UTF_8
                        );

                        // XML转JSON
                        XmlMapper xmlMapper = new XmlMapper();
                        JsonNode xmlNode = xmlMapper.readTree(xmlResponse);
                        ObjectMapper jsonMapper = new ObjectMapper();
                        String jsonResponse = jsonMapper.writeValueAsString(xmlNode);

                        jsonResponseRef.set(jsonResponse);
                    } catch (Exception e) {
                        jsonResponseRef.set("{\"error\":\"结果解析失败\"}");
                    } finally {
                        latch.countDown();
                    }
                }

                @Override
                public void onFail(WebSocket webSocket, Throwable t, Response response) {
                    jsonResponseRef.set("{\"error\":\"接口调用失败\"}");
                    latch.countDown();
                }
            });

            // 等待结果（最多30秒）
            if (!latch.await(30, TimeUnit.SECONDS)) {
                throw new RuntimeException("接口响应超时");
            }

        } finally {
            // 清理临时文件
            Files.deleteIfExists(inputPath);
            Files.deleteIfExists(wavPath);
        }

        // 处理最终结果
        ObjectMapper mapper = new ObjectMapper();
        JsonNode resultNode = mapper.readTree(jsonResponseRef.get());
        return Result.OK("识别成功", resultNode);
    }

    @Value("${jeecg.path.upload}")
    private String uploadPath;

    @ApiOperation(value = "转换音频文件为mp3", notes = "转换音频文件为mp3")
    @PostMapping(value = "/convertToMp3")
    public Result<Map<String, String>> convertToMp3(@RequestParam("file") MultipartFile file,
                                                    HttpServletRequest req) throws IOException, InterruptedException {
        try {
            Path uploadDir = Paths.get(uploadPath + "/temp/");
            if (!Files.exists(uploadDir)) {
                Files.createDirectories(uploadDir);
            }

            String originalFilename = file.getOriginalFilename();
            String baseName = FilenameUtils.getBaseName(originalFilename);
            String extension = FilenameUtils.getExtension(originalFilename);
            long timeMillis = System.currentTimeMillis();
            String uniqueFileName = baseName + "_" + timeMillis + "." + extension;

            Path inputPath = uploadDir.resolve(uniqueFileName);
            file.transferTo(inputPath);

            // 检查文件是否成功上传
            if (!Files.exists(inputPath)) {
                return Result.error(500, "文件上传失败");
            }

            // 设置文件权限为可读、可写
            Set<PosixFilePermission> perms = PosixFilePermissions.fromString("rwxr-xr-x");
            Files.setPosixFilePermissions(inputPath, perms);

            String outputFileName = baseName + "_" + timeMillis + "_converted.mp3";
            Path outputPath = uploadDir.resolve(outputFileName);

            // FFmpeg命令字符串
            String inputFilePath = inputPath.toUri().getPath();  // 确保路径没有问题
            String outputFilePath = outputPath.toUri().getPath();

            String ffmpegCommand = String.format("ffmpeg -i %s %s", inputFilePath, outputFilePath);
            System.out.println(ffmpegCommand);

            // 执行FFmpeg命令并捕获错误输出
            ProcessBuilder processBuilder = new ProcessBuilder(ffmpegCommand.split(" "));
            processBuilder.inheritIO();  // 继承输入输出流，显示FFmpeg执行过程
            processBuilder.redirectErrorStream(true);  // 合并标准输出和标准错误流

            Process process = processBuilder.start();

            // 读取FFmpeg的输出
            BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
            String line;
            while ((line = reader.readLine()) != null) {
                System.out.println(line);  // 打印FFmpeg执行的输出
            }
            int exitCode = process.waitFor();
            if (exitCode == 0) {
                // 删除原文件
                Files.deleteIfExists(inputPath);
                HashMap<String, String> result = new HashMap<>();
                result.put("url", redisUtil.get("SuperWords:config:DOMAIN") + "/super-words/sys/common/static/temp/" + outputFileName);
                return Result.ok(result);
            } else {
                return Result.error(500, "音频转换失败，FFmpeg返回错误");
            }
        } catch (IOException e) {
            return Result.error(500, "文件处理错误: " + e.getMessage());
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return Result.error(500, "音频转换过程被中断");
        } catch (Exception e) {
            return Result.error(500, "系统错误: " + e.getMessage());
        }
    }


    @ApiOperation(value = "删除音频文件", notes = "根据文件名删除音频文件")
    @DeleteMapping(value = "/deleteFile")
    public Result<String> deleteFile(@RequestParam("fileName") String fileName) {
        try {
            // 构建文件路径
            Path filePath = Paths.get(uploadPath + "/temp/" + fileName);

            // 检查文件是否存在
            if (Files.exists(filePath)) {
                // 删除文件
                Files.delete(filePath);
                return Result.ok("文件删除成功");
            } else {
                return Result.error(404, "文件未找到");
            }
        } catch (IOException e) {
            return Result.error(500, "文件删除失败: " + e.getMessage());
        } catch (Exception e) {
            return Result.error(500, "系统错误: " + e.getMessage());
        }
    }


    @ApiOperation(value = "执行语音评测", notes = "调用有道API进行语音评测")
    @PostMapping(value = "/iseTest")
    public Result<Object> iseTest(
            @RequestParam("file") MultipartFile file,
            @RequestParam("word") String word,
            @RequestParam("langType") String langType,
            HttpServletRequest req) throws Exception {

        // ================= 文件存储准备 =================
        Path uploadDir = Paths.get(uploadPath + "/temp/youdao-ise/");
        if (!Files.exists(uploadDir)) {
            Files.createDirectories(uploadDir);
        }

        String originalFilename = file.getOriginalFilename();
        String baseName = FilenameUtils.getBaseName(originalFilename);
        String extension = FilenameUtils.getExtension(originalFilename);
        long timeMillis = System.currentTimeMillis();

        // 生成唯一文件名
        String uniqueFileName = baseName + "_" + timeMillis + "." + extension;
        Path inputPath = uploadDir.resolve(uniqueFileName);
        Path wavPath = null;

        try {
            // ================= 保存原始文件 =================
            file.transferTo(inputPath);
            if (!Files.exists(inputPath)) {
                return Result.error(500, "文件上传失败");
            }

            // ================= 新增音频格式转换 =================
            String wavFileName = baseName + "_" + timeMillis + "_converted.wav";
            wavPath = uploadDir.resolve(wavFileName);

            // 构建增强版FFmpeg命令
            List<String> ffmpegCommand = new ArrayList<>(Arrays.asList(
                    "ffmpeg",
                    "-y",
                    "-loglevel", "error"
            ));

            // 处理特殊格式（如PCM）
            if ("pcm".equalsIgnoreCase(extension)) {
                ffmpegCommand.addAll(Arrays.asList(
                        "-f", "s16le",
                        "-ar", "16000",
                        "-ac", "1",
                        "-i", inputPath.toString()
                ));
            } else {
                ffmpegCommand.addAll(Arrays.asList(
                        "-i", inputPath.toString(),
                        "-vn",
                        "-ar", "16000",
                        "-ac", "1"
                ));
            }

            // 输出参数
            ffmpegCommand.addAll(Arrays.asList(
                    "-acodec", "pcm_s16le",
                    "-f", "wav",
                    wavPath.toString()
            ));

            // 执行转换命令（带超时控制）
            Process process = new ProcessBuilder(ffmpegCommand)
                    .redirectErrorStream(true)
                    .start();

            // 异步读取错误日志
            new Thread(() -> {
                try (BufferedReader reader = new BufferedReader(
                        new InputStreamReader(process.getInputStream()))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        log.error("[FFmpeg] 文件: {} | 错误: {}", inputPath.getFileName(), line);
                    }
                } catch (IOException e) {
                    log.error("FFmpeg日志读取失败", e);
                }
            }).start();

            // 设置30秒超时
            if (!process.waitFor(30, TimeUnit.SECONDS)) {
                process.destroyForcibly();
                throw new RuntimeException("音频转换超时");
            }

            // 验证转换结果
            if (!Files.exists(wavPath) || Files.size(wavPath) == 0) {
                throw new IOException("WAV文件生成失败");
            }

            // ================= 参数准备 =================
            String appKey = "3ad3fe10c6c99102";
            String appSecret = "YJFphV8fKwB55SVMOe4D1Tlg9q59eSqX";

            // 读取转换后的WAV文件
            String q = Base64.getEncoder().encodeToString(Files.readAllBytes(wavPath));

            Map<String, String> params = new HashMap<>();
            params.put("appKey", appKey);
            params.put("q", q);
            params.put("format", "wav");
            params.put("rate", "16000");
            params.put("channel", "1");
            params.put("docType", "json");
            params.put("type", "1");
            String salt = UUID.randomUUID().toString();
            params.put("salt", salt);
            params.put("langType", "en");
            params.put("text", word);
            String curtime = String.valueOf(System.currentTimeMillis() / 1000);
            params.put("curtime", curtime);

            // 生成签名
            String signStr = appKey + truncate(q) + salt + curtime + appSecret;
            String sign = getSha256(signStr);
            params.put("sign", sign);
            params.put("signType", "v2");

            // ================= 接口调用 =================
            String resultJson = doYoudaoRequest("https://openapi.youdao.com/iseapi", params);

            // 解析结果
            ObjectMapper mapper = new ObjectMapper();
            JsonNode resultNode = mapper.readTree(resultJson);
            return Result.OK("评测成功", resultNode);

        } catch (Exception e) {
            log.error("语音评测流程异常", e);
            return Result.error(500, "处理失败: " + e.getMessage());
        } finally {
            // 清理临时文件
            Files.deleteIfExists(inputPath);
            if (wavPath != null) {
                Files.deleteIfExists(wavPath);
            }
        }
    }

    // SHA256签名方法
    private String getSha256(String string) throws NoSuchAlgorithmException {
        MessageDigest md = MessageDigest.getInstance("SHA-256");
        byte[] hash = md.digest(string.getBytes(StandardCharsets.UTF_8));
        return bytesToHex(hash);
    }

    // 字节转十六进制
    private String bytesToHex(byte[] bytes) {
        StringBuilder hexString = new StringBuilder();
        for (byte b : bytes) {
            String hex = Integer.toHexString(0xff & b);
            if (hex.length() == 1) hexString.append('0');
            hexString.append(hex);
        }
        return hexString.toString();
    }

    // 截取字符串
    private String truncate(String q) {
        if (q == null) return null;
        int len = q.length();
        return len <= 20 ? q : (q.substring(0, 10) + len + q.substring(len - 10));
    }

    // 封装HTTP请求
    private String doYoudaoRequest(String url, Map<String, String> params) throws Exception {
        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpPost httpPost = new HttpPost(url);
            List<NameValuePair> formParams = new ArrayList<>();
            params.forEach((k, v) -> formParams.add(new BasicNameValuePair(k, v)));

            httpPost.setEntity(new UrlEncodedFormEntity(formParams, StandardCharsets.UTF_8));

            try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
                return EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
            }
        }
    }

    private String getChapterId(String bookId) {
        // 查询该书籍的所有章节
        List<WordBookChapter> chapterList = wordBookChapterService.list(new QueryWrapper<WordBookChapter>().lambda()
                .eq(WordBookChapter::getBookId, bookId)
                .orderBy(true, true, WordBookChapter::getSort));

        // 如果没有章节，返回null
        if (chapterList == null || chapterList.isEmpty()) {
            return null;
        }

        // 默认使用sort=1的章节，如果没有sort=1的章节，则使用第一个章节
        for (WordBookChapter chapter : chapterList) {
            if (chapter.getSort() != null && chapter.getSort() == 1) {
                return chapter.getId();
            }
        }

        // 如果没有找到sort=1的章节，则使用第一个章节
        return chapterList.get(0).getId();
    }

    @PostMapping("/genderPdf")
    public void generatePdf(@RequestBody GeneratePdfDto generatePdfDto, HttpServletResponse response) {
        Document document = new Document();
        try {
            // 加载字体文件（保持不变）
            URL fontUrl = getClass().getClassLoader().getResource("font/NotoSansSC-VariableFont_wght.ttf");
            if (fontUrl == null) {
                throw new Exception("字体文件未找到！");
            }

            File tempFontFile = new File("tempFont.ttf");
            if (!tempFontFile.exists()) {
                try (InputStream in = fontUrl.openStream(); FileOutputStream out = new FileOutputStream(tempFontFile)) {
                    byte[] buffer = new byte[4096];
                    int bytesRead;
                    while ((bytesRead = in.read(buffer)) != -1) {
                        out.write(buffer, 0, bytesRead);
                    }
                } catch (IOException e) {
                    throw new Exception("字体文件复制失败: " + e.getMessage());
                }
            }

            // 设置响应头（保持不变）
            response.setContentType("application/pdf");
            String encodedFilename = URLEncoder.encode("学习材料.pdf", "UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + encodedFilename);

            // 创建PDF文档
            PdfWriter writer = PdfWriter.getInstance(document, response.getOutputStream());
            document.open();

            // 创建字体（保持不变）
            BaseFont baseFont = BaseFont.createFont(tempFontFile.getAbsolutePath(), BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            Font titleFont = new Font(baseFont, 16, Font.BOLD);
            Font contentFont = new Font(baseFont, 12, Font.BOLD);
            Font questionFont = new Font(baseFont, 14, Font.BOLD);
            Font optionFont = new Font(baseFont, 12, Font.BOLD);
            Font answerFont = new Font(baseFont, 12, Font.BOLD);

            // 页面尺寸和边距设置
            float marginLeft = 36;
            float marginRight = 36;
            float marginTop = 36;
            float marginBottom = 36;
            float pageWidth = document.right() - document.left();
            float pageHeight = document.top() - document.bottom();
            float columnWidth = (pageWidth - marginLeft - marginRight) / 2;
            float columnHeight = pageHeight - marginTop - marginBottom;
            float gutter = 10; // 栏间距

            // 创建ColumnText实例
            ColumnText columnText = new ColumnText(writer.getDirectContent());

            // 设置初始栏（左栏）
            float leftX = marginLeft;
            float leftY = document.top() - marginTop;
            columnText.setSimpleColumn(leftX, marginBottom,
                    leftX + columnWidth, leftY);

            // 遍历construction数组
            for (ConstructionItem item : generatePdfDto.getConstruction()) {
                // 添加所有内容元素
                addContentElements(columnText, item, titleFont, contentFont,
                        questionFont, optionFont, answerFont);
            }

            // 处理分栏和分页
            int status;
            boolean isLeftColumn = true;

            do {
                status = columnText.go();

                if (ColumnText.hasMoreText(status)) {
                    if (isLeftColumn) {
                        // 切换到右栏
                        float rightX = marginLeft + columnWidth + gutter;
                        columnText.setSimpleColumn(rightX, marginBottom,
                                rightX + columnWidth, document.top() - marginTop);
                        isLeftColumn = false;
                    } else {
                        // 新页面，回到左栏
                        document.newPage();
                        columnText.setSimpleColumn(marginLeft, marginBottom,
                                marginLeft + columnWidth, document.top() - marginTop);
                        isLeftColumn = true;
                    }
                }
            } while (ColumnText.hasMoreText(status));

        } catch (Exception e) {
            e.printStackTrace();
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        } finally {
            document.close();
            // 删除临时字体文件
            File tempFontFile = new File("tempFont.ttf");
            if (tempFontFile.exists()) {
                tempFontFile.delete();
            }
        }
    }

    private void addContentElements(ColumnText columnText, ConstructionItem item,
                                    Font titleFont, Font contentFont,
                                    Font questionFont, Font optionFont,
                                    Font answerFont) throws DocumentException {
        // 设置全局行间距因子
        float lineSpacing = 1.5f; // 1.5倍行距

        // 添加文章标题
        Paragraph articleTitle = new Paragraph("文章内容", titleFont);
        articleTitle.setAlignment(Element.ALIGN_CENTER);
        articleTitle.setSpacingAfter(15f); // 增加段后间距
        articleTitle.setLeading(articleTitle.getFont().getSize() * lineSpacing);
        columnText.addElement(articleTitle);

        // 添加英文文章
        Paragraph englishArticle = new Paragraph(item.getArticle(), contentFont);
        englishArticle.setLeading(contentFont.getSize() * lineSpacing);
        englishArticle.setSpacingAfter(15f); // 增加段后间距
        columnText.addElement(englishArticle);

        // 添加中文翻译
        Paragraph chineseArticle = new Paragraph(item.getChineseMeaning(), contentFont);
        chineseArticle.setLeading(contentFont.getSize() * lineSpacing);
        chineseArticle.setSpacingAfter(25f); // 增加段后间距
        columnText.addElement(chineseArticle);

        // 添加郑重提示
        Paragraph note = new Paragraph("郑重提示: 认真看完全文后再回答问题。", contentFont);
        note.setLeading(contentFont.getSize() * lineSpacing);
        note.setSpacingAfter(20f); // 增加段后间距
        columnText.addElement(note);

        // 添加问题列表
        for (Question question : item.getQuestions()) {
            // 添加问题
            Paragraph questionParagraph = new Paragraph(question.getQuestion() + "（ ）", questionFont);
            questionParagraph.setLeading(questionFont.getSize() * lineSpacing);
            questionParagraph.setSpacingAfter(10f); // 增加段后间距
            columnText.addElement(questionParagraph);

            // 添加中文问题含义（可选）
            if (question.getQuestionChineseMeaning() != null && !question.getQuestionChineseMeaning().isEmpty()) {
                Paragraph chQuestion = new Paragraph(question.getQuestionChineseMeaning(), contentFont);
                chQuestion.setLeading(contentFont.getSize() * lineSpacing);
                chQuestion.setSpacingAfter(10f); // 增加段后间距
                columnText.addElement(chQuestion);
            }

            // 添加选项
            char optionChar = 'A';
            for (Option option : question.getOptions()) {
                Paragraph optionParagraph = new Paragraph();
                optionParagraph.setLeading(contentFont.getSize() * lineSpacing);
                optionParagraph.add(new Chunk(optionChar + ". ", optionFont));
                optionParagraph.add(new Chunk(option.getEn() + " (" + option.getCh() + ")", optionFont));
                optionParagraph.setSpacingAfter(8f); // 增加选项间距
                columnText.addElement(optionParagraph);
                optionChar++;
            }
        }
    }

    @ApiOperation(value = "保存提示词,获取hash", notes = "保存提示词,获取hash")
    @PostMapping(value = "/savePrompt")
    public Result<?> savePrompt(@RequestBody SavePromptDto savePromptDto) {
        HashMap<String, String> results = new HashMap<>();
        if (redisUtil.get("SuperWords:config:hash") != null) {
            Map<String, String> result = ThirdRequestUtils.savePrompt(savePromptDto.getPrompt(), redisUtil);
            results.put("hash", result.get("hash"));
            return Result.OK("获取提示词成功", results);
        }
        return Result.OK("保存提示词成功", results);
    }

    /**
     * 短语、句子生成音频
     */
    @ApiOperation(value = "短语、句子生成音频", notes = "短语、句子生成音频")
    @PostMapping(value = "/generateAudio")
    public Result<?> generateAudio(@Valid @RequestBody GenerateAudioDto generateAudioDto) {
        String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
        String thirdToken = ThirdRequestUtils.getThirdToken(ThirdRequestConstant.TOKEN_USERNAME, ThirdRequestConstant.TOKEN_PASSWORD, redisUtil);
        String voiceType = isChinese(generateAudioDto.getStrings()) ? redisUtil.get("SuperWords:config:HUOSHAN_CHINESE_SPEAKER").toString() : redisUtil.get("SuperWords:config:HUOSHAN_ENGLISH_SPEAKER").toString();
        HashMap<String, String> results = new HashMap<>();
        if (redisUtil.get("SuperWords:generateAudio:" + generateAudioDto.getStrings()) != null) {
            results.put("audio_url", redisUtil.get("SuperWords:generateAudio:" + generateAudioDto.getStrings()).toString());
            return Result.OK("短语、句子获取音频成功", results);
        }
        Map<String, Object> wordJson = new HashMap<>();
        wordJson.put("word", generateAudioDto.getStrings());
        wordJson.put("speed_ratio", generateAudioDto.getSpeed_ratio());
        wordJson.put("voice_type", voiceType);
        wordJson.put("appid", redisUtil.get("SuperWords:config:HUOSHAN_APPID").toString());
        wordJson.put("access_token", redisUtil.get("SuperWords:config:HUOSHAN_ACCESSTOKEN").toString());
        log.info("wordJson:{}", wordJson);

        Map<String, String> result = ThirdRequestUtils.analyzeStringAudio(wordJson, thirdToken, redisUtil);
        String file_url = domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(result.get("url"), "temp");
        redisUtil.set("SuperWords:generateAudio:" + generateAudioDto.getStrings(), file_url);
        results.put("audio_url", file_url);
        return Result.OK("短语、句子生成音频成功", results);
    }

    private boolean isChinese(String str) {
        // 简单判断：如果包含中文字符就认为是中文
        return str.matches(".*[\\u4e00-\\u9fa5]+.*");
    }


    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    /**
     * Downloads an audio file from the given URL and saves it locally.
     *
     * @return The local file path where the audio file is saved.
     */
    public String downloadAndSaveLocally(String fileUrl, String bizPath) {
        try {
            // Define the base directory and ensure it exists
            String ctxPath = uploadpath;
            File baseDir = new File(ctxPath + File.separator + bizPath);
            if (!baseDir.exists()) {
                baseDir.mkdirs(); // Create the directory if it doesn't exist
            }

            // Extract the file name from the URL
            String fileName = Paths.get(new URL(fileUrl).getPath()).getFileName().toString();

            // Ensure the file name is unique
            fileName = generateUniqueFileName(fileName);

            // Define the full path to save the file
            String savePath = baseDir.getPath() + File.separator + fileName;

            // Download the file and save it locally
            try (InputStream in = new URL(fileUrl).openStream();
                 FileOutputStream out = new FileOutputStream(savePath)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            String relativePath = bizPath + File.separator + fileName;
            return relativePath.replace(File.separator, "/");
        } catch (IOException e) {
            return "";
        }
    }

    /**
     * Generates a unique file name by appending a timestamp if necessary.
     *
     * @param originalName The original file name.
     * @return A unique file name.
     */
    private static String generateUniqueFileName(String originalName) {
        if (originalName.contains(".")) {
            int dotIndex = originalName.lastIndexOf(".");
            return originalName.substring(0, dotIndex) + "_" + System.currentTimeMillis() + originalName.substring(dotIndex);
        } else {
            return originalName + "_" + System.currentTimeMillis();
        }
    }

    /**
     * 文本转音频 - 支持按句子切分
     */
    /*@ApiOperation(value = "文本转音频", notes = "将文本转换为音频，支持按句子切分")
    @PostMapping(value = "/textToAudio")
    public Result<?> textToAudio(@Valid @RequestBody TextToAudioDto textToAudioDto) {
        String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
        String thirdToken = ThirdRequestUtils.getThirdToken(ThirdRequestConstant.TOKEN_USERNAME, ThirdRequestConstant.TOKEN_PASSWORD, redisUtil);

        // 检查是否需要分句处理
        if (textToAudioDto.getSplitBySentence()) {
            // 将文本切分成句子
            List<String> sentences = splitTextToSentences(textToAudioDto.getContent());
            List<Map<String, String>> audioResults = new ArrayList<>();

            // 处理每个句子，生成音频
            for (String sentence : sentences) {
                if (StringUtils.isBlank(sentence)) {
                    continue;
                }

                Map<String, String> sentenceResult = generateAudioForSentence(sentence, thirdToken, domain);
                if (sentenceResult != null && sentenceResult.containsKey("audio_url")) {
                    Map<String, String> resultMap = new HashMap<>();
                    resultMap.put("sentence", sentence);
                    resultMap.put("audio_url", sentenceResult.get("audio_url"));
                    audioResults.add(resultMap);
                }
            }

            return Result.OK("文本转音频成功", audioResults);
        } else {
            // 不分句，直接处理整段文本
            Map<String, String> result = generateAudioForSentence(textToAudioDto.getContent(), thirdToken, domain);
            return Result.OK("文本转音频成功", result);
        }
    }*/

    /**
     * 将文本内容分割成句子
     *
     * @param text 文本内容
     * @return 句子列表
     */
    private List<String> splitTextToSentences(String text) {
        List<String> sentences = new ArrayList<>();
        if (StringUtils.isBlank(text)) {
            return sentences;
        }

        // 中英文的句子结束符号
        Pattern pattern = Pattern.compile("(?<=[.!?。！？…])\\s*");
        Matcher matcher = pattern.matcher(text);

        int lastEnd = 0;
        while (matcher.find()) {
            int end = matcher.end();
            String sentence = text.substring(lastEnd, end).trim();
            if (StringUtils.isNotBlank(sentence)) {
                sentences.add(sentence);
            }
            lastEnd = end;
        }

        // 处理最后一个句子
        if (lastEnd < text.length()) {
            String lastSentence = text.substring(lastEnd).trim();
            if (StringUtils.isNotBlank(lastSentence)) {
                sentences.add(lastSentence);
            }
        }

        return sentences;
    }

    /**
     * 为单个句子生成音频
     *
     * @param sentence   句子内容
     * @param thirdToken 第三方服务的token
     * @param domain     域名
     * @return 包含音频URL的结果
     */
    /*private Map<String, String> generateAudioForSentence(String sentence, String thirdToken, String domain) {
        // 判断语言类型
        String voiceType = isChinese(sentence) ? "BV002_streaming" : "BV503_streaming";
        HashMap<String, String> results = new HashMap<>();

        // 检查缓存中是否已存在
        if (redisUtil.get("SuperWords:generateAudio:" + sentence) != null) {
            results.put("audio_url", redisUtil.get("SuperWords:generateAudio:" + sentence).toString());
            return results;
        }

        // 构建请求参数
        Map<String, Object> wordJson = new HashMap<>();
        wordJson.put("word", sentence);
        wordJson.put("appkey", redisUtil.get("SuperWords:config:HUOSHAN_APPKEY").toString());
        wordJson.put("accessKey", redisUtil.get("SuperWords:config:HUOSHAN_ACCESSKEY").toString());
        wordJson.put("secretKey", redisUtil.get("SuperWords:config:HUOSHAN_SECRTEKEY").toString());
        wordJson.put("appid", "6193012464");
        wordJson.put("access_token", "xYS-YQhPfZqNxqRw1GHTm2rlA1p0k4BU");
        wordJson.put("en_voice_type", "BV503_streaming");
        wordJson.put("ch_voice_type", "BV002_streaming");
        wordJson.put("handle", "1");

        // 根据curl命令中的参数，添加room_id和type
        wordJson.put("room_id", "3f4d704b-a08b-4721-9b78-5b90d796ac10_1743046155");
        wordJson.put("type", "words");

        // 调用第三方服务生成音频
        Map<String, String> result = ThirdRequestUtils.analyzeStringAudio(wordJson, thirdToken, redisUtil);
        String file_url = domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(result.get("url"), "temp");

        // 保存到缓存中
        redisUtil.set("SuperWords:generateAudio:" + sentence, file_url);
        results.put("audio_url", file_url);
        return results;
    }*/

    /*@ApiOperation(value = "串词成文 - 随机短文", notes = "传入bookId和chapterId，返回该章节中随机的短文和短文对应的单词数据")
    @GetMapping(value = "/randomArticleWithWords")
    */

    /**
     * @return 包含随机短文(article)和相关单词数据(words)的结果
     *//*
    public Result<Map<String, Object>> randomArticleWithWords(
            @RequestParam(name = "bookId", required = true) String bookId,
            @RequestParam(name = "chapterId", required = true) String chapterId) {
        
        // 获取章节中的随机短文
        List<WordArticle> articles = wordArticleService.getRandomArticlesForChapterWithLimit(bookId, chapterId, 1);
        
        if (articles == null || articles.isEmpty()) {
            return Result.error("未找到相关短文");
        }
        
        WordArticle article = articles.get(0);
        
        // 从文章中获取单词ID
        String wordIdsStr = article.getWordIds();
        if (wordIdsStr == null || wordIdsStr.trim().isEmpty()) {
            return Result.error("短文中未包含单词信息");
        }
        
        // 将逗号分隔的单词ID转换为列表
        List<String> wordIds = Arrays.asList(wordIdsStr.split(","));
        
        // 查询这些特定单词的详细信息
        BookWords bookWords = new BookWords();
        bookWords.setBookId(bookId);
        bookWords.setChapterId(chapterId);
        bookWords.setWordIds(wordIds); // 设置特定的单词ID列表
        bookWords.setCreateBy(CommonUtils.getUserIdByToken());
        
        List<String> types = new ArrayList<>();
        types.add("part_of_speech");
        types.add("transformation");
        types.add("speak_naturl_phonics");
        types.add("root_particles");
        types.add("natural_phonics");
        types.add("collection");
        types.add("examine");
        types.add("root_breakdown");
        
        // 获取特定单词的详细信息
        List<BookWords> wordsList = bookWordsService.listWithWordsLimit(bookWords, 0, types);
        
        Map<String, Object> result = new HashMap<>();
        result.put("article", article);
        result.put("words", wordsList);
        
        return Result.OK(result);
    }*/
    @ApiOperation(value = "保存问题列表到文章", notes = "保存问题列表到指定文章")
    @PostMapping(value = "/saveQuestions")
    public Result<Object> saveQuestions(
            @RequestParam("articleId") String articleId,
            @RequestBody List<QuestionEntity> questionList) {
        try {
            // 获取文章
            WordArticle article = wordArticleService.getById(articleId);
            if (article == null) {
                return Result.error("未找到指定的文章");
            }

            // 设置问题列表
            article.setQuestionList(questionList);

            // 转换为JSON保存
            String questionsJson = JSONObject.toJSONString(questionList);
            article.setQuestionsJson(questionsJson);

            // 保存更新
            if (wordArticleService.updateById(article)) {
                log.info("成功保存问题列表到文章: {}, 问题数量: {}", articleId, questionList.size());
                return Result.OK("保存成功");
            } else {
                return Result.error("保存失败");
            }
        } catch (Exception e) {
            log.error("保存问题列表失败", e);
            return Result.error("保存问题列表失败: " + e.getMessage());
        }
    }

    /**
     * 文章内容转音频 - 仅处理文章内容，不包括问题列表
     */
    /*@ApiOperation(value = "文章内容转音频", notes = "将文章内容转换为音频，不包含问题列表")
    @PostMapping(value = "/articleToAudio")
    public Result<?> articleToAudio(@RequestParam("articleId") String articleId) {
        try {
            // 获取文章
            WordArticle article = wordArticleService.getById(articleId);
            if (article == null) {
                return Result.error("未找到指定的文章");
            }

            // 仅提取文章内容文本，不包含问题列表
            String content = article.getContent();
            if (StringUtils.isBlank(content)) {
                return Result.error("文章内容为空");
            }

            // 准备调用TTS API的参数
            String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
            String thirdToken = ThirdRequestUtils.getThirdToken(ThirdRequestConstant.TOKEN_USERNAME, ThirdRequestConstant.TOKEN_PASSWORD, redisUtil);

            // 检查缓存中是否已存在
            String cacheKey = "SuperWords:articleAudio:" + articleId;
            if (redisUtil.get(cacheKey) != null) {
                Map<String, String> cachedResult = new HashMap<>();
                cachedResult.put("audio_url", redisUtil.get(cacheKey).toString());
                return Result.OK("文章音频生成成功", cachedResult);
            }

            // 将文本切分成句子以便处理较长文本
            List<String> sentences = splitTextToSentences(content);
            List<Map<String, String>> audioResults = new ArrayList<>();

            // 处理每个句子，生成音频
            for (String sentence : sentences) {
                if (StringUtils.isBlank(sentence)) {
                    continue;
                }

                Map<String, String> sentenceResult = generateAudioForSentence(sentence, thirdToken, domain);
                if (sentenceResult != null && sentenceResult.containsKey("audio_url")) {
                    Map<String, String> resultMap = new HashMap<>();
                    resultMap.put("sentence", sentence);
                    resultMap.put("audio_url", sentenceResult.get("audio_url"));
                    audioResults.add(resultMap);
                }
            }

            // 将生成的第一个音频URL保存到缓存中（简化处理，实际可能需要合并多个音频）
            if (!audioResults.isEmpty()) {
                redisUtil.set(cacheKey, audioResults.get(0).get("audio_url"));
            }

            return Result.OK("文章音频生成成功", audioResults);
        } catch (Exception e) {
            log.error("文章音频生成失败", e);
            return Result.error("文章音频生成失败: " + e.getMessage());
        }
    }*/
}
