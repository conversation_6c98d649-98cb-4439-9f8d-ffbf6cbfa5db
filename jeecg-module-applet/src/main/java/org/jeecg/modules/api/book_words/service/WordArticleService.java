package org.jeecg.modules.api.book_words.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.common.system.base.entity.ConstructionEntity;
import org.jeecg.modules.api.book_words.entity.WordArticle;

import java.util.List;
import java.util.Map;

/**
 * @Description: 单词短文表
 * @Author: jeecg-boot
 * @Date: 2023-07-22
 * @Version: V1.0
 */
public interface WordArticleService extends IService<WordArticle> {

    /**
     * 为章节内的单词生成短文并存储
     *
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @param articlesPerGroup 每组生成的短文数量
     * @return 成功生成的短文数量
     */
    /*int generateAndSaveArticlesForChapter(String bookId, String chapterId, int articlesPerGroup);

    *//**
     * 为章节内的单词生成短文并存储（旧版本，不推荐使用）
     *
     * @param chapterId 章节ID
     * @param articlesPerGroup 每组生成的短文数量
     * @return 成功生成的短文数量
     *//*
    int generateAndSaveArticlesForChapter(String chapterId, int articlesPerGroup);

    *//**
     * 为指定的单词组生成短文并存储
     *
     * @param wordIds 单词ID列表
     * @param words 单词列表
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @param groupIndex 组索引
     * @param count 生成的短文数量
     * @return 包含生成的短文和相关内容的Map
     *//*
    Map<String, Object> generateAndSaveArticles(List<String> wordIds, List<String> words, String bookId, String chapterId, int groupIndex, int count);

    *//**
     * 为指定的单词组生成短文并存储（旧版本，不推荐使用）
     *
     * @param wordIds 单词ID列表
     * @param words 单词列表
     * @param chapterId 章节ID
     * @param groupIndex 组索引
     * @param count 生成的短文数量
     * @return 包含生成的短文和相关内容的Map
     *//*
    Map<String, Object> generateAndSaveArticles(List<String> wordIds, List<String> words, String chapterId, int groupIndex, int count);*/

    /**
     * 获取章节的随机短文
     *
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @return 短文列表
     */
    List<WordArticle> getRandomArticlesForChapter(String bookId, String chapterId);

    /**
     * 获取章节的随机短文（旧版本，不推荐使用）
     *
     * @param chapterId 章节ID
     * @return 短文列表
     */
    List<WordArticle> getRandomArticlesForChapter(String chapterId);

    /**
     * 获取章节的随机短文，限制返回数量
     *
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @param limit 限制数量
     * @return 短文列表
     */
    List<WordArticle> getRandomArticlesForChapterWithLimit(String bookId, String chapterId, int limit);

    /**
     * 获取包含指定单词的随机短文
     *
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @param words 单词列表
     * @param limit 限制数量
     * @return 短文列表
     */
    List<WordArticle> getRandomArticlesWithWords(String bookId, String chapterId, List<String> words, int limit);

    /**
     * 获取包含指定单词的随机短文（旧版本，不推荐使用）
     *
     * @param chapterId 章节ID
     * @param words 单词列表
     * @param limit 限制数量
     * @return 短文列表
     */
    List<WordArticle> getRandomArticlesWithWords(String chapterId, List<String> words, int limit);
    
    /**
     * 将WordArticle转换为ConstructionEntity
     * 
     * @param articles 短文列表
     * @return ConstructionEntity列表
     */
    List<ConstructionEntity> convertToConstructionEntities(List<WordArticle> articles);

    /**
     * 获取章节短文中的单词ID列表
     *
     * @param bookId 词书ID
     * @param chapterId 章节ID
     * @return 单词ID列表
     */
    List<String> getWordIdsInChapterArticles(String bookId, String chapterId);

    /**
     * 获取章节短文中的单词ID列表（旧版本，不推荐使用）
     *
     * @param chapterId 章节ID
     * @return 单词ID列表
     */
    List<String> getWordIdsInChapterArticles(String chapterId);
    
    /**
     * 根据单词ID列表直接查询短文
     * 
     * @param wordIds 单词ID列表
     * @param limit 限制数量
     * @return 短文列表
     */
    List<WordArticle> getArticlesByWordIds(List<String> wordIds, int limit);
} 