# Parent_ID问题修复说明

## 1. 问题描述

在生成应用题时，发现子题目的parent_id都是null，日志显示：
```
添加子题目: What event triggered the girl's excitement? (parent_id: null)
添加子题目: How did the girl likely feel about the school she was accepted to? (parent_id: null)
```

## 2. 问题根因分析

### 2.1 ID生成时机问题
```java
// 问题代码
InzWordQuestion parentQuestion = createWordQuestion(wordEntity, item, sort++, "0");
inzWordQuestions.add(parentQuestion);

// 此时 parentQuestion.getId() 为 null，因为ID是在保存到数据库时才生成的
for (TestQuestionEntity child : wordEntity.getChildren()) {
    InzWordQuestion childQuestion = createWordQuestion(child, item, sort++, parentQuestion.getId()); // null!
}
```

### 2.2 ID生成策略
```java
@TableId(type = IdType.ASSIGN_UUID)
private String id;
```
- 使用`ASSIGN_UUID`策略
- MyBatis-Plus在保存时才生成UUID
- 在内存中创建对象时，ID为null

## 3. 解决方案

### 3.1 手动生成UUID
```java
// 修复后的代码
InzWordQuestion parentQuestion = createWordQuestion(wordEntity, item, sort++, "0");
// 手动生成UUID作为父题目ID
String parentId = java.util.UUID.randomUUID().toString().replace("-", "");
parentQuestion.setId(parentId);
inzWordQuestions.add(parentQuestion);

// 使用手动生成的parentId
for (TestQuestionEntity child : wordEntity.getChildren()) {
    InzWordQuestion childQuestion = createWordQuestion(child, item, sort++, parentId);
    inzWordQuestions.add(childQuestion);
}
```

### 3.2 修改createWordQuestion方法
```java
private InzWordQuestion createWordQuestion(TestQuestionEntity entity, InzWords word, int sort, String parentId) {
    InzWordQuestion question = new InzWordQuestion();
    
    // 手动设置字段，避免BeanUtils可能的问题
    question.setQuestion(entity.getQuestion());
    question.setQuestionType(entity.getQuestionType());
    question.setTrueAnswer(entity.getTrueAnswer());
    
    // 设置基本信息
    question.setSort(sort);
    question.setBookId(word.getBookId());
    question.setWordId(word.getId());
    question.setChapterId(word.getChapterId());
    question.setParentId(parentId); // 确保正确设置
    
    // ... 其他字段处理
    
    return question;
}
```

## 4. 修复效果

### 4.1 修复前
```
parent_id: null (所有子题目)
```

### 4.2 修复后
```
parent_id: 生成的UUID (正确的父题目ID)
```

## 5. 技术细节

### 5.1 UUID生成
- 使用`java.util.UUID.randomUUID()`生成标准UUID
- 移除连字符，保持与MyBatis-Plus生成格式一致
- 确保ID的唯一性

### 5.2 数据一致性
- 父题目和子题目在同一个事务中保存
- 手动生成的ID与数据库约束兼容
- 保持与现有数据格式的一致性

## 6. 测试验证

### 6.1 预期日志输出
```
发现应用题，开始处理 5 个子题目
添加子题目: What event triggered the girl's excitement? (parent_id: 生成的UUID)
添加子题目: How did the girl likely feel about the school she was accepted to? (parent_id: 生成的UUID)
添加子题目: What would be the best way for the girl to express her excitement to her family? (parent_id: 生成的UUID)
添加子题目: What could be a potential downside of being too excited? (parent_id: 生成的UUID)
添加子题目: Fill in the blank: When we are ______, we often have a lot of energy and can't wait to take action. (parent_id: 生成的UUID)
```

### 6.2 数据库验证
```sql
-- 查询应用题及其子题目
SELECT id, question, question_type, parent_id, sort 
FROM inz_word_question 
WHERE word_id = '单词ID' 
ORDER BY sort;

-- 预期结果：
-- 1. 应用题主题目：parent_id = '0'
-- 2. 所有子题目：parent_id = 应用题的ID
```

## 7. 风险评估

### 7.1 兼容性
- ✅ 与现有ID生成策略兼容
- ✅ 不影响非应用题的处理
- ✅ 保持数据库约束的完整性

### 7.2 性能影响
- ✅ UUID生成开销极小
- ✅ 不增加额外的数据库查询
- ✅ 保持批量保存的效率

## 8. 总结

通过手动生成UUID解决了parent_id为null的问题：
- ✅ 确保子题目正确关联到父题目
- ✅ 保持数据的完整性和一致性
- ✅ 不影响现有功能的稳定性
- ✅ 提供清晰的日志输出便于调试

修复后，应用题的children将正确设置parent_id，确保题目关联关系的完整性。
