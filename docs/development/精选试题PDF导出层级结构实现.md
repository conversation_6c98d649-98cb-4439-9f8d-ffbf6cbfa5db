# 精选试题PDF导出层级结构实现文档

## 1. 需求概述

### 1.1 问题描述
- 现有PDF导出功能将所有题目平铺显示，没有正确处理应用题的父子关系
- 应用题主题目和子题目被当作独立题目处理，破坏了逻辑结构
- PDF中缺少层级缩进和分组显示

### 1.2 数据结构说明
- **父题目**：`parentId = "0"`，`questionType = "应用题"`，包含题干但无选项和答案
- **子题目**：`parentId` 指向父题目ID，包含完整的题目、选项、答案信息
- 题目通过 `sort` 字段控制显示顺序

## 2. 技术实现方案

### 2.1 核心架构设计

```java
// 主要流程
1. 数据查询 → 2. 数据重组 → 3. PDF生成 → 4. 答案生成

// 关键方法
- reorganizeQuestions()     // 数据重组
- generateHierarchicalPDF() // PDF内容生成
- generateHierarchicalAnswers() // 答案生成
```

### 2.2 数据重组逻辑

#### A. reorganizeQuestions方法
```java
private Map<String, List<InzTestQuestionInfo>> reorganizeQuestions(List<InzTestQuestionInfo> questions) {
    // 1. 分离父题目、子题目和普通题目
    Map<String, InzTestQuestionInfo> parentQuestions = new HashMap<>();
    Map<String, List<InzTestQuestionInfo>> childQuestionsByParent = new HashMap<>();
    List<InzTestQuestionInfo> regularQuestions = new ArrayList<>();
    
    // 2. 分类处理
    for (InzTestQuestionInfo question : questions) {
        String parentId = question.getParentId();
        
        if ("0".equals(parentId) || parentId == null) {
            if ("应用题".equals(question.getQuestionType())) {
                parentQuestions.put(question.getId(), question);
            } else {
                regularQuestions.add(question);
            }
        } else {
            childQuestionsByParent.computeIfAbsent(parentId, k -> new ArrayList<>()).add(question);
        }
    }
    
    // 3. 构建层级结构
    Map<String, List<InzTestQuestionInfo>> result = new LinkedHashMap<>();
    
    // 处理普通题目（按题型分组）
    // 处理应用题（每个应用题作为独立组）
    
    return result;
}
```

#### B. 数据结构转换
```
原始平铺结构：
[应用题主题目, 子题目1, 子题目2, 单选题1, 单选题2, ...]

转换后层级结构：
{
  "单选题": [单选题1, 单选题2, ...],
  "应用题_1_parentId1": [应用题主题目, 子题目1, 子题目2, ...],
  "多选题": [多选题1, 多选题2, ...]
}
```

### 2.3 PDF生成逻辑

#### A. generateHierarchicalPDF方法
```java
private void generateHierarchicalPDF(Document document, Map<String, List<InzTestQuestionInfo>> hierarchicalData,
                                   Font questionFont, Font optionsFont, Font sectionFont) {
    for (Map.Entry<String, List<InzTestQuestionInfo>> entry : hierarchicalData.entrySet()) {
        String groupKey = entry.getKey();
        List<InzTestQuestionInfo> questions = entry.getValue();
        
        if (groupKey.startsWith("应用题_")) {
            // 处理应用题组
            generateApplicationQuestionGroup(document, questions, sectionNumber, ...);
        } else {
            // 处理普通题型组
            generateRegularQuestionGroup(document, groupKey, questions, sectionNumber, ...);
        }
    }
}
```

#### B. 应用题组生成
```java
private void generateApplicationQuestionGroup(Document document, List<InzTestQuestionInfo> questions, ...) {
    InzTestQuestionInfo parentQuestion = questions.get(0);
    List<InzTestQuestionInfo> childQuestions = questions.subList(1, questions.size());
    
    // 1. 应用题标题
    String sectionTitle = getSectionTitle(sectionNumber, "应用题", 1);
    
    // 2. 父题目（阅读材料）
    Paragraph parentContent = new Paragraph(parentQuestion.getQuestion(), questionFont);
    parentContent.setIndentationLeft(20f);
    parentContent.setIndentationRight(20f);
    
    // 3. 子题目（带编号和选项）
    for (int i = 0; i < childQuestions.size(); i++) {
        String questionText = String.format("%d. %s", (i + 1), childQuestion.getQuestion());
        // 生成选项
        generateQuestionOptions(document, childQuestion, optionsFont);
    }
}
```

## 3. PDF显示效果

### 3.1 应用题显示格式
```
一、应用题（每小题15分，共15分）

阅读材料:
    A young girl received a new pair of shoes for her birthday. 
    Excited about her gift, she hurried to try them on...

1. What occasion led to the girl receiving new shoes?
    A. Christmas
    B. Her birthday
    C. Graduation
    D. A family outing

2. How did the girl likely feel about the school she was accepted to?
    A. Disinterested
    B. Nervous
    C. Excited
    D. Indifferent
```

### 3.2 普通题型显示格式
```
二、单选题（每小题2分，共4分）

1. What does the word 'excited' mean?
    A. Calm
    B. Eager
    C. Indifferent
    D. Bored

2. Which emotion is similar to feeling 'excited'?
    A. Happy
    B. Sad
    C. Angry
    D. Bored
```

## 4. 答案部分实现

### 4.1 generateHierarchicalAnswers方法
```java
private void generateHierarchicalAnswers(Document document, Map<String, List<InzTestQuestionInfo>> hierarchicalData,
                                       Font answerFont, Font sectionFont) {
    // 答案标题
    Paragraph answerTitle = new Paragraph("参考答案", sectionFont);
    
    // 按组生成答案
    for (Map.Entry<String, List<InzTestQuestionInfo>> entry : hierarchicalData.entrySet()) {
        if (groupKey.startsWith("应用题_")) {
            generateApplicationAnswers(document, questions, sectionNumber, ...);
        } else {
            generateRegularAnswers(document, groupKey, questions, sectionNumber, ...);
        }
    }
}
```

### 4.2 答案显示格式
```
参考答案

一、应用题
1. B
2. C
3. A
4. A
5. excited

二、单选题
1. B
2. A
```

## 5. 技术特性

### 5.1 层级结构处理
- ✅ **智能分组**：自动识别应用题和普通题型
- ✅ **父子关联**：正确处理应用题的主题目和子题目关系
- ✅ **排序保持**：按sort字段维护题目顺序
- ✅ **格式统一**：统一的编号和缩进格式

### 5.2 PDF布局优化
- ✅ **层级缩进**：应用题材料使用缩进显示
- ✅ **编号规范**：大题使用中文数字，小题使用阿拉伯数字
- ✅ **间距控制**：合理的题目间距和组间距
- ✅ **字体样式**：不同层级使用不同字体大小和样式

### 5.3 兼容性保证
- ✅ **向后兼容**：普通题型的处理逻辑完全不变
- ✅ **混合支持**：支持应用题和其他题型混合显示
- ✅ **错误处理**：安全处理空数据和异常情况

## 6. 关键代码片段

### 6.1 数据重组核心逻辑
```java
// 构建应用题组
for (Map.Entry<String, InzTestQuestionInfo> entry : parentQuestions.entrySet()) {
    String parentId = entry.getKey();
    InzTestQuestionInfo parentQuestion = entry.getValue();
    
    List<InzTestQuestionInfo> hierarchicalGroup = new ArrayList<>();
    hierarchicalGroup.add(parentQuestion);
    
    // 添加子题目
    List<InzTestQuestionInfo> children = childQuestionsByParent.get(parentId);
    if (children != null && !children.isEmpty()) {
        children.sort(Comparator.comparingInt(InzTestQuestionInfo::getSort));
        hierarchicalGroup.addAll(children);
    }
    
    // 使用特殊键标识应用题组
    String groupKey = "应用题_" + parentQuestion.getSort() + "_" + parentId;
    result.put(groupKey, hierarchicalGroup);
}
```

### 6.2 选项生成逻辑
```java
private void generateQuestionOptions(Document document, InzTestQuestionInfo question, Font optionsFont) {
    if ("单选题".equals(questionType) || "多选题".equals(questionType) || "判断题".equals(questionType)) {
        String optionsString = question.getOptions().replace("[", "").replace("]", "").trim();
        String[] options = optionsString.split(",");

        for (int i = 0; i < options.length; i++) {
            String option = options[i].trim().replaceAll("^\"|\"$", "").trim();
            String optionLabel = (char) ('A' + i) + ". ";
            Paragraph optionParagraph = new Paragraph("    " + optionLabel + option, optionsFont);
            document.add(optionParagraph);
        }
    }
}
```

## 7. 测试验证

### 7.1 测试用例
1. **纯应用题**：验证应用题的层级显示和答案格式
2. **混合题型**：验证应用题和其他题型的混合显示
3. **边界情况**：验证空数据、缺少子题目等情况

### 7.2 验证要点
- 应用题主题目显示为阅读材料
- 子题目正确缩进和编号
- 答案部分的层级对应关系
- PDF布局的美观性和可读性

## 8. 总结

### 8.1 实现效果
- ✅ 完美解决了应用题的层级结构显示问题
- ✅ 保持了原有功能的兼容性
- ✅ 提供了专业的PDF布局和格式
- ✅ 支持复杂的混合题型场景

### 8.2 技术亮点
- 智能的数据重组算法
- 灵活的PDF生成框架
- 完善的错误处理机制
- 详细的日志记录系统

通过这个实现，精选试题的PDF导出功能现在能够正确处理应用题的层级结构，提供专业、美观、易读的PDF文档。
