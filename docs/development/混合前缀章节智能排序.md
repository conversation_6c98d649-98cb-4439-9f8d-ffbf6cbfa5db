# 混合前缀章节智能排序功能

## 1. 需求背景

当导入的单词文件包含不同前缀的章节名时（如unit 1, unit 2, chapter 1, unit 3），需要实现智能排序：
1. **按前缀分组**：相同前缀的章节归为一组
2. **组内排序**：按数字顺序排序
3. **组间排序**：按前缀首次出现的顺序排序

## 2. 排序算法设计

### 2.1 排序规则
```
输入：unit 1, unit 2, chapter 1, unit 3
处理：
  1. 前缀分组：unit组[unit 1, unit 2, unit 3], chapter组[chapter 1]
  2. 组内排序：unit组[unit 1, unit 2, unit 3], chapter组[chapter 1]
  3. 组间排序：unit组在前（首次出现），chapter组在后
输出：unit 1, unit 2, unit 3, chapter 1
```

### 2.2 核心算法流程
```java
private List<InzWordBookChapter> sortChaptersByMixedPrefix(List<InzWordBookChapter> chapters) {
    // 1. 记录前缀首次出现顺序
    Map<String, Integer> prefixOrder = new LinkedHashMap<>();
    
    // 2. 按前缀分组
    Map<String, List<InzWordBookChapter>> prefixGroups = new LinkedHashMap<>();
    
    // 3. 遍历章节，提取前缀并分组
    for (InzWordBookChapter chapter : chapters) {
        String prefix = extractChapterPrefix(chapter.getName());
        if (!prefixOrder.containsKey(prefix)) {
            prefixOrder.put(prefix, prefixOrder.size());
            prefixGroups.put(prefix, new ArrayList<>());
        }
        prefixGroups.get(prefix).add(chapter);
    }
    
    // 4. 对每个前缀组内部排序
    for (List<InzWordBookChapter> group : prefixGroups.values()) {
        group.sort((c1, c2) -> {
            Integer num1 = extractChapterNumber(c1.getName());
            Integer num2 = extractChapterNumber(c2.getName());
            return num1.compareTo(num2);
        });
    }
    
    // 5. 按前缀首次出现顺序合并
    List<InzWordBookChapter> result = new ArrayList<>();
    for (String prefix : prefixOrder.keySet()) {
        result.addAll(prefixGroups.get(prefix));
    }
    
    return result;
}
```

## 3. 前缀提取算法

### 3.1 支持的前缀格式
```java
// 英文前缀
"Unit 1" -> "unit"
"Chapter 2" -> "chapter"
"Lesson 3" -> "lesson"
"Part 4" -> "part"
"Section 5" -> "section"

// 中文前缀
"单元 1" -> "单元"
"课 2" -> "课"
"第1章" -> "第章"
"第2课" -> "第课"
"第3单元" -> "第单元"
```

### 3.2 前缀提取逻辑
```java
private String extractChapterPrefix(String chapterName) {
    String name = chapterName.trim().toLowerCase();
    
    // 使用正则表达式匹配常见模式
    String[] patterns = {
        "unit\\s*\\d+",      // Unit 1, Unit1
        "chapter\\s*\\d+",   // Chapter 1, Chapter1
        "lesson\\s*\\d+",    // Lesson 1, Lesson1
        "单元\\s*\\d+",       // 单元1, 单元 1
        "第\\s*\\d+\\s*章",   // 第1章, 第 1 章
        // ... 更多模式
    };
    
    // 匹配并提取前缀
    for (String pattern : patterns) {
        if (name.matches(pattern)) {
            // 返回对应的前缀
        }
    }
    
    // 兜底逻辑：提取第一个单词作为前缀
    return extractFirstWord(name);
}
```

## 4. 实际应用示例

### 4.1 示例1：混合英文前缀
```
输入：Unit 3, Chapter 1, Unit 1, Chapter 2, Unit 2
处理过程：
  前缀分组：
    unit组: [Unit 3, Unit 1, Unit 2]
    chapter组: [Chapter 1, Chapter 2]
  组内排序：
    unit组: [Unit 1, Unit 2, Unit 3]
    chapter组: [Chapter 1, Chapter 2]
  组间排序：unit组在前（首次出现）
输出：Unit 1, Unit 2, Unit 3, Chapter 1, Chapter 2
```

### 4.2 示例2：混合中英文前缀
```
输入：Unit 2, 单元 1, Unit 1, 第1章
处理过程：
  前缀分组：
    unit组: [Unit 2, Unit 1]
    单元组: [单元 1]
    第章组: [第1章]
  组内排序：
    unit组: [Unit 1, Unit 2]
    单元组: [单元 1]
    第章组: [第1章]
输出：Unit 1, Unit 2, 单元 1, 第1章
```

## 5. 日志输出优化

### 5.1 详细的排序过程日志
```java
log.info("开始混合前缀章节排序，原始章节顺序：");
for (int i = 0; i < chapters.size(); i++) {
    log.info("  原始第{}位: {}", i + 1, chapters.get(i).getName());
}

log.info("发现新前缀: {} (顺序: {})", prefix, prefixOrder.get(prefix) + 1);

log.info("对前缀组 '{}' 进行内部排序，包含 {} 个章节", prefix, group.size());

log.info("混合前缀排序完成，最终顺序：");
for (int i = 0; i < sortedChapters.size(); i++) {
    log.info("  最终第{}位: {}", i + 1, sortedChapters.get(i).getName());
}
```

### 5.2 预期日志输出
```
2025-07-30 INFO - 开始混合前缀章节排序，原始章节顺序：
2025-07-30 INFO -   原始第1位: Unit 3
2025-07-30 INFO -   原始第2位: Chapter 1
2025-07-30 INFO -   原始第3位: Unit 1
2025-07-30 INFO -   原始第4位: Unit 2
2025-07-30 INFO - 发现新前缀: unit (顺序: 1)
2025-07-30 INFO - 发现新前缀: chapter (顺序: 2)
2025-07-30 INFO - 对前缀组 'unit' 进行内部排序，包含 3 个章节
2025-07-30 INFO -   组内第1位: Unit 1
2025-07-30 INFO -   组内第2位: Unit 2
2025-07-30 INFO -   组内第3位: Unit 3
2025-07-30 INFO - 对前缀组 'chapter' 进行内部排序，包含 1 个章节
2025-07-30 INFO -   组内第1位: Chapter 1
2025-07-30 INFO - 混合前缀排序完成，最终顺序：
2025-07-30 INFO -   最终第1位: Unit 1
2025-07-30 INFO -   最终第2位: Unit 2
2025-07-30 INFO -   最终第3位: Unit 3
2025-07-30 INFO -   最终第4位: Chapter 1
```

## 6. 技术特性

### 6.1 算法优势
- ✅ **智能分组**：自动识别不同前缀并分组
- ✅ **保持顺序**：按首次出现顺序维护前缀组的相对位置
- ✅ **组内排序**：确保同前缀章节按数字顺序排列
- ✅ **兼容性强**：支持中英文混合前缀
- ✅ **容错性好**：对无法识别的格式有兜底处理

### 6.2 性能特点
- **时间复杂度**：O(n log n)，主要消耗在组内排序
- **空间复杂度**：O(n)，需要额外存储分组信息
- **实际性能**：章节数量通常较少，性能影响可忽略

### 6.3 扩展性
- **新前缀支持**：通过修改patterns数组轻松添加新的前缀格式
- **自定义排序**：可以扩展组间排序规则
- **国际化支持**：支持多语言前缀格式

## 7. 测试用例

### 7.1 基础测试
```
输入：["Unit 1", "Unit 2", "Unit 3"]
输出：["Unit 1", "Unit 2", "Unit 3"]
```

### 7.2 混合前缀测试
```
输入：["Unit 3", "Chapter 1", "Unit 1", "Chapter 2", "Unit 2"]
输出：["Unit 1", "Unit 2", "Unit 3", "Chapter 1", "Chapter 2"]
```

### 7.3 中英文混合测试
```
输入：["Unit 2", "单元 1", "Unit 1", "第1章"]
输出：["Unit 1", "Unit 2", "单元 1", "第1章"]
```

### 7.4 边界情况测试
```
输入：["Chapter 1", "Unit 1"]  // chapter在前
输出：["Chapter 1", "Unit 1"]  // 保持chapter在前的顺序
```

## 8. 总结

混合前缀章节智能排序功能实现了：
- ✅ 智能识别不同前缀的章节
- ✅ 按前缀分组并组内排序
- ✅ 保持前缀首次出现的相对顺序
- ✅ 提供详细的排序过程日志
- ✅ 支持多种前缀格式和国际化

这个算法能够很好地处理复杂的章节命名场景，确保最终的章节顺序既符合逻辑又保持一致性。
