# PDF导出应用题分组实现文档

## 1. 需求概述

### 1.1 问题描述
当前PDF导出功能将每个应用题作为独立的题型组处理，导致应用题分散在不同位置。需要将所有应用题放在一个"应用题"题型组中，按照1、2、3...的顺序排列。

### 1.2 预期显示效果
```
三、应用题（每小题15分，共30分）
1、阅读材料：<PERSON> wanted to improve the lighting in his house...
（1）What did <PERSON> aim to achieve by installing larger windows?
（2）What was the result of <PERSON>'s action?

2、阅读材料：A young girl received a new pair of shoes...
（1）What occasion led to the girl receiving new shoes?
（2）How did the girl feel about her new shoes?
```

## 2. 技术实现方案

### 2.1 核心架构设计

```java
// 主要流程
1. 数据重组 → 2. 应用题分组 → 3. PDF生成 → 4. 答案生成

// 关键方法
- reorganizeQuestions()     // 数据重组（修改）
- parseApplicationQuestions() // 应用题解析（新增）
- generateApplicationQuestionGroup() // 应用题生成（修改）
- generateApplicationAnswers() // 应用题答案生成（修改）
```

### 2.2 数据结构设计

#### A. ApplicationQuestionGroup类
```java
/**
 * 应用题组数据结构
 */
private static class ApplicationQuestionGroup {
    InzTestQuestionInfo parentQuestion;
    List<InzTestQuestionInfo> childQuestions;
    
    ApplicationQuestionGroup(InzTestQuestionInfo parentQuestion, List<InzTestQuestionInfo> childQuestions) {
        this.parentQuestion = parentQuestion;
        this.childQuestions = childQuestions;
    }
}
```

#### B. 数据重组逻辑
```java
// 处理应用题（所有应用题放在一个组中）
if (!parentQuestions.isEmpty()) {
    List<InzTestQuestionInfo> allApplicationQuestions = new ArrayList<>();
    
    // 按sort排序所有应用题父题目
    List<InzTestQuestionInfo> sortedParents = parentQuestions.values().stream()
            .sorted(Comparator.comparingInt(InzTestQuestionInfo::getSort))
            .collect(Collectors.toList());
    
    for (InzTestQuestionInfo parentQuestion : sortedParents) {
        // 添加父题目和子题目
        allApplicationQuestions.add(parentQuestion);
        allApplicationQuestions.addAll(children);
    }
    
    // 所有应用题放在一个组中
    result.put("应用题", allApplicationQuestions);
}
```

## 3. 应用题解析实现

### 3.1 parseApplicationQuestions方法
```java
/**
 * 解析应用题列表，将其分组为父子关系
 */
private List<ApplicationQuestionGroup> parseApplicationQuestions(List<InzTestQuestionInfo> questions) {
    List<ApplicationQuestionGroup> groups = new ArrayList<>();
    Map<String, InzTestQuestionInfo> parentMap = new HashMap<>();
    Map<String, List<InzTestQuestionInfo>> childrenMap = new HashMap<>();
    
    // 分离父题目和子题目
    for (InzTestQuestionInfo question : questions) {
        String parentId = question.getParentId();
        
        if ("0".equals(parentId) || parentId == null) {
            // 父题目
            parentMap.put(question.getId(), question);
            childrenMap.put(question.getId(), new ArrayList<>());
        } else {
            // 子题目
            childrenMap.computeIfAbsent(parentId, k -> new ArrayList<>()).add(question);
        }
    }
    
    // 按sort排序父题目，然后构建组
    List<InzTestQuestionInfo> sortedParents = parentMap.values().stream()
            .sorted(Comparator.comparingInt(InzTestQuestionInfo::getSort))
            .collect(Collectors.toList());
    
    for (InzTestQuestionInfo parent : sortedParents) {
        List<InzTestQuestionInfo> children = childrenMap.get(parent.getId());
        children.sort(Comparator.comparingInt(InzTestQuestionInfo::getSort));
        groups.add(new ApplicationQuestionGroup(parent, children));
    }
    
    return groups;
}
```

## 4. PDF生成实现

### 4.1 应用题组判断逻辑
```java
// 判断是否为应用题组
if ("应用题".equals(groupKey)) {
    // 处理应用题组
    globalQuestionNumber = generateApplicationQuestionGroup(document, questions, sectionNumber, 
                                                          globalQuestionNumber, questionFont, optionsFont, sectionFont);
} else {
    // 处理普通题型组
    globalQuestionNumber = generateRegularQuestionGroup(document, groupKey, questions, sectionNumber, 
                                                      globalQuestionNumber, questionFont, optionsFont, sectionFont);
}
```

### 4.2 应用题组生成逻辑
```java
private int generateApplicationQuestionGroup(Document document, List<InzTestQuestionInfo> questions, 
                                           int sectionNumber, int startQuestionNumber, 
                                           Font questionFont, Font optionsFont, Font sectionFont) 
                                           throws DocumentException {
    // 分离所有应用题的父子关系
    List<ApplicationQuestionGroup> applicationGroups = parseApplicationQuestions(questions);
    
    // 应用题标题
    String sectionTitle = getSectionTitle(sectionNumber, "应用题", applicationGroups.size());
    Paragraph sectionParagraph = new Paragraph(sectionTitle, sectionFont);
    document.add(sectionParagraph);
    
    // 生成每个应用题
    for (int appIndex = 0; appIndex < applicationGroups.size(); appIndex++) {
        ApplicationQuestionGroup appGroup = applicationGroups.get(appIndex);
        
        // 应用题编号（1、2、3...）
        String appQuestionTitle = String.format("%d、阅读材料：", (appIndex + 1));
        Paragraph appQuestionParagraph = new Paragraph(appQuestionTitle, questionFont);
        document.add(appQuestionParagraph);
        
        // 阅读材料内容
        Paragraph materialContent = new Paragraph(appGroup.parentQuestion.getQuestion(), questionFont);
        document.add(materialContent);
        
        // 子题目
        for (int i = 0; i < appGroup.childQuestions.size(); i++) {
            InzTestQuestionInfo childQuestion = appGroup.childQuestions.get(i);
            
            // 子题目编号和内容 - 使用（1）、（2）格式
            String questionText = String.format("（%d）%s", (i + 1), childQuestion.getQuestion());
            Paragraph questionParagraph = new Paragraph(questionText, questionFont);
            document.add(questionParagraph);
            
            // 子题目选项
            generateQuestionOptions(document, childQuestion, optionsFont);
        }
    }
    
    // 应用题组只占用一个题型编号，不影响全局题目编号
    return currentQuestionNumber;
}
```

## 5. 答案生成实现

### 5.1 应用题答案生成逻辑
```java
private int generateApplicationAnswers(Document document, List<InzTestQuestionInfo> questions,
                                     int sectionNumber, int startQuestionNumber, 
                                     Font answerFont, Font sectionFont) throws DocumentException {
    // 解析应用题组
    List<ApplicationQuestionGroup> applicationGroups = parseApplicationQuestions(questions);
    
    // 应用题答案标题
    String answerSectionTitle = getAnswerSectionTitle(sectionNumber, "应用题");
    Paragraph answerSectionParagraph = new Paragraph(answerSectionTitle, answerFont);
    document.add(answerSectionParagraph);
    
    // 生成每个应用题的答案
    for (int appIndex = 0; appIndex < applicationGroups.size(); appIndex++) {
        ApplicationQuestionGroup appGroup = applicationGroups.get(appIndex);
        
        // 应用题编号（1、2、3...）
        String appAnswerTitle = String.format("%d、", (appIndex + 1));
        Paragraph appAnswerParagraph = new Paragraph(appAnswerTitle, answerFont);
        document.add(appAnswerParagraph);
        
        // 子题目答案 - 使用（1）、（2）格式
        for (int i = 0; i < appGroup.childQuestions.size(); i++) {
            InzTestQuestionInfo childQuestion = appGroup.childQuestions.get(i);
            String answerText = String.format("（%d）%s", (i + 1), childQuestion.getTrueAnswer());
            Paragraph answerParagraph = new Paragraph(answerText, answerFont);
            document.add(answerParagraph);
        }
    }
    
    // 应用题组不影响全局题目编号
    return startQuestionNumber;
}
```

## 6. 显示效果对比

### 6.1 修改前（应用题分散）
```
三、应用题（每小题15分，共15分）
第10题、
阅读材料：John wanted to improve the lighting in his house...
（1）What did John aim to achieve by installing larger windows?
（2）What was the result of John's action?

四、应用题（每小题15分，共15分）
第11题、
阅读材料：A young girl received a new pair of shoes...
（1）What occasion led to the girl receiving new shoes?
（2）How did the girl feel about her new shoes?
```

### 6.2 修改后（应用题分组）
```
三、应用题（每小题15分，共30分）
1、阅读材料：John wanted to improve the lighting in his house...
（1）What did John aim to achieve by installing larger windows?
（2）What was the result of John's action?

2、阅读材料：A young girl received a new pair of shoes...
（1）What occasion led to the girl receiving new shoes?
（2）How did the girl feel about her new shoes?
```

## 7. 技术特性

### 7.1 应用题分组
- ✅ **统一分组**：所有应用题放在一个题型组中
- ✅ **内部编号**：应用题使用1、2、3...编号
- ✅ **子题目编号**：子题目使用（1）、（2）、（3）...编号
- ✅ **排序保持**：按sort字段维护应用题顺序

### 7.2 PDF布局优化
- ✅ **层级清晰**：题型 → 应用题 → 子题目的层级结构
- ✅ **编号规范**：统一的编号格式
- ✅ **间距控制**：合理的应用题间距和子题目间距
- ✅ **答案对应**：答案部分与题目部分保持一致的编号格式

### 7.3 兼容性保证
- ✅ **向后兼容**：普通题型的处理逻辑完全不变
- ✅ **混合支持**：支持应用题和其他题型混合显示
- ✅ **错误处理**：安全处理空数据和异常情况

## 8. 测试验证

### 8.1 测试用例
1. **多个应用题**：验证多个应用题的分组和编号
2. **混合题型**：验证应用题与其他题型的混合显示
3. **答案对应**：验证答案部分的编号与题目部分一致

### 8.2 验证要点
- 所有应用题都在一个题型组中
- 应用题编号为1、2、3...
- 子题目编号为（1）、（2）、（3）...
- 答案部分的编号与题目部分一致
- PDF布局美观、清晰

## 9. 总结

### 9.1 实现效果
- ✅ 完美实现了应用题分组显示
- ✅ 应用题使用统一的编号格式
- ✅ 答案部分与题目部分保持一致
- ✅ 保持了专业的PDF布局和格式

### 9.2 技术亮点
- 灵活的应用题解析机制
- 统一的编号格式规范
- 清晰的层级结构设计
- 完善的答案对应关系

通过这个实现，PDF导出功能现在能够将所有应用题放在一个题型组中，使用统一的编号格式，提供更加专业和规范的试题文档。
