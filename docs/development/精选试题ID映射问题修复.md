# 精选试题ID映射问题修复文档

## 1. 问题描述

### 1.1 现象
在精选试题生成过程中，发现以下问题：
- 查询时的题目ID：`1a7d69b301454e139648bd69c8288b78`
- 返回结果的ID：`d47d6d1ca62bc7fc8478765e069769c8`
- **ID发生了变化，导致parent_id关联关系被破坏**

### 1.2 日志对比
```
// 查询阶段日志
主题目3: id=1a7d69b301454e139648bd69c8288b78, type=应用题, question=You are preparing to do laundry..., parentId=0

// 返回结果
{
  "id": "d47d6d1ca62bc7fc8478765e069769c8",  // ❌ ID变了
  "questionType": "应用题",
  "parentId": "0",
  "question": "You are preparing to do laundry..."
}
```

## 2. 问题根因分析

### 2.1 数据流程分析
```
InzWordQuestion (原始数据)
    ↓ 查询
id=1a7d69b301454e139648bd69c8288b78, parentId=0 (应用题主题目)
id=xxx1, parentId=1a7d69b301454e139648bd69c8288b78 (子题目1)
id=xxx2, parentId=1a7d69b301454e139648bd69c8288b78 (子题目2)
    ↓ 转换为InzTestQuestionInfo
id=d47d6d1ca62bc7fc8478765e069769c8, parentId=0 (新ID！)
id=yyy1, parentId=1a7d69b301454e139648bd69c8288b78 (❌ 指向旧ID)
id=yyy2, parentId=1a7d69b301454e139648bd69c8288b78 (❌ 指向旧ID)
```

### 2.2 问题原因
1. **MyBatis-Plus自动生成新ID**：创建`InzTestQuestionInfo`对象时，MyBatis-Plus自动生成新的UUID
2. **parent_id映射缺失**：子题目的parent_id仍然指向原始的父题目ID，而不是新生成的ID
3. **关联关系断裂**：导致应用题主题目和子题目之间的关联关系被破坏

## 3. 解决方案

### 3.1 ID映射机制
```java
// 建立原始ID到新ID的映射关系
Map<String, String> idMapping = new HashMap<>();

// 保存后记录映射关系
for (int i = 0; i < allQuestions.size(); i++) {
    String originalId = allQuestions.get(i).getId();
    String newId = inzTestQuestionInfos.get(i).getId();
    idMapping.put(originalId, newId);
}
```

### 3.2 parent_id更新机制
```java
// 更新parent_id为正确的新ID
for (int i = 0; i < allQuestions.size(); i++) {
    InzWordQuestion originalQuestion = allQuestions.get(i);
    InzTestQuestionInfo newQuestion = inzTestQuestionInfos.get(i);
    
    String originalParentId = originalQuestion.getParentId();
    if (originalParentId != null && !"0".equals(originalParentId)) {
        String newParentId = idMapping.get(originalParentId);
        if (newParentId != null) {
            newQuestion.setParentId(newParentId);
            inzTestQuestionInfoService.updateById(newQuestion);
        }
    }
}
```

## 4. 修复后的数据流程

### 4.1 正确的数据流程
```
InzWordQuestion (原始数据)
    ↓ 查询
id=A, parentId=0 (应用题主题目)
id=B, parentId=A (子题目1)
id=C, parentId=A (子题目2)
    ↓ 保存为InzTestQuestionInfo
id=A', parentId=0 (新ID)
id=B', parentId=A (临时，待更新)
id=C', parentId=A (临时，待更新)
    ↓ 建立映射关系
A -> A', B -> B', C -> C'
    ↓ 更新parent_id
id=A', parentId=0 (应用题主题目)
id=B', parentId=A' (✅ 正确指向新的父题目ID)
id=C', parentId=A' (✅ 正确指向新的父题目ID)
```

### 4.2 关键步骤
1. **批量保存**：先保存所有题目，获得新的ID
2. **建立映射**：记录原始ID到新ID的映射关系
3. **更新关联**：将子题目的parent_id更新为新的父题目ID

## 5. 详细日志输出

### 5.1 ID映射日志
```java
log.info("准备保存题目: 原始ID={}, 题型={}, parent_id={}", 
         wordEntity.getId(), wordEntity.getQuestionType(), wordEntity.getParentId());

log.info("ID映射: {} -> {}", originalId, newId);

log.info("更新parent_id: 题目ID={}, 原parent_id={}, 新parent_id={}", 
         newQuestion.getId(), originalParentId, newParentId);
```

### 5.2 预期日志输出
```
开始组装题目内容，处理ID映射关系...
准备保存题目: 原始ID=1a7d69b301454e139648bd69c8288b78, 题型=应用题, parent_id=0
准备保存题目: 原始ID=xxx1, 题型=单选题, parent_id=1a7d69b301454e139648bd69c8288b78
准备保存题目: 原始ID=xxx2, 题型=单选题, parent_id=1a7d69b301454e139648bd69c8288b78

建立ID映射关系...
ID映射: 1a7d69b301454e139648bd69c8288b78 -> d47d6d1ca62bc7fc8478765e069769c8
ID映射: xxx1 -> yyy1
ID映射: xxx2 -> yyy2

开始更新parent_id映射关系...
更新parent_id: 题目ID=yyy1, 原parent_id=1a7d69b301454e139648bd69c8288b78, 新parent_id=d47d6d1ca62bc7fc8478765e069769c8
更新parent_id: 题目ID=yyy2, 原parent_id=1a7d69b301454e139648bd69c8288b78, 新parent_id=d47d6d1ca62bc7fc8478765e069769c8
```

## 6. 修复效果验证

### 6.1 修复前的问题
```json
// 应用题主题目
{
  "id": "d47d6d1ca62bc7fc8478765e069769c8",
  "parentId": "0",
  "questionType": "应用题"
}

// 子题目（关联关系错误）
{
  "id": "yyy1",
  "parentId": "1a7d69b301454e139648bd69c8288b78",  // ❌ 指向不存在的ID
  "questionType": "单选题"
}
```

### 6.2 修复后的效果
```json
// 应用题主题目
{
  "id": "d47d6d1ca62bc7fc8478765e069769c8",
  "parentId": "0",
  "questionType": "应用题"
}

// 子题目（关联关系正确）
{
  "id": "yyy1",
  "parentId": "d47d6d1ca62bc7fc8478765e069769c8",  // ✅ 正确指向父题目
  "questionType": "单选题"
}
```

## 7. 性能考虑

### 7.1 额外开销
- **内存开销**：需要维护ID映射表
- **数据库操作**：需要额外的update操作更新parent_id
- **时间复杂度**：O(n)的映射建立和更新操作

### 7.2 优化建议
- 映射表在处理完成后及时清理
- 批量更新parent_id以减少数据库操作次数
- 考虑在数据量很大时使用事务批处理

## 8. 测试验证

### 8.1 测试用例
1. **单个应用题**：验证1个应用题+5个子题目的关联关系
2. **多个应用题**：验证多个应用题的ID映射不会冲突
3. **混合题型**：验证应用题和其他题型混合时的处理

### 8.2 验证要点
- 所有子题目的parent_id都正确指向新的父题目ID
- ID映射关系完整且无冲突
- 返回的数据结构保持完整的层级关系

## 9. 总结

### 9.1 修复内容
- ✅ 建立了原始ID到新ID的映射机制
- ✅ 实现了parent_id的正确更新
- ✅ 保持了应用题主题目和子题目的关联关系
- ✅ 添加了详细的调试日志

### 9.2 解决的问题
- ✅ ID变化导致的关联关系断裂
- ✅ 子题目无法正确关联到父题目
- ✅ 数据完整性和一致性问题

通过这个修复，精选试题功能现在能够正确处理应用题的ID映射和关联关系，确保返回的数据结构完整且关联关系正确。
