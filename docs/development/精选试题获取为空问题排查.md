# 精选试题获取为空问题排查文档

## 1. 问题描述

当前端传入`question=5`时，即使数据库中有应用题数据，仍然显示获取的为空值。

## 2. 问题分析

### 2.1 题型编号映射问题

**发现的问题**：题型编号映射错误

```java
// 当前的题型映射表
Map<String, List<String>> typeMap = new HashMap<>();
typeMap.put("1", Collections.singletonList("单选题"));
typeMap.put("2", Collections.singletonList("多选题"));
typeMap.put("3", Collections.singletonList("判断题"));
typeMap.put("4", Collections.singletonList("填空题"));
typeMap.put("5", Collections.singletonList("问答题"));  // ❌ 问题在这里
typeMap.put("6", Collections.singletonList("应用题"));  // ✅ 应用题是编号6
```

**问题根因**：
- 前端传入`question=5`期望获取应用题
- 但编号"5"映射的是"问答题"，不是"应用题"
- 应用题的正确编号是"6"

### 2.2 可能的解决方案

#### 方案1：修正前端传参
前端应该传入`question=6`来获取应用题

#### 方案2：修正题型映射表
如果业务逻辑要求"5"对应应用题，则需要调整映射表

## 3. 添加的调试日志

### 3.1 请求参数日志
```java
log.info("========== 开始生成精选试题 ==========");
log.info("请求参数: bookId={}, chapterId={}, questionType={}, questionCount={}", 
         inzTestQuestion.getBookId(), inzTestQuestion.getUnitId(), 
         inzTestQuestion.getQuestionType(), inzTestQuestion.getQuestionCount());
```

### 3.2 题型映射日志
```java
log.info("开始解析题型编号: '{}'", questionType);
log.info("题型映射表: {}", typeMap);
log.info("分割后的题型编号数组: {}", Arrays.toString(typeCodes));
log.info("题型编号 '{}' 映射为: {}", trimmedCode, types);
log.info("最终解析的题型列表: {}", questionTypes);
```

### 3.3 数据库查询日志
```java
// 统计信息
log.info("该章节总题目数量: {}", totalCount);

// 各题型数量分布
for (String questionType : questionTypes) {
    long typeCount = iInzWordQuestionService.count(...);
    log.info("题型 '{}' 的题目数量: {}", questionType, typeCount);
}

// 查询结果
log.info("查询到 {} 个主题目", mainQuestions.size());
for (int i = 0; i < mainQuestions.size(); i++) {
    InzWordQuestion q = mainQuestions.get(i);
    log.info("主题目{}: id={}, type={}, question={}, parentId={}", 
             i+1, q.getId(), q.getQuestionType(), q.getQuestion(), q.getParentId());
}
```

### 3.4 应用题子题目处理日志
```java
if ("应用题".equals(mainQuestion.getQuestionType())) {
    log.info("发现应用题，开始查询子题目，父题目ID: {}", mainQuestion.getId());
    
    // 查询子题目
    List<InzWordQuestion> childQuestions = ...;
    log.info("应用题 {} 包含 {} 个子题目", mainQuestion.getQuestion(), childQuestions.size());
    
    // 详细的子题目信息
    for (int i = 0; i < childQuestions.size(); i++) {
        InzWordQuestion child = childQuestions.get(i);
        log.info("  子题目{}: type={}, question={}, parentId={}", 
                 i+1, child.getQuestionType(), child.getQuestion(), child.getParentId());
    }
}
```

## 4. 排查步骤

### 4.1 确认题型编号
1. 检查前端传入的`question`参数值
2. 确认期望获取的题型
3. 验证题型映射表的正确性

### 4.2 检查数据库数据
1. 确认数据库中是否有对应题型的数据
2. 检查题目的`question_type`字段值
3. 验证应用题的`parent_id`设置

### 4.3 查看日志输出
运行后查看以下关键日志：
```
========== 开始生成精选试题 ==========
请求参数: bookId=xxx, chapterId=xxx, questionType=5, questionCount=xxx
题型编号 '5' 映射为: [问答题]
题型 '问答题' 的题目数量: 0
查询到 0 个主题目
```

## 5. 预期的日志输出

### 5.1 正确的应用题查询（question=6）
```
========== 开始生成精选试题 ==========
请求参数: bookId=xxx, chapterId=xxx, questionType=6, questionCount=1
开始解析题型编号: '6'
题型映射表: {1=[单选题], 2=[多选题], 3=[判断题], 4=[填空题], 5=[问答题], 6=[应用题]}
分割后的题型编号数组: [6]
题型编号 '6' 映射为: [应用题]
最终解析的题型列表: [应用题]
该章节总题目数量: 15
题型 '应用题' 的题目数量: 1
开始查询主题目，查询条件: bookId=xxx, chapterId=xxx, questionTypes=[应用题]
查询到 1 个主题目
主题目1: id=xxx, type=应用题, question=A young girl received a letter..., parentId=0
开始处理主题目，收集子题目...
添加主题目: type=应用题, question=A young girl received a letter...
发现应用题，开始查询子题目，父题目ID: xxx
应用题 A young girl received a letter... 包含 5 个子题目
  子题目1: type=单选题, question=What event triggered the girl's..., parentId=xxx
  子题目2: type=单选题, question=How did the girl likely feel..., parentId=xxx
  子题目3: type=单选题, question=What would be the best way..., parentId=xxx
  子题目4: type=单选题, question=What could be a potential..., parentId=xxx
  子题目5: type=填空题, question=Fill in the blank: When we are..., parentId=xxx
题目收集完成，总共收集到 6 个题目（包含主题目和子题目）
精选试题生成完成，共生成 6 道题目（包含应用题子题目）
```

### 5.2 错误的题型查询（question=5）
```
========== 开始生成精选试题 ==========
请求参数: bookId=xxx, chapterId=xxx, questionType=5, questionCount=1
开始解析题型编号: '5'
题型编号 '5' 映射为: [问答题]
最终解析的题型列表: [问答题]
该章节总题目数量: 15
题型 '问答题' 的题目数量: 0
查询到 0 个主题目
题目收集完成，总共收集到 0 个题目（包含主题目和子题目）
精选试题生成完成，共生成 0 道题目（包含应用题子题目）
```

## 6. 解决建议

### 6.1 立即解决方案
**使用正确的题型编号**：
- 如果要获取应用题，前端应传入`question=6`
- 如果要获取问答题，前端应传入`question=5`

### 6.2 长期解决方案
1. **统一题型编号规范**：在前后端建立统一的题型编号文档
2. **添加题型验证**：在接口层添加题型编号的有效性验证
3. **完善错误提示**：当查询结果为空时，提供更明确的错误信息

## 7. 测试验证

### 7.1 测试用例
1. 传入`question=6`，验证能否正确获取应用题
2. 传入`question=5`，验证是否正确提示问答题数量为0
3. 传入`question=1`，验证能否正确获取单选题

### 7.2 验证要点
- 题型映射是否正确
- 数据库查询条件是否准确
- 应用题子题目是否正确关联
- 日志输出是否完整清晰

通过这些详细的日志，您可以清楚地看到问题出现在哪个环节，从而快速定位和解决问题。
