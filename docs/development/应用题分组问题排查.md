# 应用题分组问题排查文档

## 1. 问题现象

### 1.1 当前问题
从用户反馈的截图可以看出，PDF中应用题仍然被分成了多个独立的题型组：
```
一、应用题
第1题、
（1）To beautify the environment
（2）All residents
...

二、应用题  
第2题、
（1）Efficient maintenance of the house
（2）Any family member
...

三、应用题
第3题、
（1）Traditional clothes
（2）To maintain cultural identity
...
```

### 1.2 期望效果
应该是所有应用题在一个题型组中：
```
一、应用题
1、阅读材料：...
（1）To beautify the environment
（2）All residents

2、阅读材料：...
（1）Efficient maintenance of the house
（2）Any family member

3、阅读材料：...
（1）Traditional clothes
（2）To maintain cultural identity
```

## 2. 问题分析

### 2.1 可能的原因
1. **数据分类错误**：应用题的父题目被错误分类为普通题目
2. **题型字段不匹配**：数据库中的`questionType`字段值不是"应用题"
3. **parentId字段问题**：应用题父题目的`parentId`不是"0"
4. **数据重组逻辑问题**：重组逻辑存在bug

### 2.2 排查步骤
1. 检查原始数据的字段值
2. 验证数据分类逻辑
3. 确认数据重组结果
4. 检查PDF生成逻辑

## 3. 调试日志增强

### 3.1 详细数据处理日志
```java
for (InzTestQuestionInfo question : questions) {
    String parentId = question.getParentId();
    String questionType = question.getQuestionType();
    
    log.info("处理题目: id={}, parentId={}, questionType={}, question={}", 
             question.getId(), parentId, questionType,
             question.getQuestion().length() > 30 ? question.getQuestion().substring(0, 30) + "..." : question.getQuestion());
    
    if ("0".equals(parentId) || parentId == null) {
        if ("应用题".equals(questionType)) {
            parentQuestions.put(question.getId(), question);
            log.info("✅ 发现应用题父题目: id={}, question={}", 
                     question.getId(), 
                     question.getQuestion().length() > 50 ? question.getQuestion().substring(0, 50) + "..." : question.getQuestion());
        } else {
            regularQuestions.add(question);
            log.info("➕ 添加普通题目: id={}, type={}", question.getId(), questionType);
        }
    } else {
        childQuestionsByParent.computeIfAbsent(parentId, k -> new ArrayList<>()).add(question);
        log.info("🔗 发现子题目: id={}, parentId={}, type={}, question={}", 
                 question.getId(), parentId, questionType,
                 question.getQuestion().length() > 30 ? question.getQuestion().substring(0, 30) + "..." : question.getQuestion());
    }
}
```

### 3.2 分类结果详细日志
```java
log.info("📊 数据分类完成 - 应用题父题目: {}, 普通题目: {}, 子题目组: {}", 
         parentQuestions.size(), regularQuestions.size(), childQuestionsByParent.size());

// 详细输出分类结果
log.info("🎯 应用题父题目列表:");
for (InzTestQuestionInfo parent : parentQuestions.values()) {
    log.info("  - ID: {}, Sort: {}, Question: {}", 
             parent.getId(), parent.getSort(), 
             parent.getQuestion().length() > 50 ? parent.getQuestion().substring(0, 50) + "..." : parent.getQuestion());
}

log.info("📝 普通题目按题型分布:");
Map<String, Long> typeCount = regularQuestions.stream()
        .collect(Collectors.groupingBy(InzTestQuestionInfo::getQuestionType, Collectors.counting()));
for (Map.Entry<String, Long> entry : typeCount.entrySet()) {
    log.info("  - {}: {} 道题目", entry.getKey(), entry.getValue());
}
```

### 3.3 最终分组结果日志
```java
log.info("🎉 题目重组完成，共生成 {} 个分组", result.size());

// 输出最终分组结果
log.info("📋 最终分组结果:");
for (Map.Entry<String, List<InzTestQuestionInfo>> entry : result.entrySet()) {
    String groupKey = entry.getKey();
    List<InzTestQuestionInfo> groupQuestions = entry.getValue();
    log.info("  - 分组: {}, 题目数量: {}", groupKey, groupQuestions.size());
    
    if ("应用题".equals(groupKey)) {
        log.info("    📖 应用题详细信息:");
        for (InzTestQuestionInfo q : groupQuestions) {
            log.info("      * ID: {}, ParentID: {}, Type: {}, Sort: {}", 
                     q.getId(), q.getParentId(), q.getQuestionType(), q.getSort());
        }
    }
}
```

## 4. 可能的问题和解决方案

### 4.1 问题1：题型字段值不匹配
**现象**：数据库中应用题父题目的`questionType`不是"应用题"

**排查方法**：
```sql
-- 检查应用题父题目的题型字段
SELECT id, question_type, parent_id, question 
FROM inz_test_question_info 
WHERE test_question_id = 'your_test_id' 
AND (parent_id = '0' OR parent_id IS NULL)
AND question_type LIKE '%应用%';
```

**解决方案**：
- 如果题型字段是其他值（如"阅读理解"、"综合题"等），需要修改判断条件
- 或者统一数据库中的题型字段值

### 4.2 问题2：parentId字段问题
**现象**：应用题父题目的`parentId`不是"0"

**排查方法**：
```sql
-- 检查所有题目的parent_id分布
SELECT parent_id, question_type, COUNT(*) as count
FROM inz_test_question_info 
WHERE test_question_id = 'your_test_id'
GROUP BY parent_id, question_type
ORDER BY parent_id;
```

**解决方案**：
- 修改判断条件，适应实际的parentId值
- 或者修正数据库中的parentId字段

### 4.3 问题3：数据重组逻辑问题
**现象**：应用题被错误地分类为普通题目

**解决方案**：
```java
// 修改分类条件，更宽松的匹配
if ("0".equals(parentId) || parentId == null || "".equals(parentId.trim())) {
    // 检查是否为应用题（可能有多种表示方式）
    if (questionType != null && (
        "应用题".equals(questionType) || 
        "阅读理解".equals(questionType) || 
        "综合题".equals(questionType) ||
        questionType.contains("应用") ||
        questionType.contains("阅读")
    )) {
        parentQuestions.put(question.getId(), question);
    } else {
        regularQuestions.add(question);
    }
}
```

## 5. 数据验证SQL

### 5.1 检查题目分布
```sql
-- 查看所有题目的基本信息
SELECT 
    id,
    question_type,
    parent_id,
    sort,
    SUBSTRING(question, 1, 50) as question_preview
FROM inz_test_question_info 
WHERE test_question_id = 'your_test_id'
ORDER BY sort;
```

### 5.2 检查应用题结构
```sql
-- 查看应用题的父子关系
SELECT 
    parent.id as parent_id,
    parent.question_type as parent_type,
    parent.sort as parent_sort,
    SUBSTRING(parent.question, 1, 50) as parent_question,
    child.id as child_id,
    child.question_type as child_type,
    child.sort as child_sort,
    SUBSTRING(child.question, 1, 30) as child_question
FROM inz_test_question_info parent
LEFT JOIN inz_test_question_info child ON parent.id = child.parent_id
WHERE parent.test_question_id = 'your_test_id'
AND (parent.parent_id = '0' OR parent.parent_id IS NULL)
AND parent.question_type LIKE '%应用%'
ORDER BY parent.sort, child.sort;
```

## 6. 临时解决方案

如果数据结构确实有问题，可以使用以下临时解决方案：

### 6.1 强制应用题分组
```java
// 在数据重组时，强制将所有包含特定关键词的题目归类为应用题
if ("0".equals(parentId) || parentId == null) {
    // 更宽松的应用题识别
    boolean isApplicationQuestion = questionType != null && (
        questionType.contains("应用") ||
        questionType.contains("阅读") ||
        questionType.contains("理解") ||
        question.getQuestion().contains("阅读材料") ||
        question.getQuestion().length() > 100  // 长题干通常是应用题
    );
    
    if (isApplicationQuestion) {
        parentQuestions.put(question.getId(), question);
    } else {
        regularQuestions.add(question);
    }
}
```

### 6.2 后处理合并
```java
// 在最终结果中，将所有应用题相关的分组合并
Map<String, List<InzTestQuestionInfo>> mergedResult = new LinkedHashMap<>();
List<InzTestQuestionInfo> allApplicationQuestions = new ArrayList<>();

for (Map.Entry<String, List<InzTestQuestionInfo>> entry : result.entrySet()) {
    String key = entry.getKey();
    List<InzTestQuestionInfo> questions = entry.getValue();
    
    if (key.contains("应用") || key.contains("阅读")) {
        allApplicationQuestions.addAll(questions);
    } else {
        mergedResult.put(key, questions);
    }
}

if (!allApplicationQuestions.isEmpty()) {
    mergedResult.put("应用题", allApplicationQuestions);
}
```

## 7. 验证步骤

1. **运行修改后的代码**，查看详细日志输出
2. **检查数据分类结果**，确认应用题是否被正确识别
3. **验证最终分组**，确认只有一个"应用题"分组
4. **测试PDF生成**，确认显示效果符合预期

## 8. 总结

通过增强的调试日志，我们可以：
- 清楚地看到每个题目的分类过程
- 确认应用题父题目是否被正确识别
- 验证最终的分组结果
- 快速定位问题所在

请运行修改后的代码，查看日志输出，这将帮助我们快速定位问题的根本原因。
