# PDF导出编译错误修复文档

## 1. 发现的错误

### 1.1 错误描述
在PDF导出功能的层级结构实现中，发现调用了不存在的方法：
```java
String sectionTitle = getApplicationSectionTitle(sectionNumber);
```

### 1.2 错误位置
- **文件**: `InzTestQuestionController.java`
- **行号**: 第897行
- **方法**: `generateApplicationQuestionGroup`

## 2. 错误原因分析

### 2.1 根本原因
在编写应用题组生成逻辑时，错误地调用了一个不存在的方法`getApplicationSectionTitle()`，该方法在代码中没有定义。

### 2.2 预期行为
应该使用现有的`getSectionTitle()`方法来生成应用题的标题。

## 3. 修复方案

### 3.1 修复前的错误代码
```java
// 应用题标题 - 使用阿拉伯数字
String sectionTitle = getApplicationSectionTitle(sectionNumber);  // ❌ 方法不存在
Paragraph sectionParagraph = new Paragraph(sectionTitle, sectionFont);
```

### 3.2 修复后的正确代码
```java
// 应用题标题
String sectionTitle = getSectionTitle(sectionNumber, "应用题", 1);  // ✅ 使用现有方法
Paragraph sectionParagraph = new Paragraph(sectionTitle, sectionFont);
```

### 3.3 修复说明
- 使用现有的`getSectionTitle(sectionNumber, "应用题", 1)`方法
- 参数说明：
  - `sectionNumber`: 题型序号
  - `"应用题"`: 题型名称
  - `1`: 题目数量（应用题按1个大题计算）

## 4. 验证修复效果

### 4.1 编译验证
修复后代码应该能够正常编译，不再出现"找不到符号"的错误。

### 4.2 功能验证
应用题标题应该正确显示为：
```
一、应用题（每小题15分，共15分）
```

### 4.3 格式验证
- 使用中文数字编号（一、二、三）
- 包含题型名称和分值信息
- 格式与其他题型保持一致

## 5. 相关方法说明

### 5.1 getSectionTitle方法
```java
private String getSectionTitle(int sectionNumber, String questionType, int questionCount) {
    String chineseNumber;
    switch (sectionNumber) {
        case 1: chineseNumber = "一"; break;
        case 2: chineseNumber = "二"; break;
        case 3: chineseNumber = "三"; break;
        case 4: chineseNumber = "四"; break;
        case 5: chineseNumber = "五"; break;
        case 6: chineseNumber = "六"; break;
        default: chineseNumber = String.valueOf(sectionNumber);
    }

    int scorePerQuestion = getScorePerQuestion(questionType);
    int totalScore = questionCount * scorePerQuestion;

    return String.format("%s、%s（每小题%d分，共%d分）",
            chineseNumber, questionType, scorePerQuestion, totalScore);
}
```

### 5.2 getScorePerQuestion方法
```java
private int getScorePerQuestion(String questionType) {
    switch (questionType) {
        case "单选题": return 2;
        case "多选题": return 3;
        case "判断题": return 2;
        case "填空题": return 3;
        case "应用题": return 15;  // 应用题每题15分
        default: return 2;
    }
}
```

## 6. 预防措施

### 6.1 代码审查
- 在提交代码前进行完整的编译检查
- 确保所有调用的方法都已定义
- 使用IDE的实时错误检查功能

### 6.2 测试验证
- 修复编译错误后进行功能测试
- 验证PDF生成的完整流程
- 检查应用题的显示格式

### 6.3 文档更新
- 及时更新技术文档
- 记录所有方法的用途和参数
- 维护代码变更日志

## 7. 其他潜在问题检查

### 7.1 Import语句检查
所有必要的import语句都已正确添加：
```java
import com.lowagie.text.*;
import com.lowagie.text.pdf.*;
import java.util.stream.Collectors;
// ... 其他必要的import
```

### 7.2 方法依赖检查
确认所有新增方法都正确调用了现有的方法：
- ✅ `getSectionTitle()` - 已存在
- ✅ `getScorePerQuestion()` - 已存在
- ✅ `getAnswerSectionTitle()` - 已存在

### 7.3 变量作用域检查
确认所有变量都在正确的作用域内定义和使用。

## 8. 总结

### 8.1 修复内容
- ✅ 修复了`getApplicationSectionTitle`方法不存在的错误
- ✅ 使用现有的`getSectionTitle`方法替代
- ✅ 保持了代码的一致性和可维护性

### 8.2 修复效果
- 编译错误完全解决
- 应用题标题格式正确
- 功能逻辑保持完整

### 8.3 经验总结
- 在编写新功能时要确保所有调用的方法都已定义
- 优先使用现有的方法而不是重复实现
- 及时进行编译检查和功能验证

通过这次修复，PDF导出功能的层级结构实现现在应该能够正常编译和运行。
