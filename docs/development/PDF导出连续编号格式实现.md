# PDF导出连续编号格式实现文档

## 1. 新格式需求

### 1.1 题目编号格式
- **普通题目**：使用连续编号（第1题、第2题、第3题...）
- **应用题主题目**：使用连续编号（第10题、第11题...）
- **应用题子题目**：使用括号编号（（1）、（2）、（3）...）

### 1.2 预期显示效果
```
一、单选题（每小题2分，共6分）
第1题. What is the capital of France?
第2题. Which planet is closest to the sun?
第3题. What is 2+2?

二、多选题（每小题3分，共6分）
第4题. Which of the following are fruits?
第5题. Select all prime numbers:

三、判断题（每小题2分，共4分）
第6题. The earth is flat.
第7题. Water boils at 100°C.

四、填空题（每小题3分，共6分）
第8题. The chemical symbol for gold is _____.
第9题. Complete the sentence: "To be or not to be, _____ is the question."

五、应用题（每小题15分，共30分）
第10题、
阅读材料：<PERSON> wanted to improve the lighting in his house...
（1）What did <PERSON> aim to achieve by installing larger windows?
（2）What was the result of <PERSON>'s action?

第11题、
阅读材料：A young girl received a new pair of shoes...
（1）What occasion led to the girl receiving new shoes?
（2）How did the girl feel about her new shoes?
```

## 2. 技术实现方案

### 2.1 全局编号机制
```java
// 在generateHierarchicalPDF方法中维护全局编号
int globalQuestionNumber = 1; // 全局题目编号

for (Map.Entry<String, List<InzTestQuestionInfo>> entry : hierarchicalData.entrySet()) {
    if (groupKey.startsWith("应用题_")) {
        globalQuestionNumber = generateApplicationQuestionGroup(document, questions, sectionNumber, 
                                                              globalQuestionNumber, ...);
    } else {
        globalQuestionNumber = generateRegularQuestionGroup(document, groupKey, questions, sectionNumber, 
                                                          globalQuestionNumber, ...);
    }
}
```

### 2.2 方法签名更新
所有生成方法都增加了`startQuestionNumber`参数和返回值：

```java
// 修改前
private void generateApplicationQuestionGroup(Document document, List<InzTestQuestionInfo> questions, 
                                            int sectionNumber, Font questionFont, Font optionsFont, Font sectionFont)

// 修改后
private int generateApplicationQuestionGroup(Document document, List<InzTestQuestionInfo> questions, 
                                           int sectionNumber, int startQuestionNumber, 
                                           Font questionFont, Font optionsFont, Font sectionFont)
```

## 3. 具体实现细节

### 3.1 普通题型编号实现
```java
// 在generateRegularQuestionGroup方法中
int currentQuestionNumber = startQuestionNumber;
for (int i = 0; i < questions.size(); i++) {
    InzTestQuestionInfo question = questions.get(i);
    
    // 题干 - 使用全局编号
    String questionText = String.format("第%d题. %s", currentQuestionNumber, question.getQuestion());
    Paragraph questionParagraph = new Paragraph(questionText, questionFont);
    
    currentQuestionNumber++;
}

return currentQuestionNumber; // 返回下一个编号
```

### 3.2 应用题编号实现
```java
// 在generateApplicationQuestionGroup方法中
// 应用题主题目编号
String mainQuestionTitle = String.format("第%d题、", startQuestionNumber);
Paragraph mainQuestionParagraph = new Paragraph(mainQuestionTitle, questionFont);

// 阅读材料
Paragraph materialTitle = new Paragraph("阅读材料：" + parentQuestion.getQuestion(), questionFont);

// 子题目编号
for (int i = 0; i < childQuestions.size(); i++) {
    InzTestQuestionInfo childQuestion = childQuestions.get(i);
    
    // 子题目编号和内容 - 使用（1）、（2）格式
    String questionText = String.format("（%d）%s", (i + 1), childQuestion.getQuestion());
    Paragraph questionParagraph = new Paragraph(questionText, questionFont);
}

return startQuestionNumber + 1; // 应用题算1道大题
```

## 4. 答案部分实现

### 4.1 答案编号对应
答案部分使用相同的编号逻辑，确保与题目编号一一对应：

```java
// 普通题型答案
String answerText = String.format("第%d题. %s", currentQuestionNumber, question.getTrueAnswer());

// 应用题答案
String mainAnswerTitle = String.format("第%d题、", startQuestionNumber);
// 子题目答案
String answerText = String.format("（%d）%s", (i + 1), childQuestion.getTrueAnswer());
```

### 4.2 答案显示效果
```
参考答案

一、单选题
第1题. A
第2题. B
第3题. C

二、多选题
第4题. A, C
第5题. B, D

三、判断题
第6题. 错误
第7题. 正确

四、填空题
第8题. Au
第9题. that

五、应用题
第10题、
（1）To improve lighting
（2）Better natural light

第11题、
（1）Her birthday
（2）Excited and happy
```

## 5. 关键代码变更

### 5.1 主要方法修改
1. **generateHierarchicalPDF**: 增加全局编号维护
2. **generateApplicationQuestionGroup**: 增加编号参数和返回值
3. **generateRegularQuestionGroup**: 增加编号参数和返回值
4. **generateHierarchicalAnswers**: 增加全局编号维护
5. **generateApplicationAnswers**: 增加编号参数和返回值
6. **generateRegularAnswers**: 增加编号参数和返回值

### 5.2 编号格式统一
- **题目编号**: `第%d题.` 或 `第%d题、`（应用题）
- **子题目编号**: `（%d）`
- **答案编号**: 与题目编号保持一致

## 6. 技术特性

### 6.1 连续编号
- ✅ **全局连续**：所有题目使用连续编号，不按题型重新开始
- ✅ **应用题特殊处理**：应用题主题目占用一个编号，子题目使用括号编号
- ✅ **答案对应**：答案编号与题目编号完全对应

### 6.2 格式规范
- ✅ **编号格式**：统一使用"第X题."格式
- ✅ **应用题格式**：主题目使用"第X题、"，子题目使用"（X）"
- ✅ **阅读材料**：明确标识"阅读材料："
- ✅ **间距控制**：合理的题目间距和组间距

### 6.3 兼容性保证
- ✅ **向后兼容**：题型分组和标题格式保持不变
- ✅ **选项显示**：选项格式和缩进保持不变
- ✅ **分值计算**：题型分值计算逻辑不变

## 7. 测试验证

### 7.1 测试用例
1. **纯普通题型**：验证连续编号是否正确
2. **纯应用题**：验证应用题编号和子题目编号
3. **混合题型**：验证不同题型间的编号连续性
4. **答案对应**：验证答案编号与题目编号的对应关系

### 7.2 验证要点
- 题目编号连续性（第1题、第2题、第3题...）
- 应用题编号正确性（第10题、第11题...）
- 子题目编号格式（（1）、（2）、（3）...）
- 答案编号对应关系
- PDF布局美观性

## 8. 显示效果对比

### 8.1 修改前（按题型重新编号）
```
一、单选题
1. Question 1
2. Question 2

二、应用题
1. Application Question
  1. Sub Question 1
  2. Sub Question 2
```

### 8.2 修改后（全局连续编号）
```
一、单选题
第1题. Question 1
第2题. Question 2

二、应用题
第3题、
阅读材料：Application Question
（1）Sub Question 1
（2）Sub Question 2
```

## 9. 总结

### 9.1 实现效果
- ✅ 完美实现了连续编号的PDF格式
- ✅ 应用题的层级结构清晰明确
- ✅ 答案编号与题目编号完全对应
- ✅ 保持了专业的PDF布局和格式

### 9.2 技术亮点
- 全局编号管理机制
- 灵活的方法参数传递
- 统一的编号格式规范
- 完善的答案对应关系

通过这个实现，PDF导出功能现在能够生成符合您要求的连续编号格式，提供更加专业和规范的试题文档。
