# 精选试题应用题子题目支持功能实现

## 1. 需求背景

在精选试题功能中，当用户选择了应用题时，需要将该应用题的所有子题目（parent_id指向该应用题的题目）也一并返回，确保用户能够看到完整的应用题内容。

## 2. 问题分析

### 2.1 原始逻辑问题
```java
// 原始查询逻辑 - 只查询主题目，忽略了应用题的子题目
List<InzWordQuestion> list = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
    .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
    .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId())
    .in(InzWordQuestion::getQuestionType, questionTypes)
    .last("ORDER BY RAND() LIMIT " + inzTestQuestion.getQuestionCount()));
```

**问题**：
- 查询时没有区分主题目和子题目
- 应用题的子题目可能被随机选中，但缺少主题目上下文
- 或者主题目被选中，但子题目被遗漏

### 2.2 期望行为
- 当选择应用题时，返回应用题主题目 + 所有子题目
- 保持原有的随机选择逻辑
- 确保题目的完整性和关联性

## 3. 技术实现

### 3.1 修改查询策略
```java
// 新的查询逻辑 - 只查询主题目（parent_id为'0'或null）
List<InzWordQuestion> mainQuestions = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
    .eq(InzWordQuestion::getBookId, inzTestQuestion.getBookId())
    .eq(InzWordQuestion::getChapterId, inzTestQuestion.getUnitId())
    .in(InzWordQuestion::getQuestionType, questionTypes)
    .and(wrapper -> wrapper.eq(InzWordQuestion::getParentId, "0").or().isNull(InzWordQuestion::getParentId))
    .last("ORDER BY RAND() LIMIT " + inzTestQuestion.getQuestionCount()));
```

**改进点**：
- 只查询主题目，避免子题目被单独选中
- 保持随机选择的逻辑
- 确保数据的完整性

### 3.2 应用题子题目查询
```java
// 收集所有题目（包括应用题的子题目）
List<InzWordQuestion> allQuestions = new ArrayList<>();
for (InzWordQuestion mainQuestion : mainQuestions) {
    // 添加主题目
    allQuestions.add(mainQuestion);
    
    // 如果是应用题，查询并添加其子题目
    if ("应用题".equals(mainQuestion.getQuestionType())) {
        List<InzWordQuestion> childQuestions = iInzWordQuestionService.list(new QueryWrapper<InzWordQuestion>().lambda()
                .eq(InzWordQuestion::getParentId, mainQuestion.getId())
                .orderByAsc(InzWordQuestion::getSort));
        allQuestions.addAll(childQuestions);
        log.info("应用题 {} 包含 {} 个子题目", mainQuestion.getQuestion(), childQuestions.size());
    }
}
```

**特性**：
- 自动检测应用题类型
- 按sort字段排序子题目
- 保持题目的逻辑顺序
- 详细的日志记录

## 4. 实现效果

### 4.1 数据流程示例

**输入**：用户选择包含应用题的精选试题

**处理过程**：
1. 查询主题目（包括应用题主题目）
2. 对于每个应用题，查询其子题目
3. 按顺序组装完整的题目列表
4. 保存到InzTestQuestionInfo表

**输出**：完整的题目列表，包含应用题及其所有子题目

### 4.2 具体示例

假设用户选择了1道应用题：

**数据库中的数据**：
```sql
-- 应用题主题目
id='app_001', question='In a bustling city...', question_type='应用题', parent_id='0'

-- 应用题子题目
id='child_001', question='What is the primary issue...', question_type='单选题', parent_id='app_001'
id='child_002', question='What are citizens urging...', question_type='单选题', parent_id='app_001'
id='child_003', question='What is a suggested solution...', question_type='单选题', parent_id='app_001'
id='child_004', question='What consequence is mentioned...', question_type='单选题', parent_id='app_001'
id='child_005', question='What can performance refer to...', question_type='单选题', parent_id='app_001'
```

**返回结果**：
- 1道应用题主题目
- 5道子题目
- 总共6道题目

## 5. 兼容性保证

### 5.1 非应用题处理
- 单选题、多选题等非应用题的处理逻辑完全不变
- 保持原有的随机选择和排序逻辑
- 确保向后兼容性

### 5.2 混合题型支持
- 支持同时选择应用题和其他题型
- 每种题型按照各自的逻辑处理
- 保持题目的完整性和关联性

## 6. 性能优化

### 6.1 查询优化
- 使用分步查询，避免复杂的JOIN操作
- 按需查询子题目，减少不必要的数据传输
- 利用数据库索引提高查询效率

### 6.2 内存优化
- 使用ArrayList动态扩容
- 及时释放不需要的对象引用
- 避免重复数据的创建

## 7. 日志监控

### 7.1 关键日志
```java
log.info("应用题 {} 包含 {} 个子题目", mainQuestion.getQuestion(), childQuestions.size());
log.info("精选试题生成完成，共生成 {} 道题目（包含应用题子题目）", inzTestQuestionInfos.size());
```

### 7.2 监控指标
- 应用题的选中频率
- 子题目的数量分布
- 题目生成的总数量
- 处理时间和性能指标

## 8. 测试建议

### 8.1 功能测试
1. 选择纯应用题，验证子题目是否完整返回
2. 选择混合题型，验证各类题目是否正确处理
3. 验证题目的排序和关联关系

### 8.2 边界测试
1. 应用题没有子题目的情况
2. 应用题子题目数量很多的情况
3. 同时选择多道应用题的情况

### 8.3 性能测试
1. 大量题目的查询性能
2. 复杂查询条件的响应时间
3. 并发访问的稳定性

## 9. 总结

通过这次修改，精选试题功能现在能够：
- ✅ 正确处理应用题及其子题目
- ✅ 保持原有功能的兼容性
- ✅ 提供完整的题目内容
- ✅ 维护题目的逻辑关联性
- ✅ 支持混合题型的选择

这个实现确保了用户在选择应用题时能够获得完整的题目体验，同时保持了系统的稳定性和性能。
