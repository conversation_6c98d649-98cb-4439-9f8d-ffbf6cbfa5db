# 应用题Children处理功能需求文档

## 1. 需求背景

当前系统在生成试题时，如果生成了应用题（question_type="应用题"），需要将其children中的子题目提取出来，作为独立的InzWordQuestion记录保存到数据库中，并设置正确的parent_id关联关系。

## 2. 当前实现分析

### 2.1 现有试题生成流程
```java
// InzWordsServiceImpl.java 第274-301行
protected void generateQuestions(@NotNull ArrayList<InzWords> words) {
    words.parallelStream().forEach(item -> {
        // 调用第三方API生成试题
        List<TestQuestionEntity> wordEntities = ThirdRequestUtils.analyzeQuestion(wordBooks.getName(),sendMessage,thirdToken,redisUtil);
        
        // 转换并保存试题
        ArrayList<InzWordQuestion> inzWordQuestions = new ArrayList<>();
        for (TestQuestionEntity wordEntity : wordEntities) {
            InzWordQuestion inzWordQuestion = new InzWordQuestion();
            BeanUtils.copyProperties(wordEntity, inzWordQuestion);
            // 设置基本信息
            inzWordQuestion.setSort(sort);
            inzWordQuestion.setBookId(item.getBookId());
            inzWordQuestion.setWordId(item.getId());
            inzWordQuestion.setChapterId(item.getChapterId());
            inzWordQuestions.add(inzWordQuestion);
        }
        inzWordQuestionService.saveBatch(inzWordQuestions);
    });
}
```

### 2.2 问题分析
当前实现**没有处理应用题的children子题目**，需要增加以下逻辑：
1. 检测题目类型是否为"应用题"
2. 如果是应用题，解析children数组
3. 将children中的子题目作为独立记录保存
4. 设置子题目的parent_id为应用题的id

## 3. 数据结构分析

### 3.1 输入数据格式（您提供的例子）
```json
{
    "question": "In a bustling city, waste management...",
    "question_type": "应用题",
    "children": [
        {
            "question": "What is the primary issue mentioned in the passage?",
            "options": ["Poor waste management", "Overpopulation", "Traffic congestion", "Noise pollution"],
            "question_type": "单选题",
            "true_answer": "Poor waste management",
            "other_content": {"分值": 2}
        },
        // ... 更多子题目
    ],
    "other_content": {"分值": 5}
}
```

### 3.2 目标数据库结构
```sql
-- 应用题主记录
INSERT INTO inz_word_question (id, question, question_type, parent_id, ...) 
VALUES ('parent_id_123', 'In a bustling city...', '应用题', '0', ...);

-- 子题目记录
INSERT INTO inz_word_question (id, question, question_type, parent_id, options, true_answer, ...) 
VALUES ('child_id_1', 'What is the primary issue...', '单选题', 'parent_id_123', '[...]', 'Poor waste management', ...);
```

## 4. 技术实现方案

### 4.1 修改TestQuestionEntity类
需要在TestQuestionEntity中添加children字段支持：

```java
@Data
public class TestQuestionEntity implements Serializable {
    @ApiModelProperty(value = "问题")
    private String question;
    
    @ApiModelProperty(value = "选项")
    private List<String> options;
    
    @ApiModelProperty(value = "题型")
    private String questionType;
    
    @ApiModelProperty(value = "正确答案")
    private String trueAnswer;
    
    @ApiModelProperty(value = "其他内容")
    private List<Object> otherContent;
    
    // 新增：支持children子题目
    @ApiModelProperty(value = "子题目")
    private List<TestQuestionEntity> children;
}
```

### 4.2 修改试题生成逻辑
在InzWordsServiceImpl.generateQuestions方法中增加children处理：

```java
protected void generateQuestions(@NotNull ArrayList<InzWords> words) {
    words.parallelStream().forEach(item -> {
        List<TestQuestionEntity> wordEntities = ThirdRequestUtils.analyzeQuestion(...);
        ArrayList<InzWordQuestion> inzWordQuestions = new ArrayList<>();
        int sort = 1;
        
        for (TestQuestionEntity wordEntity : wordEntities) {
            // 保存主题目
            InzWordQuestion parentQuestion = createWordQuestion(wordEntity, item, sort++, "0");
            inzWordQuestions.add(parentQuestion);
            
            // 处理应用题的children
            if ("应用题".equals(wordEntity.getQuestionType()) && 
                wordEntity.getChildren() != null && !wordEntity.getChildren().isEmpty()) {
                
                for (TestQuestionEntity child : wordEntity.getChildren()) {
                    InzWordQuestion childQuestion = createWordQuestion(child, item, sort++, parentQuestion.getId());
                    inzWordQuestions.add(childQuestion);
                }
            }
        }
        
        inzWordQuestionService.saveBatch(inzWordQuestions);
    });
}

private InzWordQuestion createWordQuestion(TestQuestionEntity entity, InzWords word, int sort, String parentId) {
    InzWordQuestion question = new InzWordQuestion();
    BeanUtils.copyProperties(entity, question);
    question.setSort(sort);
    question.setBookId(word.getBookId());
    question.setWordId(word.getId());
    question.setChapterId(word.getChapterId());
    question.setParentId(parentId);
    
    // 处理options字段
    if (entity.getOptions() != null) {
        question.setOptions(entity.getOptions().toString());
    }
    
    return question;
}
```

## 5. 实现步骤

### 步骤1：修改TestQuestionEntity类
- 添加children字段支持
- 确保JSON反序列化正确处理嵌套结构

### 步骤2：修改试题生成逻辑
- 在generateQuestions方法中添加children处理逻辑
- 抽取createWordQuestion公共方法
- 确保parent_id正确设置

### 步骤3：测试验证
- 使用您提供的JSON数据进行测试
- 验证数据库中的记录结构正确
- 确认parent_id关联关系正确

## 6. 预期效果

实现后，当生成应用题时：
1. ✅ 应用题本身作为一条记录保存（parent_id='0'）
2. ✅ 每个children子题目作为独立记录保存
3. ✅ 所有子题目的parent_id指向应用题的id
4. ✅ 保持原有的sort排序逻辑
5. ✅ 保持原有的bookId、chapterId、wordId关联

## 7. 风险评估

- **低风险**：修改仅涉及试题生成逻辑，不影响现有功能
- **兼容性**：对于非应用题，逻辑保持不变
- **数据完整性**：通过事务确保数据一致性
