package org.jeecg.common.constant;

/**
 * 缓存常量
 */
public interface CacheConstant {

    /**
     * 字典信息缓存
     */
    public static final String SYS_DICT_CACHE = "sys:cache:dict";
    
    /**
     * 表字典信息缓存
     */
    public static final String SYS_DICT_TABLE_CACHE = "sys:cache:dictTable";
    
    /**
     * 表字典信息缓存（通过多个key）
     */
    public static final String SYS_DICT_TABLE_BY_KEYS_CACHE = "sys:cache:dictTableByKeys";
    
    /**
     * 数据权限配置缓存
     */
    public static final String SYS_DATA_PERMISSIONS_CACHE = "sys:cache:permission:datarules";
    
    /**
     * 缓存用户信息
     */
    public static final String SYS_USERS_CACHE = "sys:cache:user";
    
    /**
     * 全部部门信息缓存
     */
    public static final String SYS_DEPARTS_CACHE = "sys:cache:depart:alldata";
    
    /**
     * 全部部门IDS缓存
     */
    public static final String SYS_DEPART_IDS_CACHE = "sys:cache:depart:allids";
    
    /**
     * 测试缓存key
     */
    public static final String TEST_DEMO_CACHE = "test:demo";
    
    /**
     * 字典信息缓存
     */
    public static final String SYS_ENABLE_DICT_CACHE = "sys:cache:dict:enable";
    
    /**
     * gateway路由缓存
     */
    public static final String GATEWAY_ROUTES = "sys:cache:cloud:gateway_routes";
    
    /**
     * 短信验证码缓存key前缀
     */
    public static final String SMS_CODE_PREFIX = "sys:cache:sms:code:";
    
    /**
     * 动态数据源缓存
     */
    public static final String SYS_DYNAMICDB_CACHE = "sys:cache:dbconnect:dynamic:";
} 