package org.jeecg.common.util;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.system.base.entity.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ThirdRequestUtils {

    private static final Logger logger = LoggerFactory.getLogger(ThirdRequestUtils.class);

    // 配置文件中获取这些值
    private static final String BASE_URL = "https://open-api.infrabiz.net";
    private static final String APP_ID = "5211b25d29";
    private static final String SECRET = "OThmN2UzMTc0ZTAxMTJmMTY3NDIwMzA3OGJhMDVlYWYxNTAxMjc4Mzg2OGUzM2U1ZjZkMTg1MGZhMmM1OWNkNQ==";
    private static final String SMS_APPID = "b17d703e740062b9";
    private static final String SMS_SIGN = "InfraBiz";
    private static final String SMS_TEMPLATE = "3a6a685f29b4c070";
    private static final String SMS_ENCRYPT_KEY = "7d3e909d202a0b22d05802ac71c45ca9";
    private static final String ROOM_ID = "df2e432c-aadb-4a12-849e-1b3cdd66e4a6_1736583087";
    private static final String AI_PLATFORM = "CloseAi";
    private static final String BIG_MODULE = "gpt-4o-mini";
    private static final String FILL_IN_ROOM_ID = "61732fea-d826-44e0-a442-69594296aa5d_1737350343";
    private static final String QUESTION_ROOM_ID = "99d5ec36-5e77-43ab-ba75-c752f53342df_1737800797";   //e18bf3c6-47dd-4e32-9449-d11b45ef97fd_1741920565
    private static final String CONSTRUCTION_ROOM_ID = "f91c829b-91b3-48c6-bf8e-060d668039fc_1737358600";

    /**
     * 发送短信
     *
     * @param phoneNumber 接收短信的电话号码
     * @param content     短信内容
     * @return 发送成功返回 true，失败返回 false
     */
    public static boolean sendSms(String phoneNumber, String content, String token, RedisUtil redisUtil) {
        // 构建请求数据

        JSONObject optionJson = new JSONObject();
        optionJson.put("code", content);

        JSONObject data = new JSONObject();
        data.put("mobile", phoneNumber);
        data.put("sign", redisUtil.get("SuperWords:config:SMS_SIGN"));
        data.put("template", redisUtil.get("SuperWords:config:SMS_TEMPLATE"));
        data.put("option", optionJson.toString());
        data.put("appid", redisUtil.get("SuperWords:config:SMS_APPID"));
        data.put("type", "sms_tencent");
        data.put("encryptKey", md5(String.valueOf(redisUtil.get("SuperWords:config:SMS_APPID")) + String.valueOf(redisUtil.get("SuperWords:config:SMS_ENCRYPT_KEY"))));

        // 构建 URL 查询参数
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/api/v1/sendSms";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))));
            URI uri = uriBuilder.build();  // 构建最终的 URI
            HttpPost httpPost = new HttpPost(uri);
            System.out.println(data.toString());
            httpPost.setEntity(new StringEntity(data.toString(), StandardCharsets.UTF_8));
            httpPost.setHeader("Content-Type", "application/json");
            httpPost.setHeader("token", token);
            HttpResponse response = httpClient.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            // 解析响应数据并检查响应状态
            JSONObject responseJson = JSONObject.parseObject(responseBody);
            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
                return false;
            }
            return true;
        } catch (IOException e) {
            logger.error("发送请求失败", e);
            return false;
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取第三方token
     */
    public static String getThirdToken(String userName, String password, RedisUtil redisUtil) {
        assert redisUtil != null;
        if (redisUtil.get("SuperWords:loginCommonToken:" + userName) != null) {
            return redisUtil.get("SuperWords:loginCommonToken:" + userName).toString();
        }
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/api/v1/login";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 构建 URL 查询参数
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))))
                    .addParameter("username", userName)
                    .addParameter("password", password);

            URI uri = uriBuilder.build();  // 构建最终的 URI
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("Content-Type", "application/json");

            HttpResponse response = httpClient.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            // 解析响应数据并检查响应状态
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
                return "";
            }
            JSONObject data = responseJson.getJSONObject("data");
//            String token = data.get("token").toString();
            redisUtil.set("SuperWords:loginCommonToken:" + userName, data.getString("token"), data.getInteger("ttl"));
            return data.get("token").toString();
        } catch (IOException e) {
            logger.error("发送请求失败", e);
            return "";
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * MD5 加密方法
     */
    private static String md5(String input) {
        try {
            java.security.MessageDigest md = java.security.MessageDigest.getInstance("MD5");
            byte[] array = md.digest(input.getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : array) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (Exception e) {
            logger.error("MD5 加密失败", e);
            return "";
        }
    }

    /**
     * 保存提示词
     */
    public static Map<String, String> savePrompt(String prompt, RedisUtil redisUtil) {
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/savePrompt";
        Map<String, String> results = new HashMap<>();
        String hash = "";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            // 创建 HttpPost 请求
            HttpPost httpPost = new HttpPost(url);
            httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");

            // 使用 MultipartEntityBuilder 构建表单数据
            List<NameValuePair> params = new ArrayList<>();
            params.add(new BasicNameValuePair("content", prompt));
            httpPost.setEntity(new UrlEncodedFormEntity(params, "UTF-8"));

            // 执行请求
            HttpResponse response = httpClient.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("SavePrompt Response: {}", responseBody);

            // 解析响应
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("保存提示词请求失败，错误代码：{}", responseJson.getInteger("code"));
            } else {
                // 从data对象中提取hash
                JSONObject data = responseJson.getJSONObject("data");
                if (data != null && data.containsKey("hash")) {
                    hash = data.getString("hash");
                }
            }
        } catch (IOException e) {
            logger.error("保存提示词请求失败", e);
        }

        results.put("hash", hash);
        return results;
    }

    /**
     * 解析单词
     */
    public static List<WordEntity> analyzeWords(String[] words, String token, RedisUtil redisUtil, Integer type) {
        List<WordEntity> wordEntities = new ArrayList<>();
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/justJson";

        // 定义最大重试次数
        int maxRetries = 3;
        int retries = 0;

        while (retries <= maxRetries) {
            try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
                URIBuilder uriBuilder = new URIBuilder(url);
                uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                        .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))))
                        .addParameter("room_id", String.valueOf(redisUtil.get("SuperWords:config:WordKEY")))
                        .addParameter("type", "words")
                        .addParameter("appkey", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_APPKEY")))
                        .addParameter("accessKey", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_ACCESSKEY")))
                        .addParameter("secretKey", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_SECRTEKEY")))
                        .addParameter("en_voice_type", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_ENGLISH_SPEAKER")))
                        .addParameter("ch_voice_type", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_CHINESE_SPEAKER")))
                        .addParameter("appid", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_APPID")))
                        .addParameter("handle", "1")
                        .addParameter("words", String.join(",", words))
                        .addParameter("access_token", String.valueOf(redisUtil.get("SuperWords:config:HUOSHAN_ACCESSTOKEN")));
                URI uri = uriBuilder.build();  // 构建最终的 URI
                HttpGet httpGet = new HttpGet(uri);
                httpGet.setHeader("Content-Type", "application/json");

                HttpResponse response = httpClient.execute(httpGet);
                String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

                logger.info("Response: {}", responseBody);

                // 解析响应数据并检查响应状态
                JSONObject responseJson = JSONObject.parseObject(responseBody);

                if (responseJson.getInteger("code") != 200) {
                    // 检查是否是 500 错误
                    if (responseJson.getInteger("code") == 500 && retries < maxRetries) {
                        retries++;  // 增加重试次数
                        logger.warn("遇到500错误，正在重试... 第 {} 次重试", retries);
                        continue;  // 继续下一次请求
                    } else {
                        logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
                        break;  // 如果错误不是500或达到最大重试次数，退出循环
                    }
                } else {
                    JSONArray data = responseJson.getJSONArray("data");
                    // 添加到结果列表
                    for (int i = 0; i < data.size(); i++) {
                        JSONObject wordData = data.getJSONObject(i);
                        if (!wordData.containsKey("root_particles") || !(wordData.get("root_particles") instanceof JSONArray)) {
                            wordData.put("root_particles", new JSONArray());
                        }

                        // Same for other array fields that might cause issues
                        String[] arrayFields = {"root_breakdown", "natural_phonics", "speak_natural_phonics",
                                "part_of_speech", "word_transformation", "collocations",
                                "examples", "etymology"};
                        for (String field : arrayFields) {
                            if (!wordData.containsKey(field) || !(wordData.get(field) instanceof JSONArray)) {
                                wordData.put(field, new JSONArray());
                            }
                        }

                        WordEntity wordEntity = new WordEntity();

                        wordEntity.setNaturalPhonics(getSafeList(wordData, "natural_phonics", NaturalPhonics.class));
                        wordEntity.setSpeakNaturalPhonics(getSafeList(wordData, "speak_natural_phonics", SpeakNaturalPhonics.class));
                        wordEntity.setRootBreakdown(getSafeList(wordData, "root_breakdown", RootBreakdown.class));
                        wordEntity.setPartOfSpeech(getSafeList(wordData, "part_of_speech", PartOfSpeech.class));
                        wordEntity.setWordTransformation(getSafeList(wordData, "word_transformation", WordTransformation.class));
                        wordEntity.setCollocations(getSafeList(wordData, "collocations", Collocation.class));
                        wordEntity.setExamples(getSafeList(wordData, "examples", Example.class));
                        wordEntity.setEtymology(getSafeList(wordData, "etymology", Etymology.class));
                        wordEntity.setWord(wordData.getString("word"));
                        wordEntity.setPronunciation(wordData.getJSONObject("pronunciation") != null ? wordData.getJSONObject("pronunciation").toJavaObject(Pronunciation.class) : null);
                        wordEntity.setEtymology(wordData.getJSONArray("etymology") != null ? wordData.getJSONArray("etymology").toJavaList(Etymology.class) : null);
                        wordEntity.setUsAudioUrl(wordData.getString("us_audio_url"));
                        wordEntity.setNaturalAudioUrl(wordData.getString("natural_audio_url"));
                        wordEntity.setAudioUrl(wordData.getString("audio_url"));
                        wordEntity.setBreakdownAudioUrl(wordData.getString("breakdown_audio_url"));
                        wordEntity.setNaturalPhonics(wordData.getJSONArray("natural_phonics") != null ? wordData.getJSONArray("natural_phonics").toJavaList(NaturalPhonics.class) : null);
                        wordEntity.setPronunciationGuide(wordData.getJSONArray("pronunciation_guide") != null ? wordData.getJSONArray("pronunciation_guide").toJavaList(String.class) : null);
                        wordEntity.setHomophonic(wordData.getString("homophonic"));
                        wordEntities.add(wordEntity);
                    }
                    break;  // 如果请求成功，退出循环
                }
            } catch (IOException e) {
                logger.error("发送请求失败", e);
                break;  // 出现异常时，退出循环
            } catch (URISyntaxException e) {
                throw new RuntimeException(e);
            }
        }
        return wordEntities;
    }


    private static <T> List<T> getSafeList(JSONObject json, String key, Class<T> clazz) {
        try {
            JSONArray array = json.getJSONArray(key);
            return array != null ? array.toJavaList(clazz) : new ArrayList<>();
        } catch (Exception e) {
            logger.warn("Error parsing {} as array, defaulting to empty list", key, e);
            return new ArrayList<>();
        }
    }

    /**
     * 例句填空
     */
    public static List<FillInEntity> analyzeFillIn(List<String> words, String token, RedisUtil redisUtil) {
        List<FillInEntity> fillInEntities = new ArrayList<>();
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/justJson";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))))
                    .addParameter("room_id", String.valueOf(redisUtil.get("SuperWords:config:FILLINKEY")))
                    .addParameter("type", "fill_in")
                    .addParameter("words", String.join(",", words));
            URI uri = uriBuilder.build();  // 构建最终的 URI
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("Content-Type", "application/json");

            HttpResponse response = httpClient.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            // 解析响应数据并检查响应状态
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                throw new RuntimeException("生成错误，请重试");
            } else {
                JSONArray data = responseJson.getJSONArray("data");
                for (int i = 0; i < data.size(); i++) { // 遍历外层数组
                    JSONArray innerArray = data.getJSONArray(i); // 获取内层数组
                    for (int j = 0; j < innerArray.size(); j++) { // 遍历内层数组
                        JSONObject entityJson = innerArray.getJSONObject(j);
                        // 将JSON对象转换为实体类
                        FillInEntity entity = entityJson.toJavaObject(FillInEntity.class);
                        fillInEntities.add(entity);
                    }
                }
            }
        } catch (IOException e) {
            logger.error("发送请求失败", e);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        return fillInEntities;
    }

    /**
     * 串词生文
     */
    public static List<ConstructionEntity> analyzeConstruction(List<String> words, String token, RedisUtil redisUtil,String fileName) {
        List<ConstructionEntity> constructionEntities = new ArrayList<>();
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/justJson";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))))
                    .addParameter("room_id", String.valueOf(redisUtil.get("SuperWords:config:CONSTRUCTIONKEY")))
                    .addParameter("type", "construction")
                    .addParameter("appkey",redisUtil.get("SuperWords:config:HUOSHAN_APPKEY").toString())
                    .addParameter("access_token",redisUtil.get("SuperWords:config:HUOSHAN_ACCESSTOKEN").toString())
                    .addParameter("words", String.join(",", words))
                    .addParameter("book_name",fileName);
            URI uri = uriBuilder.build();  // 构建最终的 URI
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("Content-Type", "application/json");

            HttpResponse response = httpClient.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            // 解析响应数据并检查响应状态
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
            } else {
                // 获取 data 数组的第一个元素，这是一个 JSONArray
                JSONArray dataArray = responseJson.getJSONArray("data").getJSONArray(0);
                // 遍历 data 数组中的每个对象
                for (int i = 0; i < dataArray.size(); i++) {
                    // 添加到结果列表
                    constructionEntities.add(dataArray.getJSONObject(i).toJavaObject(ConstructionEntity.class));
                }
            }
        } catch (IOException e) {
            logger.error("发送请求失败", e);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        return constructionEntities;
    }

    /**
     * 精选试题
     */
    public static List<TestQuestionEntity> analyzeQuestion(String bookName,String words, String token, RedisUtil redisUtil) {
        List<TestQuestionEntity> testQuestionEntities = new ArrayList<>();
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/justJson";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))))
                    .addParameter("room_id", String.valueOf(redisUtil.get("SuperWords:config:QUESTIONKEY")))
                    .addParameter("type", "question")
                    .addParameter("book_name",bookName)
                    .addParameter("words", words);
            URI uri = uriBuilder.build();  // 构建最终的 URI
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("Content-Type", "application/json");
            HttpResponse response = httpClient.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            // 解析响应数据并检查响应状态
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
            } else {
                // 遍历 data 数组，获取每个 JSON 对象并转换成 TestQuestionEntity
                JSONArray data = (JSONArray) responseJson.getJSONArray("data").get(0);
                for (int i = 0; i < data.size(); i++) {
                    // 添加到结果列表
                    testQuestionEntities.add(data.getJSONObject(i).toJavaObject(TestQuestionEntity.class));
                }
            }
        } catch (IOException e) {
            logger.error("发送请求失败", e);
        } catch (URISyntaxException e) {
            throw new RuntimeException(e);
        }
        return testQuestionEntities;
    }


    public static Map<String, String> analyzeWordAudio(Map<String, Object> wordJson, String token, RedisUtil redisUtil) {
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/api/v1/genderNewNaturalBreakdownAudio";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))));
            URI uri = uriBuilder.build();  // 构建最终的 URI

            // 创建 HttpPost 请求
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(JSONObject.toJSONString(wordJson), StandardCharsets.UTF_8);
            System.out.println(JSONObject.toJSONString(wordJson));
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
                throw new RuntimeException("请求失败，错误代码：" + responseJson.getInteger("code"));
            } else {
                // 提取 data 中的 natural_url 和 breakdown_url
                JSONObject data = responseJson.getJSONObject("data");
                String naturalUrl = data.getString("natural_url");
                String breakdownUrl = data.getString("breakdown_url");

                // 返回两个 URL
                Map<String, String> result = new HashMap<>();
                result.put("natural_url", naturalUrl);
                result.put("breakdown_url", breakdownUrl);
                return result;
            }
        } catch (IOException e) {
            logger.error("发送请求失败", e);
            throw new RuntimeException("发送请求失败", e);
        } catch (URISyntaxException e) {
            throw new RuntimeException("URI 语法错误", e);
        }
    }

    public static Map<String, String> analyzeChineseWordAudio(Map<String, Object> wordJson, String token, RedisUtil redisUtil) {
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/api/v1/genderChineseAudio";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))));
            URI uri = uriBuilder.build();  // 构建最终的 URI

            // 创建 HttpPost 请求
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(JSONObject.toJSONString(wordJson), StandardCharsets.UTF_8);
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("Response: {}", responseBody);

            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
                throw new RuntimeException("请求失败，错误代码：" + responseJson.getInteger("code"));
            } else {
                // 提取 data 中的 natural_url 和 breakdown_url
                JSONObject data = responseJson.getJSONObject("data");
                String breakdownUrl = data.getString("file_url");

                // 返回两个 URL
                Map<String, String> result = new HashMap<>();
                result.put("file_url", breakdownUrl);
                return result;
            }
        } catch (IOException e) {
            logger.error("发送请求失败", e);
            throw new RuntimeException("发送请求失败", e);
        } catch (URISyntaxException e) {
            throw new RuntimeException("URI 语法错误", e);
        }
    }


    public static Map<String, String> analyzeStringAudio(Map<String, Object> wordJson, String token, RedisUtil redisUtil) {
        String url = redisUtil.get("SuperWords:config:BASE_URL") + "/api/v1/genderNewAudio";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("app_id", String.valueOf(redisUtil.get("SuperWords:config:APP_ID")))
                    .addParameter("identity", md5(String.valueOf(redisUtil.get("SuperWords:config:APP_ID")) + String.valueOf(redisUtil.get("SuperWords:config:SECRET"))));
            URI uri = uriBuilder.build();  // 构建最终的 URI

            // 创建 HttpPost 请求
            HttpPost httpPost = new HttpPost(uri);
            httpPost.setHeader("Content-Type", "application/json");

            StringEntity entity = new StringEntity(JSONObject.toJSONString(wordJson), StandardCharsets.UTF_8);
            logger.info("Request: {}", JSONObject.toJSONString(wordJson));
            httpPost.setEntity(entity);
            HttpResponse response = httpClient.execute(httpPost);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 200) {
                logger.error("请求失败，错误代码：{}", responseJson.getInteger("code"));
                throw new RuntimeException("请求失败，错误代码：" + responseJson.getInteger("code"));
            } else {
                // 提取 data 中的 natural_url 和 breakdown_url
                JSONObject data = responseJson.getJSONObject("data");
                String breakdownUrl = data.getString("url");

                // 返回两个 URL
                Map<String, String> result = new HashMap<>();
                result.put("url", breakdownUrl);
                return result;
            }
        } catch (IOException e) {
            logger.error("发送请求失败", e);
            throw new RuntimeException("发送请求失败", e);
        } catch (URISyntaxException e) {
            throw new RuntimeException("URI 语法错误", e);
        }
    }


    /**
     * 获取用户金豆提成
     *
     * @param userLevel         用户等级 (1:创总 2:渠道 30:区/县合伙人 31:市级合伙人 32:省级合伙人)
     * @param parentUserLevel   直接上级等级
     * @param indirectUserLevel 间接上级等级 (可为空)
     * @param token             认证token
     * @return Result对象包含金豆提成信息
     */
    public static Map<String, String> getUserBeanReward(Integer userLevel, Integer parentUserLevel,
                                                        Integer indirectUserLevel, String token) throws Exception {
        // 参数校验
        if (userLevel == null || parentUserLevel == null) {
            throw new Exception("userLevel和parentUserLevel不能为空");
        }

        // 构建请求URL
        String url = "https://www.61dian.cn/v1/api/auth/users/bean";

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            URIBuilder uriBuilder = new URIBuilder(url);
            uriBuilder.addParameter("userLevel", String.valueOf(userLevel))
                    .addParameter("parentUserLevel", String.valueOf(parentUserLevel))
                    .addParameter("indirectUserLevel", indirectUserLevel == null ? null : String.valueOf(indirectUserLevel));

            URI uri = uriBuilder.build();
            HttpGet httpGet = new HttpGet(uri);
            httpGet.setHeader("Cookie", "SERVERID=4c1ac6cfefd5e380a2f2271404a17423|1749090647|1749090647; SERVERCORSID=4c1ac6cfefd5e380a2f2271404a17423|1749090647|1749090647");

            // 如果有token则添加Authorization头
            if (StringUtils.isNotBlank(token)) {
                httpGet.setHeader("Authorization", "Bearer " + token);
            }

            HttpResponse response = httpClient.execute(httpGet);
            String responseBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);

            logger.info("获取金豆提成响应: {}", responseBody);

            // 解析响应
            JSONObject responseJson = JSONObject.parseObject(responseBody);

            if (responseJson.getInteger("code") != 0) {
                throw new Exception("获取金豆提成失败: " + responseJson.getString("msg"));
            }

            // 处理返回的金豆数据
            String[] beanValues = responseJson.getString("msg").split(",");
            Map<String, String> result = new HashMap<>();

            if (indirectUserLevel == null) {
                result.put("directReward", beanValues[0]);
            } else {
                result.put("directReward", beanValues[0]);
                result.put("indirectReward", beanValues[1]);
            }

            return result;

        } catch (IOException e) {
            logger.error("获取金豆提成请求失败", e);
            throw new Exception("请求失败: " + e.getMessage());
        } catch (URISyntaxException e) {
            logger.error("URL构建错误", e);
            throw new Exception("URL构建错误");
        } catch (Exception e) {
            logger.error("获取金豆提成异常", e);
            throw new Exception("系统异常: " + e.getMessage());
        }
    }
}
