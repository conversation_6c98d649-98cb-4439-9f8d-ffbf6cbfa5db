package org.jeecg.common.util;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsRequest;
import com.aliyuncs.dysmsapi.model.v20170525.SendSmsResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.Map;

/**
 * 阿里云短信服务工具类
 */
@Slf4j
@Component
public class AliyunSmsUtil {

    @Value("${jeecg.sms.accessKey:}")
    private String accessKeyId;

    @Value("${jeecg.sms.secretKey:}")
    private String accessKeySecret;
    
    @Value("${jeecg.oss.accessKey:}")
    private String ossAccessKeyId;
    
    @Value("${jeecg.oss.secretKey:}")
    private String ossAccessKeySecret;
    
    @Autowired
    private RedisUtil redisUtil;

    /**
     * 短信API产品名称（固定）
     */
    private static final String PRODUCT = "Dysmsapi";

    /**
     * 短信API产品域名（固定）
     */
    private static final String DOMAIN = "dysmsapi.aliyuncs.com";

    /**
     * 发送短信
     *
     * @param phoneNumber   手机号
     * @param signName      签名名称
     * @param templateCode  模板CODE
     * @param templateParam 模板参数（JSON格式）
     * @return 发送结果
     */
    public SendSmsResponse sendSms(String phoneNumber, String signName, String templateCode, Map<String, String> templateParam) {
        try {
            // 获取正确的 AccessKey
            String useAccessKeyId = accessKeyId != null && !accessKeyId.isEmpty() ? accessKeyId : ossAccessKeyId;
            String useAccessKeySecret = accessKeySecret != null && !accessKeySecret.isEmpty() ? accessKeySecret : ossAccessKeySecret;
            
            // 打印调试信息
            log.info("使用的 AccessKey ID: {}", useAccessKeyId);
            log.info("使用的签名: {}, 模板Code: {}", signName, templateCode);
            
            // 初始化acsClient
            IClientProfile profile = DefaultProfile.getProfile("cn-hangzhou", useAccessKeyId, useAccessKeySecret);
            DefaultProfile.addEndpoint("cn-hangzhou", "cn-hangzhou", PRODUCT, DOMAIN);
            IAcsClient acsClient = new DefaultAcsClient(profile);

            // 组装请求对象
            SendSmsRequest request = new SendSmsRequest();
            // 必填:待发送手机号
            request.setPhoneNumbers(phoneNumber);
            // 必填:短信签名-可在短信控制台中找到
            request.setSignName(signName);
            // 必填:短信模板-可在短信控制台中找到
            request.setTemplateCode(templateCode);
            
            // 可选:模板中的变量替换JSON串,如模板内容为"亲爱的${name},您的验证码为${code}"时
            if (templateParam != null && !templateParam.isEmpty()) {
                request.setTemplateParam(com.alibaba.fastjson.JSON.toJSONString(templateParam));
            }

            // 选填-上行短信扩展码(无特殊需求用户请忽略此字段)
            // request.setSmsUpExtendCode("90997");

            // 可选:outId为提供给业务方扩展字段,最终在短信回执消息中将此值带回给调用者
            // request.setOutId("yourOutId");

            // 发起API请求并获取响应
            SendSmsResponse response = acsClient.getAcsResponse(request);
            log.info("发送短信结果：code={}, message={}, requestId={}, bizId={}", 
                    response.getCode(), response.getMessage(), response.getRequestId(), response.getBizId());
            return response;
        } catch (ClientException e) {
            log.error("发送短信异常: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 发送验证码短信
     *
     * @param phoneNumber 手机号
     * @param code        验证码
     * @return 发送结果
     */
    public boolean sendVerificationCode(String phoneNumber, String code) {
        // 从配置中获取签名和模板
        String signName = "点点教育";  // 默认签名
        String templateCode = "SMS_136005161";  // 默认模板CODE
        
        // 尝试从Redis获取配置
        try {
            if (redisUtil.hasKey("SuperWords:config:SMS_SIGN")) {
                signName = redisUtil.get("SuperWords:config:SMS_SIGN").toString();
            }
            if (redisUtil.hasKey("SuperWords:config:SMS_TEMPLATE")) {
                templateCode = redisUtil.get("SuperWords:config:SMS_TEMPLATE").toString();
            }
        } catch (Exception e) {
            log.warn("从Redis获取短信配置失败，使用默认配置", e);
        }
        
        Map<String, String> templateParam = new HashMap<>();
        templateParam.put("code", code);
        
        SendSmsResponse response = sendSms(phoneNumber, signName, templateCode, templateParam);
        
        return response != null && "OK".equals(response.getCode());
    }
} 