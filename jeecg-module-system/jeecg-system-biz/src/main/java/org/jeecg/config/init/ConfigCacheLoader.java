package org.jeecg.config.init;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.modules.config.entity.InzConfig;
import org.jeecg.modules.config.service.IInzConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * @Description: 项目启动时加载配置项到缓存
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@Slf4j
@Component
public class ConfigCacheLoader {

    @Autowired
    private IInzConfigService inzConfigService;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * 项目启动时加载配置项到缓存
     */
    @PostConstruct
    public void loadConfigToCache() {
        log.info("开始加载配置项到缓存...");
        QueryWrapper<InzConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("code", "value");
        inzConfigService.list(queryWrapper).forEach(config -> {
            redisUtil.set("SuperWords:config:" + config.getCode(), config.getValue());
            log.info("加载配置项到缓存：{} -> {}", config.getCode(), config.getValue());
        });
        log.info("配置项加载到缓存完成！");
    }
}