package org.jeecg.modules.user_front.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.jeecg.modules.user_front.entity.InzUserPayLog;

import java.util.List;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface InzUserPayLogMapper extends BaseMapper<InzUserPayLog> {

	/**
	 * 通过主表id删除子表数据
	 *
	 * @param mainId 主表id
	 * @return boolean
	 */
	public boolean deleteByMainId(@Param("mainId") String mainId);

  /**
   * 通过主表id查询子表数据
   *
   * @param mainId 主表id
   * @return List<InzUserPayLog>
   */
	public List<InzUserPayLog> selectByMainId(@Param("mainId") String mainId);
}
