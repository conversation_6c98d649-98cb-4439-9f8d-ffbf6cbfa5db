package org.jeecg.modules.books.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.books.entity.InzWordBookChapter;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.jeecgframework.poi.excel.annotation.ExcelCollection;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description: 词书表
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@Data
@ApiModel(value="inz_word_booksPage对象", description="词书表")
public class InzWordBooksPage {

	/**主键*/
	@ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
	@ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**词书名称*/
	@Excel(name = "词书名称", width = 15)
	@ApiModelProperty(value = "词书名称")
    private String name;
	/**适用年级*/
	@Excel(name = "适用年级", width = 15)
	@ApiModelProperty(value = "适用年级")
    private String gradeLevel;
	/**缩略图*/
	@Excel(name = "缩略图", width = 15)
	@ApiModelProperty(value = "缩略图")
    private String thumb;
	@ApiModelProperty(value = "教育层次Id")
	private String educationId;
	/**教育层次名称*/
	@Excel(name = "教育层次名称", width = 15)
	@ApiModelProperty(value = "教育层次名称")
    private String educationName;
	/**单词总数*/
	@Excel(name = "单词总数", width = 15)
	@ApiModelProperty(value = "单词总数")
    private Integer wordsCount;
	/**排序*/
	@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "排序")
    private Integer sort;

	@Excel(name = "排序", width = 15)
	@ApiModelProperty(value = "提示词")
	private String prompt;
	
	@ExcelCollection(name="词书课节管理")
	@ApiModelProperty(value = "词书课节管理")
	private List<InzWordBookChapter> inzWordBookChapterList;
	
}
