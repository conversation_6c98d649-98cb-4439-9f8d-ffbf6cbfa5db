package org.jeecg.modules.books.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 词书表
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@ApiModel(value="inz_word_books对象", description="词书表")
@Data
@TableName("inz_word_books")
public class InzWordBooks implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**词书名称*/
	@Excel(name = "词书名称", width = 15)
    @ApiModelProperty(value = "词书名称")
    private String name;
	/**适用年级*/
	@Excel(name = "适用年级", width = 15)
    @ApiModelProperty(value = "适用年级")
    private String gradeLevel;
	/**缩略图*/
	@Excel(name = "缩略图", width = 15)
    @ApiModelProperty(value = "缩略图")
    private String thumb;
    /**教育层次名称*/
    @ApiModelProperty(value = "教育层次Id")
    private String educationId;
	/**教育层次名称*/
	@Excel(name = "教育层次名称", width = 15)
    @ApiModelProperty(value = "教育层次名称")
    private String educationName;
	/**单词总数*/
	@Excel(name = "单词总数", width = 15)
    @ApiModelProperty(value = "单词总数")
    private Integer wordsCount;
	/**排序*/
	@Excel(name = "排序", width = 15)
    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "默认图书")
    private Integer isDefault;

    @ApiModelProperty(value = "提示词")
    private String prompt;

    @ApiModelProperty(value = "金豆数量")
    private Integer goldenBean;
}
