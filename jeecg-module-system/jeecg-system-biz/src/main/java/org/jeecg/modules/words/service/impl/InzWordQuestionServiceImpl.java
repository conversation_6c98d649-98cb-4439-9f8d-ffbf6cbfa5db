package org.jeecg.modules.words.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.words.entity.InzWordQuestion;
import org.jeecg.modules.words.entity.InzWordsEtymology;
import org.jeecg.modules.words.mapper.InzWordsEtymologyMapper;
import org.jeecg.modules.words.mapper.InzWordsQuestionMapper;
import org.jeecg.modules.words.service.IInzWordQuestionService;
import org.jeecg.modules.words.service.IInzWordsEtymologyService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 存储词源信息
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
@Service
public class InzWordQuestionServiceImpl extends ServiceImpl<InzWordsQuestionMapper, InzWordQuestion> implements IInzWordQuestionService {
	

}
