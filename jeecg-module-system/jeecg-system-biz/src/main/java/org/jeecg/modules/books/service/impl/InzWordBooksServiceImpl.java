package org.jeecg.modules.books.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.books.entity.InzWordBookChapter;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.mapper.InzWordBooksMapper;
import org.jeecg.modules.books.service.IInzWordBookChapterService;
import org.jeecg.modules.books.service.IInzWordBooksService;
import org.jeecg.modules.words.service.IInzWordsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 词书表
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@Service
public class InzWordBooksServiceImpl extends ServiceImpl<InzWordBooksMapper, InzWordBooks> implements IInzWordBooksService {


	@Autowired
	private IInzWordBookChapterService inzWordBookChapterService;

	@Lazy
	@Autowired
	private IInzWordsService iInzWordsService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void saveMain(InzWordBooks inzWordBooks, List<InzWordBookChapter> inzWordBookChapterList) {
		baseMapper.insert(inzWordBooks);
		if(inzWordBookChapterList!=null && !inzWordBookChapterList.isEmpty()) {
			for(InzWordBookChapter entity:inzWordBookChapterList) {
				//外键设置
				entity.setBookId(inzWordBooks.getId());
				inzWordBookChapterService.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMain(InzWordBooks inzWordBooks,List<InzWordBookChapter> inzWordBookChapterList) {
		baseMapper.updateById(inzWordBooks);

		//1.先删除子表数据
		inzWordBookChapterService.deleteByMainId(inzWordBooks.getId());

		//2.子表数据重新插入
		if(inzWordBookChapterList!=null && !inzWordBookChapterList.isEmpty()) {
			for(InzWordBookChapter entity:inzWordBookChapterList) {
				//外键设置
				entity.setBookId(inzWordBooks.getId());
				inzWordBookChapterService.insert(entity);
			}
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delMain(String id) {
		inzWordBookChapterService.deleteByMainId(id);
		iInzWordsService.delMainByBookId(id);
		baseMapper.deleteById(id);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void delBatchMain(Collection<? extends Serializable> idList) {
		for(Serializable id:idList) {
			inzWordBookChapterService.deleteByMainId(id.toString());
			baseMapper.deleteById(id);
		}
	}

    @Override
    public void increment(String bookId) {
		InzWordBooks books = getById(bookId);
		books.setWordsCount(books.getWordsCount()  + 1);
		updateById(books);
	}

	@Override
	public void decrement(String bookId) {
		InzWordBooks books = getById(bookId);
		books.setWordsCount(books.getWordsCount()  - 1);
		updateById(books);
	}

}
