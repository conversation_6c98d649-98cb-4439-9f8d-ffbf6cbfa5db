package org.jeecg.modules.user_front.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.user_books.entity.AddUserBooksDto;
import org.jeecg.modules.user_front.entity.ChangePasswordDTO;
import org.jeecg.modules.user_front.entity.InzUserDevice;
import org.jeecg.modules.user_front.entity.InzUserFront;
import org.jeecg.modules.user_front.entity.InzUserPayLog;

import javax.validation.Valid;
import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * @Description: 用户表
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface IInzUserFrontService extends IService<InzUserFront> {

	/**
	 * 添加一对多
	 *
	 * @param inzUserFront
	 * @param inzUserDeviceList
	 * @param inzUserPayLogList
	 */
	public void saveMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList,List<InzUserPayLog> inzUserPayLogList) ;
	
	/**
	 * 修改一对多
	 *
	 * @param inzUserFront
	 * @param inzUserDeviceList
	 * @param inzUserPayLogList
	 */
	public void updateMain(InzUserFront inzUserFront,List<InzUserDevice> inzUserDeviceList,List<InzUserPayLog> inzUserPayLogList);
	
	/**
	 * 删除一对多
	 *
	 * @param id
	 */
	public void delMain (String id);
	
	/**
	 * 批量删除一对多
	 *
	 * @param idList
	 */
	public void delBatchMain (Collection<? extends Serializable> idList);


	void verifyAndDeduct(String id, int totalCost, @Valid AddUserBooksDto addUserBooksDto, int size) throws Exception;

	boolean changePassword(ChangePasswordDTO changePasswordDTO);
}
