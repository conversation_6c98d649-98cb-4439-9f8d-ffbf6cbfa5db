package org.jeecg.modules.words.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.words.entity.InzWordsEtymology;

import java.util.List;

/**
 * @Description: 存储词源信息
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
public interface IInzWordsEtymologyService extends IService<InzWordsEtymology> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<InzWordsEtymology>
	 */
	public List<InzWordsEtymology> selectByMainId(String mainId);

	int insert(InzWordsEtymology entity);

	boolean deleteByMainId(String id);
}
