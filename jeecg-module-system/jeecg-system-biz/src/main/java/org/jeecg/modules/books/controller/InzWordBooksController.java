package org.jeecg.modules.books.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.books.entity.InzWordBookChapter;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.service.IInzWordBookChapterService;
import org.jeecg.modules.books.service.IInzWordBooksService;
import org.jeecg.modules.books.vo.InzWordBooksPage;
import org.jeecg.modules.education.service.IInzEducationService;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
 /**
 * @Description: 词书表
 * @Author: jeecg-boot
 * @Date:   2025-02-10
 * @Version: V1.0
 */
@Api(tags="词书表")
@RestController
@RequestMapping("/books/inzWordBooks")
@Slf4j
public class InzWordBooksController {
	@Autowired
	private IInzWordBooksService inzWordBooksService;
	@Autowired
	private IInzWordBookChapterService inzWordBookChapterService;
	@Autowired
	private IInzEducationService inzEducationService;
	
	/**
	 * 分页列表查询
	 *
	 * @param inzWordBooks
	 * @param pageNo
	 * @param pageSize
	 * @param req
	 * @return
	 */
	//@AutoLog(value = "词书表-分页列表查询")
	@ApiOperation(value="词书表-分页列表查询", notes="词书表-分页列表查询")
	@GetMapping(value = "/list")
	public Result<IPage<InzWordBooks>> queryPageList(InzWordBooks inzWordBooks,
								   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
								   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
								   HttpServletRequest req) {
        QueryWrapper<InzWordBooks> queryWrapper = QueryGenerator.initQueryWrapper(inzWordBooks, req.getParameterMap());
		Page<InzWordBooks> page = new Page<InzWordBooks>(pageNo, pageSize);
		IPage<InzWordBooks> pageList = inzWordBooksService.page(page, queryWrapper);
		return Result.OK(pageList);
	}

	 /**
	  * 分页列表查询
	  *
	  * @param req
	  * @return
	  */
	 //@AutoLog(value = "词书表-分页列表查询")
	 @ApiOperation(value="词书表-分页列表查询", notes="词书表-分页列表查询")
	 @GetMapping(value = "/chapterListAll")
	 public Result<List<InzWordBookChapter>> chapterListAll(HttpServletRequest req) {
		 List<InzWordBookChapter> pageList = inzWordBookChapterService.list();
		 return Result.OK(pageList);
	 }


	 @ApiOperation(value="词书表-分页列表查询", notes="词书表-分页列表查询")
	 @GetMapping(value = "/listAll")
	 public Result<List<InzWordBooks>> listAll(InzWordBooks inzWordBooks,
													  HttpServletRequest req) {
		 List<InzWordBooks> pageList = inzWordBooksService.list(new QueryWrapper<InzWordBooks>().lambda().orderBy(true,true,InzWordBooks::getSort));
		 return Result.OK(pageList);
	 }

	 @ApiOperation(value="词书表-根据ids查询数据", notes="词书表-根据ids查询数据")
	 @GetMapping(value = "/listByIds")
	 public Result<List<InzWordBooks>> listByIds(@RequestParam String ids,
											   HttpServletRequest req) {
		 List<InzWordBooks> pageList = inzWordBooksService.list(new QueryWrapper<InzWordBooks>().lambda().in(InzWordBooks::getId, Arrays.asList(ids.split(","))).orderBy(true,true,InzWordBooks::getSort));
		 return Result.OK(pageList);
	 }

	
	/**
	 *   添加
	 *
	 * @param inzWordBooksPage
	 * @return
	 */
	@AutoLog(value = "词书表-添加")
	@ApiOperation(value="词书表-添加", notes="词书表-添加")
    @RequiresPermissions("books:inz_word_books:add")
	@PostMapping(value = "/add")
	public Result<String> add(@RequestBody InzWordBooksPage inzWordBooksPage) {
		InzWordBooks inzWordBooks = new InzWordBooks();
		BeanUtils.copyProperties(inzWordBooksPage, inzWordBooks);
		inzWordBooks.setEducationName(inzEducationService.getById(inzWordBooks.getEducationId()).getName());

		if(inzWordBooks.getIsDefault() == 1){
			inzWordBooksService.list().forEach(item->{
				item.setIsDefault(0);
				inzWordBooksService.updateById(item);
			});
		}

		inzWordBooksService.saveMain(inzWordBooks, inzWordBooksPage.getInzWordBookChapterList());
		return Result.OK("添加成功！");
	}
	
	/**
	 *  编辑
	 *
	 * @param inzWordBooksPage
	 * @return
	 */
	@AutoLog(value = "词书表-编辑")
	@ApiOperation(value="词书表-编辑", notes="词书表-编辑")
    @RequiresPermissions("books:inz_word_books:edit")
	@RequestMapping(value = "/edit", method = {RequestMethod.PUT,RequestMethod.POST})
	public Result<String> edit(@RequestBody InzWordBooksPage inzWordBooksPage) {
		InzWordBooks inzWordBooks = new InzWordBooks();
		BeanUtils.copyProperties(inzWordBooksPage, inzWordBooks);
		InzWordBooks inzWordBooksEntity = inzWordBooksService.getById(inzWordBooks.getId());
		if(inzWordBooksEntity==null) {
			return Result.error("未找到对应数据");
		}
		if(inzWordBooks.getIsDefault() != null){
			if(inzWordBooks.getIsDefault() == 1){
				inzWordBooksService.list().forEach(item->{
					item.setIsDefault(0);
					inzWordBooksService.updateById(item);
				});
			}
		}
		inzWordBooks.setEducationName(inzEducationService.getById(inzWordBooks.getEducationId()).getName());
		inzWordBooksService.updateMain(inzWordBooks, inzWordBooksPage.getInzWordBookChapterList());
		return Result.OK("编辑成功!");
	}
	
	/**
	 *   通过id删除
	 *
	 * @param id
	 * @return
	 */
	@AutoLog(value = "词书表-通过id删除")
	@ApiOperation(value="词书表-通过id删除", notes="词书表-通过id删除")
    @RequiresPermissions("books:inz_word_books:delete")
	@DeleteMapping(value = "/delete")
	public Result<String> delete(@RequestParam(name="id",required=true) String id) {
		inzWordBooksService.delMain(id);
		return Result.OK("删除成功!");
	}
	
	/**
	 *  批量删除
	 *
	 * @param ids
	 * @return
	 */
	@AutoLog(value = "词书表-批量删除")
	@ApiOperation(value="词书表-批量删除", notes="词书表-批量删除")
    @RequiresPermissions("books:inz_word_books:deleteBatch")
	@DeleteMapping(value = "/deleteBatch")
	public Result<String> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
		this.inzWordBooksService.delBatchMain(Arrays.asList(ids.split(",")));
		return Result.OK("批量删除成功！");
	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "词书表-通过id查询")
	@ApiOperation(value="词书表-通过id查询", notes="词书表-通过id查询")
	@GetMapping(value = "/queryById")
	public Result<InzWordBooks> queryById(@RequestParam(name="id",required=true) String id) {
		InzWordBooks inzWordBooks = inzWordBooksService.getById(id);
		if(inzWordBooks==null) {
			return Result.error("未找到对应数据");
		}
		return Result.OK(inzWordBooks);

	}
	
	/**
	 * 通过id查询
	 *
	 * @param id
	 * @return
	 */
	//@AutoLog(value = "词书课节管理-通过主表ID查询")
	@ApiOperation(value="词书课节管理-通过主表ID查询", notes="词书课节管理-通过主表ID查询")
	@GetMapping(value = "/queryInzWordBookChapterByMainId")
	public Result<IPage<InzWordBookChapter>> queryInzWordBookChapterListByMainId(@RequestParam(name="id",required=true) String id) {
		List<InzWordBookChapter> inzWordBookChapterList = inzWordBookChapterService.selectByMainId(id);
		IPage <InzWordBookChapter> page = new Page<>();
		page.setRecords(inzWordBookChapterList);
		page.setTotal(inzWordBookChapterList.size());
		return Result.OK(page);
	}

	 @ApiOperation(value="词书课节管理-通过主表ID查询", notes="词书课节管理-通过主表ID查询")
	 @GetMapping(value = "/queryInzWordBookChapterByMainIds")
	 public Result<IPage<InzWordBookChapter>> queryInzWordBookChapterListByMainIds(@RequestParam(name="id[]",required=false) List<String> ids) {
		if(ids!=null){
			List<InzWordBookChapter> inzWordBookChapterList = inzWordBookChapterService.selectByMainIds(ids);
			IPage <InzWordBookChapter> page = new Page<>();
			page.setRecords(inzWordBookChapterList);
			page.setTotal(inzWordBookChapterList.size());
			return Result.OK(page);
		} else {
			return Result.ok(null);
		}

	 }

    /**
    * 导出excel
    *
    * @param request
    * @param inzWordBooks
    */
    @RequiresPermissions("books:inz_word_books:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzWordBooks inzWordBooks) {
      // Step.1 组装查询条件查询数据
      QueryWrapper<InzWordBooks> queryWrapper = QueryGenerator.initQueryWrapper(inzWordBooks, request.getParameterMap());
      LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

     //配置选中数据查询条件
      String selections = request.getParameter("selections");
      if(oConvertUtils.isNotEmpty(selections)) {
           List<String> selectionList = Arrays.asList(selections.split(","));
           queryWrapper.in("id",selectionList);
      }
      //Step.2 获取导出数据
      List<InzWordBooks>  inzWordBooksList = inzWordBooksService.list(queryWrapper);

      // Step.3 组装pageList
      List<InzWordBooksPage> pageList = new ArrayList<InzWordBooksPage>();
      for (InzWordBooks main : inzWordBooksList) {
          InzWordBooksPage vo = new InzWordBooksPage();
          BeanUtils.copyProperties(main, vo);
          List<InzWordBookChapter> inzWordBookChapterList = inzWordBookChapterService.selectByMainId(main.getId());
          vo.setInzWordBookChapterList(inzWordBookChapterList);
          pageList.add(vo);
      }

      // Step.4 AutoPoi 导出Excel
      ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
      mv.addObject(NormalExcelConstants.FILE_NAME, "词书表列表");
      mv.addObject(NormalExcelConstants.CLASS, InzWordBooksPage.class);
      mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("词书表数据", "导出人:"+sysUser.getRealname(), "词书表"));
      mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
      return mv;
    }

    /**
    * 通过excel导入数据
    *
    * @param request
    * @param response
    * @return
    */
    @RequiresPermissions("books:inz_word_books:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
      MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
      Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
      for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
          // 获取上传文件对象
          MultipartFile file = entity.getValue();
          ImportParams params = new ImportParams();
          params.setTitleRows(2);
          params.setHeadRows(1);
          params.setNeedSave(true);
          try {
              List<InzWordBooksPage> list = ExcelImportUtil.importExcel(file.getInputStream(), InzWordBooksPage.class, params);
              for (InzWordBooksPage page : list) {
                  InzWordBooks po = new InzWordBooks();
                  BeanUtils.copyProperties(page, po);
                  inzWordBooksService.saveMain(po, page.getInzWordBookChapterList());
              }
              return Result.OK("文件导入成功！数据行数:" + list.size());
          } catch (Exception e) {
              log.error(e.getMessage(),e);
              return Result.error("文件导入失败:"+e.getMessage());
          } finally {
              try {
                  file.getInputStream().close();
              } catch (IOException e) {
                  e.printStackTrace();
              }
          }
      }
      return Result.OK("文件导入失败！");
    }

}
