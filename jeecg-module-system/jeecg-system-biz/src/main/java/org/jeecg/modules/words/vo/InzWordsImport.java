package org.jeecg.modules.words.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;

/**
 * @Description: 单词管理
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
@Data
@ApiModel(value="inz_wordsPage对象", description="单词管理")
public class InzWordsImport {

	/**单词*/
	@Excel(name = "单词所属的单元", width = 15)
	@ApiModelProperty(value = "单词所属的单元")
    private String unitName;

	/**单词*/
	@Excel(name = "单词的英文", width = 15)
	@ApiModelProperty(value = "单词的英文")
	private String word;

	/**单词*/
	@Excel(name = "单词的中文", width = 15)
	@ApiModelProperty(value = "单词的中文")
	private String chWord;
	
}
