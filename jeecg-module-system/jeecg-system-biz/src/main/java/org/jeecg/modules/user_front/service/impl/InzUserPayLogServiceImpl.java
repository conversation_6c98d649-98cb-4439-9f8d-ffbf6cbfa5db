package org.jeecg.modules.user_front.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.user_front.entity.InzUserPayLog;
import org.jeecg.modules.user_front.mapper.InzUserPayLogMapper;
import org.jeecg.modules.user_front.service.IInzUserPayLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
@Service
public class InzUserPayLogServiceImpl extends ServiceImpl<InzUserPayLogMapper, InzUserPayLog> implements IInzUserPayLogService {
	
	@Autowired
	private InzUserPayLogMapper inzUserPayLogMapper;
	
	@Override
	public List<InzUserPayLog> selectByMainId(String mainId) {
		return inzUserPayLogMapper.selectByMainId(mainId);
	}
}
