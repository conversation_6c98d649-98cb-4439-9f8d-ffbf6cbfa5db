package org.jeecg.modules.user_front.service;

import org.jeecg.modules.user_front.entity.InzUserPayLog;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**
 * @Description: 用户金豆记录
 * @Author: jeecg-boot
 * @Date:   2025-06-17
 * @Version: V1.0
 */
public interface IInzUserPayLogService extends IService<InzUserPayLog> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<InzUserPayLog>
	 */
	public List<InzUserPayLog> selectByMainId(String mainId);
}
