package org.jeecg.modules.words.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.words.entity.InzWordCollocations;
import org.jeecg.modules.words.entity.InzWords;
import org.jeecg.modules.words.entity.InzWordsEtymology;
import org.jeecg.modules.words.vo.InzWordsImport;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * @Description: 单词管理
 * @Author: jeecg-boot
 * @Date: 2025-02-07
 * @Version: V1.0
 */
public interface IInzWordsService extends IService<InzWords> {

    /**
     * 添加一对多
     *
     * @param inzWords
     * @param inzWordCollocationsList
     * @param inzWordsEtymologyList
     */
    public void saveMain(InzWords inzWords, List<InzWordCollocations> inzWordCollocationsList, List<InzWordsEtymology> inzWordsEtymologyList);

    /**
     * 修改一对多
     *
     * @param inzWords
     * @param inzWordCollocationsList
     * @param inzWordsEtymologyList
     */
    public void updateMain(InzWords inzWords, List<InzWordCollocations> inzWordCollocationsList, List<InzWordsEtymology> inzWordsEtymologyList);

    /**
     * 删除一对多
     *
     * @param id
     */
    public void delMain(String id);

    /**
     * 批量删除一对多
     *
     * @param idList
     */
    public void delBatchMain(Collection<? extends Serializable> idList);

    public Map<String, String> savePrompt(String prompt);

    void handleData(List<InzWordsImport> list, String bookId,String fileName);

    void handleBatch(List<String> list);

    void handleBatchByWords(List<String> list);

    void scheduledFixTask();


    void delMainByBookId(String id);

    void genderChineseAudio();

    void synchronousWords(List<String> list);

    void handleUniData();
}
