package org.jeecg.modules.words.service;

import com.baomidou.mybatisplus.extension.service.IService;
import org.jeecg.modules.words.entity.InzWordCollocations;

import java.util.List;

/**
 * @Description: 存储固定搭配
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
public interface IInzWordCollocationsService extends IService<InzWordCollocations> {

	/**
	 * 通过主表id查询子表数据
	 *
	 * @param mainId 主表id
	 * @return List<InzWordCollocations>
	 */
	public List<InzWordCollocations> selectByMainId(String mainId);

	int insert(InzWordCollocations entity);

	boolean deleteByMainId(String id);
}
