package org.jeecg.modules.words.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.jeecg.common.api.vo.Result;
import org.jeecg.common.aspect.annotation.AutoLog;
import org.jeecg.common.system.query.QueryGenerator;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.oConvertUtils;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.service.IInzWordBooksService;
import org.jeecg.modules.words.entity.InzWordCollocations;
import org.jeecg.modules.words.entity.InzWords;
import org.jeecg.modules.words.entity.InzWordsEtymology;
import org.jeecg.modules.words.service.IInzWordCollocationsService;
import org.jeecg.modules.words.service.IInzWordsEtymologyService;
import org.jeecg.modules.words.service.IInzWordsService;
import org.jeecg.modules.words.vo.InzWordsImport;
import org.jeecg.modules.words.vo.InzWordsPage;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;
import org.jeecg.modules.books.entity.InzWordBookChapter;
import org.jeecg.modules.books.service.IInzWordBookChapterService;

/**
 * @Description: 单词管理
 * @Author: jeecg-boot
 * @Date: 2025-02-07
 * @Version: V1.0
 */
@Api(tags = "单词管理")
@RestController
@RequestMapping("/words/inzWords")
@Slf4j
public class InzWordsController {
    @Autowired
    private IInzWordsService inzWordsService;
    @Autowired
    private IInzWordCollocationsService inzWordCollocationsService;
    @Autowired
    private IInzWordsEtymologyService inzWordsEtymologyService;
    @Autowired
    private IInzWordBooksService inzWordBooksService;
    @Autowired
    private IInzWordBookChapterService iInzWordBookChapterService;
    @Autowired
    private RedisUtil redisUtil;


    /**
     * 分页列表查询
     *
     * @param inzWords
     * @param pageNo
     * @param pageSize
     * @param req
     * @return
     */
    //@AutoLog(value = "单词管理-分页列表查询")
    @ApiOperation(value = "单词管理-分页列表查询", notes = "单词管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<InzWords>> queryPageList(InzWords inzWords,
                                                 @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                 HttpServletRequest req) {
        QueryWrapper<InzWords> queryWrapper = QueryGenerator.initQueryWrapper(inzWords, req.getParameterMap());
        if (inzWords.getGenerateStatus() != null) {
            if (1 == inzWords.getGenerateStatus()) {
                queryWrapper.lambda().isNull(InzWords::getUkIpa);
            } else {
                queryWrapper.lambda().isNotNull(InzWords::getUkIpa);
            }
        }
        if (StringUtils.isNoneBlank(inzWords.getSortBy())) {
            queryWrapper.orderByAsc("LEFT(word, 1)");
        } else {
            queryWrapper.orderByAsc("create_time");
        }

        Page<InzWords> page = new Page<InzWords>(pageNo, pageSize);
        IPage<InzWords> pageList = inzWordsService.page(page, queryWrapper);
        return Result.OK(pageList);
    }

    /**
     * 添加
     *
     * @param inzWordsPage
     * @return
     */
    @AutoLog(value = "单词管理-添加")
    @ApiOperation(value = "单词管理-添加", notes = "单词管理-添加")
    @RequiresPermissions("words:inz_words:add")
    @PostMapping(value = "/add")
    public Result<String> add(@RequestBody InzWordsPage inzWordsPage) {
        InzWords inzWords = new InzWords();
        BeanUtils.copyProperties(inzWordsPage, inzWords);
        inzWordsService.saveMain(inzWords, inzWordsPage.getInzWordCollocationsList(), inzWordsPage.getInzWordsEtymologyList());
        return Result.OK("添加成功！");
    }

    /**
     * 编辑
     *
     * @param inzWordsPage
     * @return
     */
    @AutoLog(value = "单词管理-编辑")
    @ApiOperation(value = "单词管理-编辑", notes = "单词管理-编辑")
    @RequiresPermissions("words:inz_words:edit")
    @RequestMapping(value = "/edit", method = {RequestMethod.PUT, RequestMethod.POST})
    public Result<String> edit(@RequestBody InzWordsPage inzWordsPage) {
        InzWords inzWords = new InzWords();
        BeanUtils.copyProperties(inzWordsPage, inzWords);
        InzWords inzWordsEntity = inzWordsService.getById(inzWords.getId());
        if (inzWordsEntity == null) {
            return Result.error("未找到对应数据");
        }
        inzWordsService.updateMain(inzWords, inzWordsPage.getInzWordCollocationsList(), inzWordsPage.getInzWordsEtymologyList());
        return Result.OK("编辑成功!");
    }

    /**
     * 通过id删除
     *
     * @param id
     * @return
     */
    @AutoLog(value = "单词管理-通过id删除")
    @ApiOperation(value = "单词管理-通过id删除", notes = "单词管理-通过id删除")
    @RequiresPermissions("words:inz_words:delete")
    @DeleteMapping(value = "/delete")
    public Result<String> delete(@RequestParam(name = "id", required = true) String id) {
        inzWordsService.delMain(id);
        return Result.OK("删除成功!");
    }

    /**
     * 批量删除
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单词管理-批量删除")
    @ApiOperation(value = "单词管理-批量删除", notes = "单词管理-批量删除")
    @RequiresPermissions("words:inz_words:deleteBatch")
    @DeleteMapping(value = "/deleteBatch")
    public Result<String> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzWordsService.delBatchMain(Arrays.asList(ids.split(",")));
        return Result.OK("批量删除成功！");
    }

    /**
     * 批量解析单词
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单词管理-批量解析单词")
    @ApiOperation(value = "单词管理-批量解析单词", notes = "单词管理-批量解析单词")
    @RequiresPermissions("words:inz_words:handleBatch")
    @PostMapping(value = "/handleBatch")
    public Result<String> handleBatch(@RequestParam(name = "ids", required = true) String ids) {
        this.inzWordsService.handleBatch(Arrays.asList(ids.split(",")));
        return Result.OK("批量解析单词提交成功，正在解析！");
    }

    /**
     * 根据单词拆分单词跟自然拼读重新生成音频
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单词管理-批量解析单词")
    @ApiOperation(value = "单词管理-批量解析单词", notes = "单词管理-批量解析单词")
//    @RequiresPermissions("words:inz_words:handleBatchByWords")
    @PostMapping(value = "/handleBatchByWords")
    public Result<String> handleBatchByWords(@RequestParam(name = "ids", required = true) String ids) {
        this.inzWordsService.handleBatchByWords(Arrays.asList(ids.split(",")));
        return Result.OK("批量解析单词提交成功，正在解析！");
    }


    /**
     * 根据单词拆分单词跟自然拼读重新生成音频
     *
     * @param ids
     * @return
     */
    @AutoLog(value = "单词管理-批量解析单词")
    @ApiOperation(value = "单词管理-批量解析单词", notes = "单词管理-批量解析单词")
//    @RequiresPermissions("words:inz_words:handleBatchByWords")
    @PostMapping(value = "/synchronousWordsData")
    public Result<String> synchronousWordsData(@RequestParam(name = "ids", required = true) String ids) {
        this.inzWordsService.synchronousWords(Arrays.asList(ids.split(",")));
        return Result.OK("批量解析单词提交成功，正在解析！");
    }


    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "单词管理-通过id查询")
    @ApiOperation(value = "单词管理-通过id查询", notes = "单词管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<InzWords> queryById(@RequestParam(name = "id", required = true) String id) {
        InzWords inzWords = inzWordsService.getById(id);
        if (inzWords == null) {
            return Result.error("未找到对应数据");
        }
        return Result.OK(inzWords);

    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "存储固定搭配-通过主表ID查询")
    @ApiOperation(value = "存储固定搭配-通过主表ID查询", notes = "存储固定搭配-通过主表ID查询")
    @GetMapping(value = "/queryInzWordCollocationsByMainId")
    public Result<IPage<InzWordCollocations>> queryInzWordCollocationsListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<InzWordCollocations> inzWordCollocationsList = inzWordCollocationsService.selectByMainId(id);
        IPage<InzWordCollocations> page = new Page<>();
        page.setRecords(inzWordCollocationsList);
        page.setTotal(inzWordCollocationsList.size());
        return Result.OK(page);
    }

    /**
     * 通过id查询
     *
     * @param id
     * @return
     */
    //@AutoLog(value = "存储词源信息-通过主表ID查询")
    @ApiOperation(value = "存储词源信息-通过主表ID查询", notes = "存储词源信息-通过主表ID查询")
    @GetMapping(value = "/queryInzWordsEtymologyByMainId")
    public Result<IPage<InzWordsEtymology>> queryInzWordsEtymologyListByMainId(@RequestParam(name = "id", required = true) String id) {
        List<InzWordsEtymology> inzWordsEtymologyList = inzWordsEtymologyService.selectByMainId(id);
        IPage<InzWordsEtymology> page = new Page<>();
        page.setRecords(inzWordsEtymologyList);
        page.setTotal(inzWordsEtymologyList.size());
        return Result.OK(page);
    }

    /**
     * 导出excel
     *
     * @param request
     * @param inzWords
     */
    @RequiresPermissions("words:inz_words:exportXls")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, InzWords inzWords) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<InzWords> queryWrapper = QueryGenerator.initQueryWrapper(inzWords, request.getParameterMap());
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        //配置选中数据查询条件
        String selections = request.getParameter("selections");
        if (oConvertUtils.isNotEmpty(selections)) {
            List<String> selectionList = Arrays.asList(selections.split(","));
            queryWrapper.in("id", selectionList);
        }
        //Step.2 获取导出数据
        List<InzWords> inzWordsList = inzWordsService.list(queryWrapper);

        // Step.3 组装pageList
        List<InzWordsPage> pageList = new ArrayList<InzWordsPage>();
        for (InzWords main : inzWordsList) {
            InzWordsPage vo = new InzWordsPage();
            BeanUtils.copyProperties(main, vo);
            //            List<InzWordCollocations> inzWordCollocationsList = inzWordCollocationsService.selectByMainId(main.getId());
//            vo.setInzWordCollocationsList(inzWordCollocationsList);
//            List<InzWordsEtymology> inzWordsEtymologyList = inzWordsEtymologyService.selectByMainId(main.getId());
//            vo.setInzWordsEtymologyList(inzWordsEtymologyList);
            pageList.add(vo);
        }


        // Step.4 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        mv.addObject(NormalExcelConstants.FILE_NAME, "单词管理列表");
        mv.addObject(NormalExcelConstants.CLASS, InzWordsPage.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("单词管理数据", "导出人:" + sysUser.getRealname(), "单词管理"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    @Transactional
//    @RequiresPermissions("words:inz_words:importExcel")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        log.info("========== 开始导入单词Excel并生成短文 ==========");
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            // 获取上传文件对象
            MultipartFile file = entity.getValue();
            ImportParams params = new ImportParams();
            params.setSheetName("Sheet1");
            params.setNeedSave(true);
            params.setKeyIndex(0);
            try {
                // 1. 导入 Excel 数据
                log.info("开始解析Excel文件: {}", file.getOriginalFilename());
                List<InzWordsImport> list = ExcelImportUtil.importExcel(file.getInputStream(), InzWordsImport.class, params);

                // 2. 如果 Excel 文件没有数据或者sheet为空，则直接返回错误
                if (list == null || list.isEmpty()) {
                    log.error("Excel文件中没有数据或工作表为空！");
                    return Result.error("Excel文件中没有数据或工作表为空！");
                }
                log.info("成功从Excel中解析出 {} 条单词数据", list.size());

                // 3. 过滤空行（这里示例以 word 和 unitName 均为空作为空行判断条件，可根据实际情况调整）
                List<InzWordsImport> filteredList = list.stream()
                        .filter(item -> StringUtils.isNotBlank(item.getWord()) || StringUtils.isNotBlank(item.getUnitName()))
                        .collect(Collectors.toList());

                if (filteredList.isEmpty()) {
                    log.error("Excel文件中没有有效数据！");
                    return Result.error("Excel文件中没有有效数据！");
                }
                log.info("过滤后有效单词数据: {} 条", filteredList.size());

                // 4. 根据文件名查找或者创建词书
                String fileName = file.getOriginalFilename();
                if (fileName != null && fileName.lastIndexOf('.') > 0) {
                    fileName = fileName.substring(0, fileName.lastIndexOf('.'));
                }
                log.info("根据文件名 '{}' 查找或创建词书", fileName);
                InzWordBooks bookInfo = inzWordBooksService.getOne(
                        new QueryWrapper<InzWordBooks>().lambda().eq(InzWordBooks::getName, fileName));
                String bookId = "";
                if (bookInfo != null) {
                    bookId = bookInfo.getId();
                    log.info("找到现有词书: ID={}, 名称={}", bookId, fileName);
                } else {
                    InzWordBooks inzWordsBooks = new InzWordBooks();
                    inzWordsBooks.setName(fileName);
                    inzWordBooksService.save(inzWordsBooks);
                    bookId = inzWordsBooks.getId();
                    log.info("创建新词书: ID={}, 名称={}", bookId, fileName);
                }

                // 5. 调用业务方法处理导入的数据
                log.info("开始处理导入的单词数据并生成短文...");
                try {
                    inzWordsService.handleData(filteredList, bookId, fileName);
                    log.info("单词数据处理完成，短文生成任务已提交");
                    
                    // 对词书章节进行重新排序
                    log.info("开始对词书章节进行重新排序...");
                    try {
                        List<InzWordBookChapter> chapters = iInzWordBookChapterService.selectByMainId(bookId);
                        log.info("找到 {} 个需要重排序的章节", chapters.size());

                        if (chapters.isEmpty()) {
                            log.info("没有找到任何章节，跳过重排序");
                        } else {

                        // 使用混合前缀智能排序
                        chapters = sortChaptersByMixedPrefix(chapters);
                        
                        // 更新排序字段
                        for (int i = 0; i < chapters.size(); i++) {
                            InzWordBookChapter chapter = chapters.get(i);
                            chapter.setSort(i + 1); // 排序从1开始
                            iInzWordBookChapterService.updateById(chapter);
                            log.info("更新章节排序: {} -> {}", chapter.getName(), chapter.getSort());
                        }

                        // 输出重排序后的完整章节列表
                        log.info("词书章节重排序完成，最终章节顺序如下：");
                        for (int i = 0; i < chapters.size(); i++) {
                            InzWordBookChapter chapter = chapters.get(i);
                            log.info("第{}章: {} (sort={})", i + 1, chapter.getName(), chapter.getSort());
                        }
                        log.info("共重排序 {} 个章节", chapters.size());
                        }
                    } catch (Exception e) {
                        log.error("章节排序过程中出错: {}", e.getMessage(), e);
                    }
                } catch (Exception e) {
                    log.error("处理单词数据时出错: {}", e.getMessage(), e);
                    return Result.error("处理单词数据时出错: " + e.getMessage());
                }

                log.info("========== 单词Excel导入和短文生成任务完成 ==========");
                return Result.OK("文件导入成功！数据行数:" + filteredList.size() + "，正在生成单词数据和短文中...");
            } catch (Exception e) {
                log.error("文件导入失败: {}", e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error("关闭文件流时出错", e);
                }
            }
        }
        log.error("未找到上传的文件");
        return Result.error("文件导入失败！未找到上传的文件");
    }


    /**
     * 导出所有单词数据为 JSON 文件
     *
     * @param request  HttpServletRequest
     * @param inzWords 单词查询条件
     * @return 返回 JSON 数据或文件下载
     */
//    @RequiresPermissions("words:inz_words:exportJson")
    @RequestMapping(value = "/exportJson")
    public Result<String> exportJson(HttpServletRequest request, HttpServletResponse response, InzWords inzWords) {
        // Step.1 组装查询条件查询数据
        QueryWrapper<InzWords> queryWrapper = QueryGenerator.initQueryWrapper(inzWords, request.getParameterMap());

        List<InzWords> inzWordsList = inzWordsService.list(queryWrapper);
        List<InzWordsPage> pageList = inzWordsList.parallelStream().map(main -> {
            InzWordsPage vo = new InzWordsPage();
            BeanUtils.copyProperties(main, vo);

            vo.setInzWordCollocationsList(inzWordCollocationsService.selectByMainId(main.getId()));
            vo.setInzWordsEtymologyList(inzWordsEtymologyService.selectByMainId(main.getId()));

            return vo;
        }).collect(Collectors.toList());
        ObjectMapper objectMapper = new ObjectMapper();

        try {
            // 设置响应内容类型为 JSON
            response.setContentType("application/json");
            // 设置文件下载的文件名
            response.setHeader("Content-Disposition", "attachment; filename=inzWordsData.json");

            // 将 JSON 数据写入响应的输出流
            objectMapper.writeValue(response.getOutputStream(), pageList);

            // 关闭响应流
            response.getOutputStream().flush();
        } catch (IOException e) {
            log.error("JSON 文件导出失败", e);
            return Result.error("导出失败: " + e.getMessage());
        }

        // Step.4 返回结果（文件路径或者直接下载）
        return Result.OK("JSON 文件导出成功！");
    }


    @Scheduled(cron = "0 * * * * ?")
    public void scheduledFixTask() {
        String open = redisUtil.get("SuperWords:config:IS_OPEN_ANALYSIS").toString();
        if ("1".equals(open)) {
            log.info("开始执行数据修复任务，111111");
            inzWordsService.scheduledFixTask();
            log.info("数据修复任务完成");
        } else {
            log.info("数据修复任务未开启");
        }
    }

    @Transactional
    @RequestMapping(value = "/chineseAudio", method = RequestMethod.POST)
    public void chineseAudio(HttpServletRequest request, HttpServletResponse response) {
        inzWordsService.genderChineseAudio();
    }


    @RequestMapping(value = "/handleUniData", method = RequestMethod.POST)
    public void handleUniData(HttpServletRequest request, HttpServletResponse response) {
        inzWordsService.handleUniData();
    }

    /**
     * 从章节名称中提取数字
     * 支持多种格式：Unit 1, Unit1, 单元1, 第1章, 第一章, Chapter 1, Chapter1 等
     *
     * @param chapterName 章节名称
     * @return 提取的数字，如果无法提取则返回null
     */
    private Integer extractChapterNumber(String chapterName) {
        if (chapterName == null || chapterName.trim().isEmpty()) {
            return null;
        }
        
        // 直接匹配纯数字
        if (chapterName.matches("\\d+")) {
            return Integer.parseInt(chapterName);
        }
        
        // 匹配常见格式中的数字部分
        String[] patterns = {
            ".*?(\\d+).*", // 匹配任何包含数字的字符串，如 "Unit 1", "Unit1", "单元1", "第1章"
            ".*?第([一二三四五六七八九十]+)章.*", // 匹配中文数字，如 "第一章"
            ".*?([一二三四五六七八九十]+).*" // 匹配中文数字，如 "一单元"
        };
        
        for (String pattern : patterns) {
            java.util.regex.Pattern p = java.util.regex.Pattern.compile(pattern);
            java.util.regex.Matcher m = p.matcher(chapterName);
            if (m.matches()) {
                String match = m.group(1);
                
                // 如果匹配的是阿拉伯数字
                if (match.matches("\\d+")) {
                    return Integer.parseInt(match);
                } 
                // 如果匹配的是中文数字
                else {
                    return convertChineseNumberToArabic(match);
                }
            }
        }
        
        return null;
    }
    
    /**
     * 将中文数字转换为阿拉伯数字
     *
     * @param chineseNumber 中文数字字符串
     * @return 阿拉伯数字
     */
    private Integer convertChineseNumberToArabic(String chineseNumber) {
        if (chineseNumber == null || chineseNumber.isEmpty()) {
            return null;
        }
        
        Map<Character, Integer> chineseMap = new HashMap<>();
        chineseMap.put('一', 1);
        chineseMap.put('二', 2);
        chineseMap.put('三', 3);
        chineseMap.put('四', 4);
        chineseMap.put('五', 5);
        chineseMap.put('六', 6);
        chineseMap.put('七', 7);
        chineseMap.put('八', 8);
        chineseMap.put('九', 9);
        chineseMap.put('十', 10);
        
        // 简单的中文数字转换，仅支持1-99
        if (chineseNumber.length() == 1) {
            return chineseMap.getOrDefault(chineseNumber.charAt(0), 0);
        } else if (chineseNumber.length() == 2) {
            if (chineseNumber.charAt(0) == '十') {
                return 10 + chineseMap.getOrDefault(chineseNumber.charAt(1), 0);
            } else {
                return chineseMap.getOrDefault(chineseNumber.charAt(0), 0) * 10;
            }
        } else if (chineseNumber.length() == 3 && chineseNumber.charAt(1) == '十') {
            return chineseMap.getOrDefault(chineseNumber.charAt(0), 0) * 10 + 
                   chineseMap.getOrDefault(chineseNumber.charAt(2), 0);
        }
        
        return 0; // 默认返回0
    }

    /**
     * 混合前缀章节智能排序
     * 支持不同前缀的章节混合排序，如：unit 1, unit 2, chapter 1, unit 3
     * 排序规则：
     * 1. 按前缀分组（unit组、chapter组等）
     * 2. 组内按数字排序
     * 3. 组间按首次出现顺序排序
     *
     * @param chapters 原始章节列表
     * @return 排序后的章节列表
     */
    private List<InzWordBookChapter> sortChaptersByMixedPrefix(List<InzWordBookChapter> chapters) {
        if (chapters == null || chapters.isEmpty()) {
            return chapters;
        }

        // 用于记录前缀首次出现的顺序
        Map<String, Integer> prefixOrder = new LinkedHashMap<>();
        // 按前缀分组
        Map<String, List<InzWordBookChapter>> prefixGroups = new LinkedHashMap<>();

        log.info("开始混合前缀章节排序，原始章节顺序：");
        for (int i = 0; i < chapters.size(); i++) {
            String chapterName = chapters.get(i).getName();
            log.info("  原始第{}位: {}", i + 1, chapterName);

            // 提取前缀
            String prefix = extractChapterPrefix(chapterName);

            // 记录前缀首次出现的顺序
            if (!prefixOrder.containsKey(prefix)) {
                prefixOrder.put(prefix, prefixOrder.size());
                prefixGroups.put(prefix, new ArrayList<>());
                log.info("  发现新前缀: {} (顺序: {})", prefix, prefixOrder.get(prefix) + 1);
            }

            // 将章节加入对应前缀组
            prefixGroups.get(prefix).add(chapters.get(i));
        }

        // 对每个前缀组内部进行排序
        for (Map.Entry<String, List<InzWordBookChapter>> entry : prefixGroups.entrySet()) {
            String prefix = entry.getKey();
            List<InzWordBookChapter> group = entry.getValue();

            log.info("对前缀组 '{}' 进行内部排序，包含 {} 个章节", prefix, group.size());

            // 组内按数字排序
            group.sort((c1, c2) -> {
                Integer num1 = extractChapterNumber(c1.getName());
                Integer num2 = extractChapterNumber(c2.getName());

                if (num1 != null && num2 != null) {
                    return num1.compareTo(num2);
                }

                // 如果无法提取数字，按字符串排序
                return c1.getName().compareTo(c2.getName());
            });

            // 输出组内排序结果
            for (int i = 0; i < group.size(); i++) {
                log.info("  组内第{}位: {}", i + 1, group.get(i).getName());
            }
        }

        // 按前缀首次出现顺序合并所有组
        List<InzWordBookChapter> sortedChapters = new ArrayList<>();
        for (String prefix : prefixOrder.keySet()) {
            sortedChapters.addAll(prefixGroups.get(prefix));
        }

        log.info("混合前缀排序完成，最终顺序：");
        for (int i = 0; i < sortedChapters.size(); i++) {
            log.info("  最终第{}位: {}", i + 1, sortedChapters.get(i).getName());
        }

        return sortedChapters;
    }

    /**
     * 提取章节名称的前缀
     * 例如：
     * "Unit 1" -> "unit"
     * "Chapter 2" -> "chapter"
     * "单元 3" -> "单元"
     * "第1章" -> "第章"
     *
     * @param chapterName 章节名称
     * @return 前缀（转换为小写）
     */
    private String extractChapterPrefix(String chapterName) {
        if (chapterName == null || chapterName.trim().isEmpty()) {
            return "unknown";
        }

        String name = chapterName.trim().toLowerCase();

        // 常见的前缀模式
        String[] patterns = {
            "unit\\s*\\d+",      // Unit 1, Unit1
            "chapter\\s*\\d+",   // Chapter 1, Chapter1
            "lesson\\s*\\d+",    // Lesson 1, Lesson1
            "part\\s*\\d+",      // Part 1, Part1
            "section\\s*\\d+",   // Section 1, Section1
            "单元\\s*\\d+",       // 单元1, 单元 1
            "课\\s*\\d+",         // 课1, 课 1
            "第\\s*\\d+\\s*章",   // 第1章, 第 1 章
            "第\\s*\\d+\\s*课",   // 第1课, 第 1 课
            "第\\s*\\d+\\s*单元"  // 第1单元, 第 1 单元
        };

        for (String pattern : patterns) {
            if (name.matches(pattern)) {
                // 提取前缀部分
                if (name.startsWith("unit")) return "unit";
                if (name.startsWith("chapter")) return "chapter";
                if (name.startsWith("lesson")) return "lesson";
                if (name.startsWith("part")) return "part";
                if (name.startsWith("section")) return "section";
                if (name.startsWith("单元")) return "单元";
                if (name.startsWith("课")) return "课";
                if (name.startsWith("第") && name.contains("章")) return "第章";
                if (name.startsWith("第") && name.contains("课")) return "第课";
                if (name.startsWith("第") && name.contains("单元")) return "第单元";
            }
        }

        // 如果没有匹配到已知模式，尝试提取第一个单词作为前缀
        String[] words = name.split("\\s+");
        if (words.length > 0) {
            // 移除数字，保留字母部分作为前缀
            String firstWord = words[0].replaceAll("\\d+", "").trim();
            if (!firstWord.isEmpty()) {
                return firstWord;
            }
        }

        // 默认返回原始名称作为前缀
        return name;
    }


}
