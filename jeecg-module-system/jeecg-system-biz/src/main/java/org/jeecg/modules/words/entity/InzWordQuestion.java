package org.jeecg.modules.words.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 精选试题详情
 * @Author: jeecg-boot
 * @Date:   2025-01-25
 * @Version: V1.0
 */
@Data
@TableName("inz_word_question")
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = false)
@ApiModel(value="inz_word_question对象", description="精选试题详情")
public class InzWordQuestion implements Serializable {
    private static final long serialVersionUID = 1L;

	/**主键*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "主键")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**问题*/
	@Excel(name = "问题", width = 15)
    @ApiModelProperty(value = "问题")
    private String question;
	/**所属试卷id*/
	@Excel(name = "所属试卷id", width = 15)
    @ApiModelProperty(value = "所属试卷id")
    private String wordId;
    /**图书id */
    @Excel(name = "图书id", width = 15)
    @ApiModelProperty(value = "图书id")
    private String bookId;
    /**章节id**/
    @Excel(name = "章节id", width = 15)
    @ApiModelProperty(value = "章节id")
    private String chapterId;
	/**选项*/
	@Excel(name = "选项", width = 15)
    @ApiModelProperty(value = "选项")
    private String options;
	/**正确答案*/
	@Excel(name = "正确答案", width = 15)
    @ApiModelProperty(value = "正确答案")
    private String trueAnswer;
	/**问题类型*/
	@Excel(name = "问题类型", width = 15)
    @ApiModelProperty(value = "问题类型")
    private String questionType;
	/**其他内容*/
	@Excel(name = "其他内容", width = 15)
    @ApiModelProperty(value = "其他内容")
    private String otherContent;
	/**上级问题id*/
	@Excel(name = "上级问题id", width = 15)
    @ApiModelProperty(value = "上级问题id")
    private String parentId;

    @Excel(name = "上级问题id", width = 15)
    @ApiModelProperty(value = "上级问题id")
    private Integer sort;
}
