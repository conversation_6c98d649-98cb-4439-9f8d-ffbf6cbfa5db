package org.jeecg.modules.words.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Description: 单词管理
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
@ApiModel(value="inz_words对象", description="单词管理")
@Data
@TableName("inz_words")
public class InzWords implements Serializable {
    private static final long serialVersionUID = 1L;

	/**id*/
	@TableId(type = IdType.ASSIGN_UUID)
    @ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
    @ApiModelProperty(value = "创建人")
    private String createBy;
	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
    @ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
    @ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**单词*/
	@Excel(name = "单词", width = 15)
    @ApiModelProperty(value = "单词")
    private String word;
	/**英式发音音标*/
	@Excel(name = "英式发音音标", width = 15)
    @ApiModelProperty(value = "英式发音音标")
    private String ukIpa;
	/**美式发音音标*/
	@Excel(name = "美式发音音标", width = 15)
    @ApiModelProperty(value = "美式发音音标")
    private String usIpa;
	/**状态 1正常 0停用*/
    @ApiModelProperty(value = "状态")
    private Integer status;
	/**单独音标*/
    @ApiModelProperty(value = "单独音标")
    private String pronunciationGuide;
	/**拆分含义*/
	@Excel(name = "拆分含义", width = 15)
    @ApiModelProperty(value = "拆分含义")
    private String rootParticlesMean;
	/**谐音*/
	@Excel(name = "谐音", width = 15)
    @ApiModelProperty(value = "谐音")
    private String homophonic;

    /**谐音*/
    @Excel(name = "音频文件", width = 15)
    @ApiModelProperty(value = "音频文件")
    private String audioUrl;


    /**谐音*/
    @Excel(name = "图书id", width = 15)
    @ApiModelProperty(value = "图书id")
    private String bookId;

    /**谐音*/
    @Excel(name = "章节id", width = 15)
    @ApiModelProperty(value = "章节id")
    private String chapterId;

    @Excel(name = "美式发音音频文件", width = 15)
    @ApiModelProperty(value = "美式发音音频文件")
    private String usAudioUrl;

    @Excel(name = "拆分单词音频文件", width = 15)
    @ApiModelProperty(value = "拆分单词音频文件")
    private String breakdownAudioUrl;

    @Excel(name = "自然拼读音频文件", width = 15)
    @ApiModelProperty(value = "自然拼读音频文件")
    private String naturalAudioUrl;

    @ApiModelProperty(value = "解析次数")
    private Integer analysisCount;


    @TableField(exist = false)
    private Integer generateStatus;

    @TableField(exist = false)
    private String sortBy;

}
