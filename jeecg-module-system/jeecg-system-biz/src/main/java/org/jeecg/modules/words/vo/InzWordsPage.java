package org.jeecg.modules.words.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.jeecg.modules.words.entity.InzWordCollocations;
import org.jeecg.modules.words.entity.InzWordsEtymology;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;
import java.util.List;

/**
 * @Description: 单词管理
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
@Data
@ApiModel(value="inz_wordsPage对象", description="单词管理")
public class InzWordsPage {

	/**id*/
	@ApiModelProperty(value = "id")
    private String id;
	/**创建人*/
	@ApiModelProperty(value = "创建人")
    private String createBy;

	@ApiModelProperty(value = "图书id")
	private String bookId;

	@ApiModelProperty(value = "章节id")
	private String chapterId;

	/**创建日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建日期")
    private Date createTime;
	/**更新人*/
	@ApiModelProperty(value = "更新人")
    private String updateBy;
	/**更新日期*/
	@JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "更新日期")
    private Date updateTime;
	/**所属部门*/
	@ApiModelProperty(value = "所属部门")
    private String sysOrgCode;
	/**单词*/
	@Excel(name = "单词", width = 15)
	@ApiModelProperty(value = "单词")
    private String word;
	/**英式发音音标*/
	@Excel(name = "英式发音音标", width = 15)
	@ApiModelProperty(value = "英式发音音标")
    private String ukIpa;
	/**美式发音音标*/
	@Excel(name = "美式发音音标", width = 15)
	@ApiModelProperty(value = "美式发音音标")
    private String usIpa;
	/**状态 1正常 0停用*/
	@ApiModelProperty(value = "状态 1正常 0停用")
    @Excel(name = "状态 1正常 0停用", width = 15,replace = {"是_Y","否_N"} )
    private Integer status;
	/**单独音标*/
	@Excel(name = "单独音标", width = 15)
	@ApiModelProperty(value = "单独音标")
    private String pronunciationGuide;
	/**拆分含义*/
	@Excel(name = "拆分含义", width = 15)
	@ApiModelProperty(value = "拆分含义")
    private String rootParticlesMean;
	/**谐音*/
	@Excel(name = "谐音", width = 15)
	@ApiModelProperty(value = "谐音")
    private String homophonic;
	
	@ApiModelProperty(value = "存储固定搭配")
	private List<InzWordCollocations> inzWordCollocationsList;
	@ApiModelProperty(value = "存储词源信息")
	private List<InzWordsEtymology> inzWordsEtymologyList;
	
}
