package org.jeecg.modules.words.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.jeecg.modules.words.entity.InzWordCollocations;
import org.jeecg.modules.words.mapper.InzWordCollocationsMapper;
import org.jeecg.modules.words.service.IInzWordCollocationsService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description: 存储固定搭配
 * @Author: jeecg-boot
 * @Date:   2025-02-07
 * @Version: V1.0
 */
@Service
public class InzWordCollocationsServiceImpl extends ServiceImpl<InzWordCollocationsMapper, InzWordCollocations> implements IInzWordCollocationsService {
	

	@Override
	public List<InzWordCollocations> selectByMainId(String mainId) {
		return baseMapper.selectByMainId(mainId);
	}

	@Override
	public int insert(InzWordCollocations entity) {
		return baseMapper.insert(entity);
	}

	@Override
	public boolean deleteByMainId(String id) {
		return baseMapper.deleteByMainId(id);
	}
}
