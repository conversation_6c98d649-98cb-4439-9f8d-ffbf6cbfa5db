package org.jeecg.modules.words.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.jeecg.common.constant.ThirdRequestConstant;
import org.jeecg.common.system.base.entity.TestQuestionEntity;
import org.jeecg.common.system.base.entity.WordEntity;
import org.jeecg.common.util.RedisUtil;
import org.jeecg.common.util.ThirdRequestUtils;
import org.jeecg.modules.book_words.entity.InzBookWords;
import org.jeecg.modules.book_words.service.IInzBookWordsService;
import org.jeecg.modules.books.entity.InzWordBookChapter;
import org.jeecg.modules.books.entity.InzWordBooks;
import org.jeecg.modules.books.service.IInzWordBookChapterService;
import org.jeecg.modules.books.service.IInzWordBooksService;
import org.jeecg.modules.phonetic_symbols.entity.InzPhoneticSymbols;
import org.jeecg.modules.phonetic_symbols.service.IInzPhoneticSymbolsService;
import org.jeecg.modules.words.entity.InzWordCollocations;
import org.jeecg.modules.words.entity.InzWordQuestion;
import org.jeecg.modules.words.entity.InzWords;
import org.jeecg.modules.words.entity.InzWordsEtymology;
import org.jeecg.modules.words.mapper.InzWordsMapper;
import org.jeecg.modules.words.service.IInzWordCollocationsService;
import org.jeecg.modules.words.service.IInzWordQuestionService;
import org.jeecg.modules.words.service.IInzWordsEtymologyService;
import org.jeecg.modules.words.service.IInzWordsService;
import org.jeecg.modules.words.vo.InzWordsImport;
import org.jetbrains.annotations.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.*;
import java.net.URL;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * @Description: 单词管理
 * @Author: jeecg-boot
 * @Date: 2025-02-07
 * @Version: V1.0
 */
@Service
public class InzWordsServiceImpl extends ServiceImpl<InzWordsMapper, InzWords> implements IInzWordsService {

    private static final Logger log = LoggerFactory.getLogger(InzWordsServiceImpl.class);

    @Autowired
    private IInzWordCollocationsService inzWordCollocationsService;
    @Autowired
    private IInzWordsEtymologyService iInzWordsEtymologyService;
    @Autowired
    private IInzWordBookChapterService iInzWordBookChapterService;
    @Autowired
    @Lazy
    private IInzBookWordsService iInzBookWordsService;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private InzWordsEtymologyServiceImpl inzWordsEtymologyServiceImpl;

    @Autowired
    private IInzWordBooksService inzWordBooksService;

    @Autowired
    private IInzPhoneticSymbolsService inzPhoneticSymbolsService;

    @Autowired
    private IInzWordQuestionService inzWordQuestionService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMain(InzWords inzWords, List<InzWordCollocations> inzWordCollocationsList, List<InzWordsEtymology> inzWordsEtymologyList) {
        baseMapper.insert(inzWords);
        inzWordBooksService.increment(inzWords.getBookId());
        if (inzWordCollocationsList != null && !inzWordCollocationsList.isEmpty()) {
            for (InzWordCollocations entity : inzWordCollocationsList) {
                //外键设置
                entity.setWordId(inzWords.getId());
                inzWordCollocationsService.insert(entity);
            }
        }
        if (inzWordsEtymologyList != null && !inzWordsEtymologyList.isEmpty()) {
            for (InzWordsEtymology entity : inzWordsEtymologyList) {
                //外键设置
                entity.setWordId(inzWords.getId());
                iInzWordsEtymologyService.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateMain(InzWords inzWords, List<InzWordCollocations> inzWordCollocationsList, List<InzWordsEtymology> inzWordsEtymologyList) {
        baseMapper.updateById(inzWords);

        //1.先删除子表数据
        inzWordCollocationsService.deleteByMainId(inzWords.getId());
        iInzWordsEtymologyService.deleteByMainId(inzWords.getId());

        //2.子表数据重新插入
        if (inzWordCollocationsList != null && !inzWordCollocationsList.isEmpty()) {
            for (InzWordCollocations entity : inzWordCollocationsList) {
                //外键设置
                entity.setWordId(inzWords.getId());
                inzWordCollocationsService.insert(entity);
            }
        }
        if (inzWordsEtymologyList != null && !inzWordsEtymologyList.isEmpty()) {
            for (InzWordsEtymology entity : inzWordsEtymologyList) {
                //外键设置
                entity.setWordId(inzWords.getId());
                iInzWordsEtymologyService.insert(entity);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        InzWords words = getById(id);
        inzWordCollocationsService.deleteByMainId(id);
        iInzWordsEtymologyService.deleteByMainId(id);
        if (StringUtils.isNotBlank(words.getBookId())) {
            inzWordBooksService.decrement(words.getBookId());
            iInzBookWordsService.deleteByWordId(id);
        }
        baseMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            InzWords words = getById(id);
            inzWordCollocationsService.deleteByMainId(id.toString());
            iInzWordsEtymologyService.deleteByMainId(id.toString());
            if (StringUtils.isNotBlank(words.getBookId())) {
                inzWordBooksService.decrement(words.getBookId());
            }
            iInzBookWordsService.deleteByWordId(String.valueOf(id));
            baseMapper.deleteById(id);
        }
    }

    @Override
    @Async
    public void handleData(List<InzWordsImport> list, String bookId, String fileName) {
        log.info("========== 开始处理导入的单词数据 ==========");
        log.info("参数: bookId={}, 单词数量={}", bookId, list.size());

        ArrayList<InzBookWords> bookWordsArrayList = new ArrayList<>();
        ArrayList<InzWords> words = new ArrayList<>();
        Map<String, String> chapterMap = new HashMap<>();
        // 新增：用于缓存每个章节下已处理的单词
        Map<String, Set<String>> chapterWordCache = new HashMap<>();
        // 新增：用于记录需要生成短文的章节ID
        Set<String> chaptersNeedArticles = new HashSet<>();

        list.forEach(item -> {
            if (StringUtils.isNotBlank(item.getWord()) || StringUtils.isNotBlank(item.getUnitName())) {
                String unitName = item.getUnitName();
                String chapterId = chapterMap.computeIfAbsent(unitName, key -> {
                    log.debug("查找或创建章节: bookId={}, unitName={}", bookId, key);
                    InzWordBookChapter chapter = iInzWordBookChapterService.getOne(new QueryWrapper<InzWordBookChapter>().lambda()
                            .eq(InzWordBookChapter::getBookId, bookId)
                            .eq(InzWordBookChapter::getName, unitName));
                    if (chapter == null) {
                        log.debug("未找到章节，创建新章节: bookId={}, unitName={}", bookId, key);
                        InzWordBookChapter newChapter = new InzWordBookChapter();
                        newChapter.setBookId(bookId);
                        newChapter.setName(unitName);
                        iInzWordBookChapterService.save(newChapter);
                        return newChapter.getId();
                    }
                    log.debug("找到现有章节: bookId={}, unitName={}, chapterId={}", bookId, key, chapter.getId());
                    return chapter.getId();
                });

                // 记录需要生成短文的章节
                chaptersNeedArticles.add(chapterId);
                log.debug("添加章节到需生成短文列表: chapterId={}", chapterId);

                // 检查当前章节下是否已存在该单词（同一批次内）
                Set<String> wordsInChapter = chapterWordCache.computeIfAbsent(chapterId, k -> new HashSet<>());
                String word = item.getWord();
                if (wordsInChapter.contains(word)) {
                    // 同一批次内重复，跳过
                    log.debug("跳过同一批次内重复的单词: word={}, chapterId={}", word, chapterId);
                    return;
                }

                // 检查数据库中是否已存在该单词在当前章节
                boolean existsInDb = exists(new QueryWrapper<InzWords>().lambda()
                        .eq(InzWords::getBookId, bookId)
                        .eq(InzWords::getChapterId, chapterId)
                        .eq(InzWords::getWord, word));
                if (existsInDb) {
                    // 数据库中已存在，跳过
                    log.debug("跳过数据库中已存在的单词: word={}, bookId={}, chapterId={}", word, bookId, chapterId);
                    return;
                }

                // 添加到缓存，避免同一批次内重复
                wordsInChapter.add(word);
                log.debug("添加单词到章节缓存: word={}, chapterId={}", word, chapterId);

                InzWords po = new InzWords();
                po.setWord(word);
                po.setBookId(bookId);
                po.setChapterId(chapterId);
                save(po);
                log.debug("保存单词到InzWords表: id={}, word={}, bookId={}, chapterId={}", po.getId(), word, bookId, chapterId);

                inzWordBooksService.increment(bookId);
                InzBookWords inzBookWords = new InzBookWords();
                inzBookWords.setBookId(bookId);
                inzBookWords.setWordId(po.getId());
                inzBookWords.setChapterId(chapterId);
                bookWordsArrayList.add(inzBookWords);
                words.add(po);
                log.debug("添加单词到批量保存列表: wordId={}, bookId={}, chapterId={}", po.getId(), bookId, chapterId);
            }
        });

        if (!bookWordsArrayList.isEmpty()) {
            log.info("批量保存 {} 个单词到InzBookWords表", bookWordsArrayList.size());
            iInzBookWordsService.saveBatch(bookWordsArrayList);

            //批量生成单词题目
            generateQuestions(words);

            // 为每个章节生成短文
            log.info("开始为 {} 个章节生成短文", chaptersNeedArticles.size());
            for (String chapterId : chaptersNeedArticles) {
                try {
                    log.info("为章节 {} 自动生成短文", chapterId);
                    // 调用生成短文的方法
                    iInzBookWordsService.generateArticlesForChapter(bookId, chapterId, fileName);
                    log.info("章节 {} 的短文生成任务已提交", chapterId);
                } catch (Exception e) {
                    log.error("为章节 {} 生成短文时出错: {}", chapterId, e.getMessage(), e);
                }
            }
            log.info("所有章节的短文生成任务已提交");
        } else {
            log.info("没有需要保存的单词，跳过生成短文");
        }
        log.info("========== 单词数据处理完成 ==========");
    }

    @Async
    protected void generateQuestions(@NotNull ArrayList<InzWords> words) {
        InzWordBooks wordBooks = inzWordBooksService.getById(words.get(0).getBookId());
        words.parallelStream().forEach(item -> {
            String sendMessage = String.format("单词：%s。题型：%s。参数答案：%s。题目数量：%s。其他需求：%s",
                    item.getWord(),
                    String.join(",", getQuestionTypes("0")),
                    "需要",
                    10,
                    "加上题目分值");
            System.out.println("生成题目message" + sendMessage);
            String thirdToken = ThirdRequestUtils.getThirdToken("infrabiz", "infrabiz", redisUtil);
            List<TestQuestionEntity> wordEntities = ThirdRequestUtils.analyzeQuestion(wordBooks.getName(), sendMessage, thirdToken, redisUtil);
            ArrayList<InzWordQuestion> inzWordQuestions = new ArrayList<>();
            int sort = 1;

            for (TestQuestionEntity wordEntity : wordEntities) {
                // 检查是否为应用题
                if ("应用题".equals(wordEntity.getQuestionType()) &&
                        wordEntity.getChildren() != null && !wordEntity.getChildren().isEmpty()) {

                    // 应用题：保存主题目
                    InzWordQuestion parentQuestion = createWordQuestion(wordEntity, item, sort++, "0");
                    inzWordQuestions.add(parentQuestion);

                    log.info("发现应用题，开始处理 {} 个子题目", wordEntity.getChildren().size());

                    // 处理children子题目
                    for (TestQuestionEntity child : wordEntity.getChildren()) {
                        InzWordQuestion childQuestion = createWordQuestion(child, item, sort++, parentQuestion.getId());
                        inzWordQuestions.add(childQuestion);
                        log.info("添加子题目: {} (parent_id: {})", child.getQuestion(), parentQuestion.getId());
                    }
                } else {
                    // 非应用题：使用原始逻辑
                    InzWordQuestion inzWordQuestion = new InzWordQuestion();
                    BeanUtils.copyProperties(wordEntity, inzWordQuestion);
                    inzWordQuestion.setSort(sort);
                    inzWordQuestion.setBookId(item.getBookId());
                    inzWordQuestion.setWordId(item.getId());
                    inzWordQuestion.setChapterId(item.getChapterId());
                    inzWordQuestion.setOptions(wordEntity.getOptions().toString());
                    inzWordQuestion.setOtherContent(wordEntity.getOptions().toString());
                    sort++;
                    inzWordQuestions.add(inzWordQuestion);
                }
            }

            inzWordQuestionService.saveBatch(inzWordQuestions);
            log.info("批量保存 {} 道题目完成", inzWordQuestions.size());
        });
    }

    /**
     * 创建InzWordQuestion对象的辅助方法
     *
     * @param entity   TestQuestionEntity对象
     * @param word     单词对象
     * @param sort     排序号
     * @param parentId 父题目ID，主题目传"0"，子题目传父题目的ID
     * @return InzWordQuestion对象
     */
    private InzWordQuestion createWordQuestion(TestQuestionEntity entity, InzWords word, int sort, String parentId) {
        InzWordQuestion question = new InzWordQuestion();

        // 手动设置字段，避免BeanUtils可能的问题
        question.setQuestion(entity.getQuestion());
        question.setQuestionType(entity.getQuestionType());
        question.setTrueAnswer(entity.getTrueAnswer());

        // 设置基本信息
        question.setSort(sort);
        question.setBookId(word.getBookId());
        question.setWordId(word.getId());
        question.setChapterId(word.getChapterId());
        question.setParentId(parentId);

        // 处理options字段 - 安全地转换为字符串
        if (entity.getOptions() != null && !entity.getOptions().isEmpty()) {
            question.setOptions(entity.getOptions().toString());
        }

        // 处理otherContent字段 - 安全地转换为字符串
        if (entity.getOtherContent() != null && !entity.getOtherContent().isEmpty()) {
            question.setOtherContent(entity.getOtherContent().toString());
        } else if (entity.getOptions() != null && !entity.getOptions().isEmpty()) {
            // 如果otherContent为空，使用options作为备选
            question.setOtherContent(entity.getOptions().toString());
        }

        log.debug("创建题目: {} (parent_id: {})", question.getQuestion(), question.getParentId());
        return question;
    }

    // 提取题型处理逻辑到一个单独的方法
    private List<String> getQuestionTypes(String questionType) {
        List<String> questionTypes = new ArrayList<>();

        // 定义题型映射表 (类型编号 -> 题型名称列表)
        Map<String, List<String>> typeMap = new HashMap<>();
        typeMap.put("1", Collections.singletonList("单选题"));
        typeMap.put("2", Collections.singletonList("多选题"));
        typeMap.put("3", Collections.singletonList("判断题"));
        typeMap.put("4", Collections.singletonList("填空题"));
        typeMap.put("5", Collections.singletonList("应用题"));

        if ("0".equals(questionType)) {
            // 如果是0，返回所有题型
            typeMap.values().forEach(questionTypes::addAll);
        } else {
            // 分割逗号分隔的题型编号（例如 "1,2,3"）
            String[] typeCodes = questionType.split(",");
            for (String code : typeCodes) {
                String trimmedCode = code.trim();
                List<String> types = typeMap.get(trimmedCode);
                if (types != null) {
                    questionTypes.addAll(types);
                }
            }
        }
        return questionTypes;
    }

    @Override
    @Async
    public void handleBatch(List<String> list) {
        for (Serializable id : list) {
            inzWordCollocationsService.deleteByMainId(id.toString());
            iInzWordsEtymologyService.deleteByMainId(id.toString());
        }
        log.info("批量解析单词");
        this.genderWordData(new ArrayList<>(list), 1);
    }

    @Override
    @Async
    public void handleBatchByWords(List<String> list) {
//		for(Serializable id:list) {
//			inzWordCollocationsService.deleteByMainId(id.toString());
//			iInzWordsEtymologyService.deleteByMainId(id.toString());
//		}
        this.genderWordDataGenerateAudioUrl(new ArrayList<>(list));
    }

    @Override
    public void scheduledFixTask() {
        List<InzWords> list = list(new QueryWrapper<InzWords>().lambda()
                .isNull(InzWords::getAudioUrl)
                .le(InzWords::getAnalysisCount, 3)
                .orderBy(true, true, InzWords::getAnalysisCount).last("LIMIT 500"));

        final int batchSize = 10;
        int totalWords = list.size();
        for (int i = 0; i < totalWords; i += batchSize) {
            List<InzWords> batch = list.subList(i, Math.min(totalWords, i + batchSize));
            List<String> wordIds = batch.stream().map(InzWords::getId).collect(Collectors.toList());
            incrementAnalysisCount(wordIds); // 新增的批量更新方法
            genderWordData(new ArrayList<>(wordIds), 2);
        }
    }

    @Async
    @Override
    public void delMainByBookId(String bookId) {
        List<InzWords> list = list(new QueryWrapper<InzWords>().lambda().eq(InzWords::getBookId, bookId));
        list.parallelStream().forEach(word -> {
            inzWordCollocationsService.deleteByMainId(word.getId());
            iInzWordsEtymologyService.deleteByMainId(word.getId());
            baseMapper.deleteById(word.getId());
        });
    }

    @Override
    public Map<String, String> savePrompt(String prompt) {
        return ThirdRequestUtils.savePrompt(prompt, redisUtil);
    }

    //处理单词
    private void genderWordData(ArrayList<String> wordIds, int type) {
        String thirdToken = ThirdRequestUtils.getThirdToken(ThirdRequestConstant.TOKEN_USERNAME, ThirdRequestConstant.TOKEN_PASSWORD, redisUtil);
        String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
        List<InzWords> list = listByIds(wordIds);

        // 创建一个 Map，用于快速查找 word 对应的 id
        Map<String, String> wordToIdMap = list.stream()
                .collect(Collectors.toMap(InzWords::getWord, InzWords::getId));

        List<WordEntity> wordEntities = list.stream()
                .parallel() // 开启并行流
                .map(word -> {
                    // 调用 analyzeWords 方法获取结果
                    log.info("开始处理单词: {}", word.getWord());
                    List<WordEntity> entities = ThirdRequestUtils.analyzeWords(new String[]{word.getWord()}, thirdToken, redisUtil, type);
                    // 如果返回的 List 为空或 null，则返回一个包含空对象的 List
                    if (entities.isEmpty()) {
                        return Collections.singletonList(new WordEntity()); // 返回一个空的 WordEntity
                    }
                    return entities; // 否则返回实际的数据
                })
                .flatMap(List::stream)  // 将每个返回的List合并成一个流
                .collect(Collectors.toList());

        if (!wordEntities.isEmpty()) {
            // 处理wordEntities中的每个WordEntity
            wordEntities.parallelStream().forEach(item -> {
                if (item == null) {
                    return;
                }
                // 增加对pronunciation字段的空值检查
                if (item.getPronunciation() == null || StringUtils.isBlank(item.getPronunciation().getEn())) {
                    return;
                }

                InzWords inzWords;
                System.out.println(wordToIdMap.get(item.getWord()));
                if (StringUtils.isNotBlank(wordToIdMap.get(item.getWord()))) {
                    inzWords = getOne(new QueryWrapper<InzWords>().lambda().eq(InzWords::getWord, item.getWord()).eq(InzWords::getId, wordToIdMap.get(item.getWord())));
                    String wordId;
                    if (inzWords != null) {
                        wordId = inzWords.getId();
                        list(new QueryWrapper<InzWords>().lambda().eq(InzWords::getWord, item.getWord())).parallelStream().forEach(po -> {
                            inzWordCollocationsService.deleteByMainId(po.getId());
                            InzWords words = new InzWords();
                            words.setWord(item.getWord());
                            words.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getAudioUrl(), "temp"));
                            words.setStatus(1);
                            words.setUkIpa(item.getPronunciation().getEn());
                            words.setUsIpa(item.getPronunciation().getUs());
                            words.setPronunciationGuide(item.getPronunciationGuide().toString());
                            words.setRootParticlesMean(item.getRootParticlesMean());
                            words.setHomophonic(item.getHomophonic());
                            if (StringUtils.isNotBlank(item.getUsAudioUrl())) {
                                words.setUsAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getUsAudioUrl(), "temp"));
                            }
                            if (StringUtils.isNotBlank(item.getBreakdownAudioUrl())) {
                                words.setBreakdownAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getBreakdownAudioUrl(), "temp"));
                            }
                            if (StringUtils.isNotBlank(item.getNaturalAudioUrl())) {
                                words.setNaturalAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getNaturalAudioUrl(), "temp"));
                            }

                            update(words, new QueryWrapper<InzWords>().lambda().eq(InzWords::getWord, item.getWord()).eq(InzWords::getId, po.getId()));
                            List<InzWordCollocations> wordCollocations = new ArrayList<>();
                            AtomicInteger examineSort = new AtomicInteger(1);
                            if (!CollectionUtils.isEmpty(item.getExamples())) {
                                item.getExamples().forEach(example -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    BeanUtils.copyProperties(example, wordCollocation);
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_EXAMINE);
                                    wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(example.getAudioUrl(), "temp"));
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    wordCollocations.add(wordCollocation);
                                });
                            }
                            //词源
                            if (!CollectionUtils.isEmpty(item.getEtymology())) {
                                item.getEtymology().forEach(etymology -> {
                                    InzWordsEtymology wordsEtymology = new InzWordsEtymology();
                                    BeanUtils.copyProperties(etymology, wordsEtymology);
                                    wordsEtymology.setWordId(po.getId());
                                    wordsEtymology.setOriginCh(etymology.getOriginCh());
                                    wordsEtymology.setMeaningEvolution(etymology.getMeaningEvolution());
                                    iInzWordsEtymologyService.save(wordsEtymology);
                                });
                            }

                            //短语
                            if (!CollectionUtils.isEmpty(item.getCollocations())) {
                                item.getCollocations().forEach(collocation -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    BeanUtils.copyProperties(collocation, wordCollocation);
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_COLLECTION);
                                    wordCollocation.setAudioUrl(collocation.getAudioUrl());
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(collocation.getAudioUrl(), "temp"));
                                    downloadAndSaveLocally(collocation.getAudioUrl(), "temp");
                                    wordCollocations.add(wordCollocation);
                                });
                            }

                            if (!CollectionUtils.isEmpty(item.getRootBreakdown())) {
                                item.getRootBreakdown().forEach(rootBreakdown -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    wordCollocation.setEnglish(rootBreakdown.getLetter());
                                    wordCollocation.setChinese(rootBreakdown.getFunction());
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_ROOT_PARTICLES);
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    wordCollocations.add(wordCollocation);
                                });
                            }


                            if (!CollectionUtils.isEmpty(item.getWordTransformation())) {
                                item.getWordTransformation().forEach(wordTransformation -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    wordCollocation.setEnglish(wordTransformation.getWords());
                                    wordCollocation.setChinese(wordTransformation.getChMean());
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_TRANSFORMATION);
                                    wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(wordTransformation.getAudioUrl(), "temp"));
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    wordCollocations.add(wordCollocation);
                                });
                            }

                            if (!CollectionUtils.isEmpty(item.getPartOfSpeech())) {
                                item.getPartOfSpeech().forEach(partOfSpeech -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    wordCollocation.setEnglish(partOfSpeech.getWord());
                                    wordCollocation.setChinese(partOfSpeech.getMeaning());
                                    wordCollocation.setAudioUrl(partOfSpeech.getAudioUrl());
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_PART_OF_SPEECH);
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(partOfSpeech.getAudioUrl(), "temp"));
                                    wordCollocations.add(wordCollocation);
                                });
                            }

//                item.getRootParticles().forEach((key, value) -> {
//                    InzWordCollocations wordCollocation = new InzWordCollocations();
//                    wordCollocation.setEnglish(key);
//                    wordCollocation.setChinese(value);
//                    wordCollocation.setWordId(wordId);
//                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_ROOT_PARTICLES);
//                    wordCollocation.setSort(examineSort.getAndIncrement());
//                    wordCollocations.add(wordCollocation);
//                });
                            if (!CollectionUtils.isEmpty(item.getSpeakNaturalPhonics())) {
                                item.getSpeakNaturalPhonics().forEach(speakNaturalPhonic -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    wordCollocation.setEnglish(speakNaturalPhonic.getLetter());
                                    wordCollocation.setChinese(speakNaturalPhonic.getPhonics());
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_SPEAK_NATURL_PHONICS);
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(speakNaturalPhonic.getAudioUrl(), "temp"));
                                    downloadAndSaveLocally(speakNaturalPhonic.getAudioUrl(), "temp");
                                    wordCollocations.add(wordCollocation);
                                });
                            }

                            if (!CollectionUtils.isEmpty(item.getNaturalPhonics())) {
                                item.getNaturalPhonics().forEach(naturalPhonic -> {
                                    InzWordCollocations wordCollocation = new InzWordCollocations();
                                    wordCollocation.setEnglish(naturalPhonic.getLetter());
                                    wordCollocation.setChinese(naturalPhonic.getPhonics());
                                    wordCollocation.setWordId(po.getId());
                                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_NATURAL_PHONICS);
                                    wordCollocation.setSort(examineSort.getAndIncrement());
                                    String rawPhonic = naturalPhonic.getPhonics().replaceAll("^/|/$", "");
                                    InzPhoneticSymbols one = inzPhoneticSymbolsService.getOne(new QueryWrapper<InzPhoneticSymbols>().lambda().eq(InzPhoneticSymbols::getName, rawPhonic));
                                    if (one != null) {
                                        wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + one.getAudioUrl());
                                    }
                                    wordCollocations.add(wordCollocation);
                                });
                            }
                            inzWordCollocationsService.saveBatch(wordCollocations);
                        });
                    } else {
                        InzWords words = new InzWords();
                        words.setWord(item.getWord());
                        words.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getAudioUrl(), "temp"));
                        words.setStatus(1);
                        words.setUkIpa(item.getPronunciation().getEn());
                        words.setUsIpa(item.getPronunciation().getUs());
                        words.setPronunciationGuide(item.getPronunciationGuide().toString());
                        words.setRootParticlesMean(item.getRootParticlesMean());
                        words.setHomophonic(item.getHomophonic());
                        words.setUsAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getUsAudioUrl(), "temp"));
                        words.setBreakdownAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getBreakdownAudioUrl(), "temp"));
                        words.setNaturalAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(item.getNaturalAudioUrl(), "temp"));
                        save(words);
                        wordId = words.getId();
                        List<InzWordCollocations> wordCollocations = new ArrayList<>();
                        AtomicInteger examineSort = new AtomicInteger(1);
                        if (!CollectionUtils.isEmpty(item.getPartOfSpeech())) {
                            item.getExamples().forEach(example -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                BeanUtils.copyProperties(example, wordCollocation);
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_EXAMINE);
                                wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(example.getAudioUrl(), "temp"));
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                wordCollocations.add(wordCollocation);
                            });
                        }
                        //词源
                        if (!CollectionUtils.isEmpty(item.getEtymology())) {
                            item.getEtymology().forEach(etymology -> {
                                InzWordsEtymology wordsEtymology = new InzWordsEtymology();
                                BeanUtils.copyProperties(etymology, wordsEtymology);
                                wordsEtymology.setWordId(wordId);
                                wordsEtymology.setOriginCh(etymology.getOriginCh());
                                wordsEtymology.setMeaningEvolution(etymology.getMeaningEvolution());
                                iInzWordsEtymologyService.save(wordsEtymology);
                            });
                        }
                        //短语
                        if (!CollectionUtils.isEmpty(item.getCollocations())) {
                            item.getCollocations().forEach(collocation -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                BeanUtils.copyProperties(collocation, wordCollocation);
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_COLLECTION);
                                wordCollocation.setAudioUrl(collocation.getAudioUrl());
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(collocation.getAudioUrl(), "temp"));
                                downloadAndSaveLocally(collocation.getAudioUrl(), "temp");
                                wordCollocations.add(wordCollocation);
                            });
                        }

                        if (!CollectionUtils.isEmpty(item.getRootBreakdown())) {
                            item.getRootBreakdown().forEach(rootBreakdown -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                wordCollocation.setEnglish(rootBreakdown.getLetter());
                                wordCollocation.setChinese(rootBreakdown.getFunction());
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_ROOT_PARTICLES);
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                wordCollocations.add(wordCollocation);
                            });
                        }

                        if (!CollectionUtils.isEmpty(item.getWordTransformation())) {
                            item.getWordTransformation().forEach(wordTransformation -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                wordCollocation.setEnglish(wordTransformation.getWords());
                                wordCollocation.setChinese(wordTransformation.getChMean());
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_TRANSFORMATION);
                                wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(wordTransformation.getAudioUrl(), "temp"));
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                wordCollocations.add(wordCollocation);
                            });
                        }
                        if (!CollectionUtils.isEmpty(item.getPartOfSpeech())) {
                            item.getPartOfSpeech().forEach(partOfSpeech -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                wordCollocation.setEnglish(partOfSpeech.getWord());
                                wordCollocation.setChinese(partOfSpeech.getMeaning());
                                wordCollocation.setAudioUrl(partOfSpeech.getAudioUrl());
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_PART_OF_SPEECH);
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(partOfSpeech.getAudioUrl(), "temp"));
                                wordCollocations.add(wordCollocation);
                            });
                        }

//                item.getRootParticles().forEach((key, value) -> {
//                    InzWordCollocations wordCollocation = new InzWordCollocations();
//                    wordCollocation.setEnglish(key);
//                    wordCollocation.setChinese(value);
//                    wordCollocation.setWordId(wordId);
//                    wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_ROOT_PARTICLES);
//                    wordCollocation.setSort(examineSort.getAndIncrement());
//                    wordCollocations.add(wordCollocation);
//                });
                        if (!CollectionUtils.isEmpty(item.getSpeakNaturalPhonics())) {
                            item.getSpeakNaturalPhonics().forEach(speakNaturalPhonic -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                wordCollocation.setEnglish(speakNaturalPhonic.getLetter());
                                wordCollocation.setChinese(speakNaturalPhonic.getPhonics());
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_SPEAK_NATURL_PHONICS);
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(speakNaturalPhonic.getAudioUrl(), "temp"));
                                downloadAndSaveLocally(speakNaturalPhonic.getAudioUrl(), "temp");
                                wordCollocations.add(wordCollocation);
                            });
                        }

                        if (!CollectionUtils.isEmpty(item.getNaturalPhonics())) {
                            item.getNaturalPhonics().forEach(naturalPhonic -> {
                                InzWordCollocations wordCollocation = new InzWordCollocations();
                                wordCollocation.setEnglish(naturalPhonic.getLetter());
                                wordCollocation.setChinese(naturalPhonic.getPhonics());
                                wordCollocation.setWordId(wordId);
                                wordCollocation.setType(ThirdRequestConstant.WORD_COLLOCATION_TYPE_NATURAL_PHONICS);
                                wordCollocation.setSort(examineSort.getAndIncrement());
                                String rawPhonic = naturalPhonic.getPhonics().replaceAll("^/|/$", "");
                                InzPhoneticSymbols one = inzPhoneticSymbolsService.getOne(new QueryWrapper<InzPhoneticSymbols>().lambda().eq(InzPhoneticSymbols::getName, rawPhonic));
                                if (one != null) {
                                    wordCollocation.setAudioUrl(domain + "/super-words/sys/common/static/" + one.getAudioUrl());
                                }
                                wordCollocations.add(wordCollocation);
                            });
                        }
                        inzWordCollocationsService.saveBatch(wordCollocations);
                    }


                } else {
                    System.out.println("没有找到数据");
                }


            });
        }
    }

    // 新增方法：批量递增 analysisCount
    private void incrementAnalysisCount(List<String> wordIds) {
        if (wordIds.isEmpty()) return;

        // 使用 SQL 语句直接递增次数，避免并发问题
        UpdateWrapper<InzWords> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", wordIds)
                .setSql("analysis_count = analysis_count + 1");
        update(null, updateWrapper); // 参数为null表示不更新实体字段，只执行SQL
    }


    @Override
    public void genderChineseAudio() {
        String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
        String thirdToken = ThirdRequestUtils.getThirdToken(ThirdRequestConstant.TOKEN_USERNAME, ThirdRequestConstant.TOKEN_PASSWORD, redisUtil);
        List<InzWordCollocations> collocations = inzWordCollocationsService.list(new QueryWrapper<InzWordCollocations>().lambda()
                .eq(InzWordCollocations::getType, "part_of_speech")
                .isNull(InzWordCollocations::getUpdateTime));
        collocations.parallelStream().forEach(collocation -> {
            // 构建 JSON 结构
            Map<String, Object> wordJson = new HashMap<>();
            wordJson.put("word", collocation.getChinese());
            wordJson.put("appkey", redisUtil.get("SuperWords:config:HUOSHAN_APPKEY").toString());
            wordJson.put("accessKey", redisUtil.get("SuperWords:config:HUOSHAN_ACCESSKEY").toString());
            wordJson.put("secretKey", redisUtil.get("SuperWords:config:HUOSHAN_SECRTEKEY").toString());
            wordJson.put("speaker", "zh_female_qingxin");

            Map<String, String> result = ThirdRequestUtils.analyzeChineseWordAudio(wordJson, thirdToken, redisUtil);
            collocation.setAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(result.get("file_url"), "temp"));
            inzWordCollocationsService.update(collocation, new UpdateWrapper<InzWordCollocations>().lambda().eq(InzWordCollocations::getId, collocation.getId()));
        });
    }

    @Override
    public void synchronousWords(List<String> list) {
        List<InzWords> inzWords = listByIds(list);
        inzWords.forEach(word -> {
            List<InzWordCollocations> collocations = inzWordCollocationsService.list(new QueryWrapper<InzWordCollocations>().lambda().eq(InzWordCollocations::getWordId, word.getId()));
            List<InzWords> inzWordsList = baseMapper.selectList(new QueryWrapper<InzWords>().lambda().eq(InzWords::getWord, word.getWord()).ne(InzWords::getId, word.getId()));
            ArrayList<InzWordCollocations> inzWordCollocations = new ArrayList<>();
            inzWordsList.forEach(item -> {
                inzWordCollocationsService.deleteByMainId(item.getId());
                collocations.forEach(item1 -> {
                    InzWordCollocations wordCollocation = new InzWordCollocations();
                    BeanUtils.copyProperties(item1, wordCollocation);
                    wordCollocation.setWordId(item.getId());
                    wordCollocation.setId(null);
                    inzWordCollocations.add(wordCollocation);
                });
                item.setAudioUrl(word.getAudioUrl());
                item.setWord(word.getWord());
                item.setNaturalAudioUrl(word.getNaturalAudioUrl());
                item.setUsAudioUrl(word.getUsAudioUrl());
                item.setBreakdownAudioUrl(word.getBreakdownAudioUrl());
                item.setHomophonic(word.getHomophonic());
                item.setUkIpa(word.getUkIpa());
                item.setUsIpa(word.getUsIpa());
                baseMapper.updateById(item);
            });
            inzWordCollocationsService.saveBatch(inzWordCollocations);
        });

    }

    @Override
    public void handleUniData() {

        // 1. 从在线JSON文件读取数据
        String jsonUrl = "https://editor-console.infrabiz.net/output.json"; // 替换为实际的JSON文件URL
        List<String> wordsFromJson = fetchWordsFromOnlineJson(jsonUrl);
        System.out.println(wordsFromJson);
//        // 2. 处理原有列表中的ID

        for (String word : wordsFromJson) {
            ArrayList<String> wordIds = new ArrayList<>();
            InzWords inzWords = baseMapper.selectOne(
                    new QueryWrapper<InzWords>()
                            .lambda()
                            .eq(InzWords::getWord, word)
                            .last("LIMIT 1") // 明确限制只返回一条记录
            );
            // 检查是否查询到结果
            if (inzWords != null && inzWords.getId() != null) {
                wordIds.add(inzWords.getId());
                this.genderWordData(wordIds, 2);
            } else {
                System.out.println("未找到单词或单词ID为空: " + word);
            }
        }
    }

    /**
     * 从在线JSON文件获取单词列表
     */
    private List<String> fetchWordsFromOnlineJson(String jsonUrl) {
        List<String> words = new ArrayList<>();

        try {
            // 创建HTTP客户端
            CloseableHttpClient httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(jsonUrl);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpGet);

            // 检查响应状态
            if (response.getStatusLine().getStatusCode() == 200) {
                // 读取JSON响应
                String jsonResponse = EntityUtils.toString(response.getEntity());

                // 解析JSON
                ObjectMapper objectMapper = new ObjectMapper();
                List<Map<String, String>> jsonData = objectMapper.readValue(
                        jsonResponse,
                        new TypeReference<List<Map<String, String>>>() {
                        }
                );

                // 提取word字段
                for (Map<String, String> item : jsonData) {
                    if (item.containsKey("word")) {
                        words.add(item.get("word"));
                    }
                }
            } else {
                throw new RuntimeException("Failed to fetch JSON: HTTP " +
                        response.getStatusLine().getStatusCode());
            }

            response.close();
            httpClient.close();
        } catch (Exception e) {
            throw new RuntimeException("Error fetching or parsing JSON", e);
        }

        return words;
    }


    private void genderWordDataGenerateAudioUrl(ArrayList<String> wordIds) {
        String domain = redisUtil.get("SuperWords:config:DOMAIN").toString();
        String thirdToken = ThirdRequestUtils.getThirdToken(ThirdRequestConstant.TOKEN_USERNAME, ThirdRequestConstant.TOKEN_PASSWORD, redisUtil);
        List<InzWords> list = listByIds(wordIds);
        list.forEach(word -> {
            List<InzWordCollocations> wordCollocations = inzWordCollocationsService.list(new QueryWrapper<InzWordCollocations>().lambda()
                    .eq(InzWordCollocations::getWordId, word.getId()).orderBy(true, true, InzWordCollocations::getSort));

            // 构建 JSON 结构
            Map<String, Object> wordJson = new HashMap<>();
            wordJson.put("word", word.getWord());
            wordJson.put("appkey", redisUtil.get("SuperWords:config:HUOSHAN_APPKEY").toString());
            wordJson.put("accessKey", redisUtil.get("SuperWords:config:HUOSHAN_ACCESSKEY").toString());
            wordJson.put("secretKey", redisUtil.get("SuperWords:config:HUOSHAN_SECRTEKEY").toString());
            wordJson.put("speaker", redisUtil.get("SuperWords:config:HUOSHAN_SPEAKER").toString());

            // 处理 naturalPhonics
            if (wordCollocations != null) {
                List<Map<String, String>> naturalList = new ArrayList<>();
                List<Map<String, String>> breakdownList = new ArrayList<>();
                wordCollocations.forEach(phonics -> {
                    if (phonics.getType().equals("natural_phonics")) {
                        // 假设 naturalPhonics 中有 word 和 ipa 字段
                        naturalList.add(createPhonicsMap(phonics.getEnglish(), phonics.getChinese()));
//								breakdownList.add(createPhonicsMap(phonics.getChinese(), phonics.getPhonics()));
                    }

                    if (phonics.getType().equals("speak_naturl_phonics")) {
                        // 假设 naturalPhonics 中有 word 和 ipa 字段
                        breakdownList.add(createPhonicsMap(phonics.getEnglish(), phonics.getChinese()));
                    }

                });
                wordJson.put("natural", naturalList);
                wordJson.put("breakdown", breakdownList);
            }
            Map<String, String> result = ThirdRequestUtils.analyzeWordAudio(wordJson, thirdToken, redisUtil);
            word.setNaturalAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(result.get("natural_url"), "temp"));
            word.setBreakdownAudioUrl(domain + "/super-words/sys/common/static/" + downloadAndSaveLocally(result.get("breakdown_url"), "temp"));
            update(word, new UpdateWrapper<InzWords>().lambda().eq(InzWords::getId, word.getId()));
        });

    }

    // 辅助方法，用于创建 phonics map
    private Map<String, String> createPhonicsMap(String word, String ipa) {
        Map<String, String> phonicsMap = new HashMap<>();
        phonicsMap.put("word", word);
        phonicsMap.put("ipa", ipa);
        return phonicsMap;
    }


    @Value(value = "${jeecg.path.upload}")
    private String uploadpath;

    /**
     * Downloads an audio file from the given URL and saves it locally.
     *
     * @return The local file path where the audio file is saved.
     */
    public String downloadAndSaveLocally(String fileUrl, String bizPath) {
        try {
            // Define the base directory and ensure it exists
            String ctxPath = uploadpath;
            File baseDir = new File(ctxPath + File.separator + bizPath);
            if (!baseDir.exists()) {
                baseDir.mkdirs(); // Create the directory if it doesn't exist
            }

            // Extract the file name from the URL
            String fileName = Paths.get(new URL(fileUrl).getPath()).getFileName().toString();

            // Ensure the file name is unique
            fileName = generateUniqueFileName(fileName);

            // Define the full path to save the file
            String savePath = baseDir.getPath() + File.separator + fileName;

            // Download the file and save it locally
            try (InputStream in = new URL(fileUrl).openStream();
                 FileOutputStream out = new FileOutputStream(savePath)) {
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = in.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }

            String relativePath = bizPath + File.separator + fileName;
            return relativePath.replace(File.separator, "/");
        } catch (IOException e) {
            return "";
        }
    }

    /**
     * Generates a unique file name by appending a timestamp if necessary.
     *
     * @param originalName The original file name.
     * @return A unique file name.
     */
    private static String generateUniqueFileName(String originalName) {
        if (originalName.contains(".")) {
            int dotIndex = originalName.lastIndexOf(".");
            return originalName.substring(0, dotIndex) + "_" + System.currentTimeMillis() + originalName.substring(dotIndex);
        } else {
            return originalName + "_" + System.currentTimeMillis();
        }
    }

}
