--  修改仪表盘组件的名称
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '常用', `comp_type` = '', `icon` = 'ant-design:setting-twotone', `order_num` = 2, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'jeecg', `update_time` = '2022-04-24 11:02:19' WHERE `id` = '100';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '布局', `comp_type` = 'layout', `icon` = 'ic:baseline-tab', `order_num` = 3, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:49:13' WHERE `id` = '100101';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '数据', `comp_type` = 'dataList', `icon` = 'ant-design:table-outlined', `order_num` = 3, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:49:48' WHERE `id` = '100102';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '首页', `comp_type` = 'home', `icon` = 'carbon:home', `order_num` = 9, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:50:07' WHERE `id` = '100104';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '地图', `comp_type` = 'dataList', `icon` = 'ant-design:table-outlined', `order_num` = 3, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 09:49:48' WHERE `id` = '100120';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '图表', `comp_type` = 'chart', `icon` = 'ant-design:bar-chart-outlined', `order_num` = 1, `type_id` = NULL, `comp_config` = '', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = NULL, `update_time` = '2022-04-29 17:52:06' WHERE `id` = '200';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '自定义', `comp_type` = 'customForm', `icon` = 'ant-design:project-filled', `order_num` = 2, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-07-13 19:02:51', `update_by` = 'ldd', `update_time` = '2023-02-17 21:18:37' WHERE `id` = '707153616621699072';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '个性', `comp_type` = 'custom', `icon` = 'ant-design:appstore-twotone', `order_num` = 100, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = 'admin', `create_time` = '2022-07-18 19:22:09', `update_by` = 'admin', `update_time` = '2022-07-18 19:33:20' WHERE `id` = '708970414976712704';
UPDATE `onl_drag_comp` SET `parent_id` = '0', `comp_name` = '表单', `comp_type` = 'customForm', `icon` = 'ant-design:project-filled', `order_num` = 2, `type_id` = NULL, `comp_config` = NULL, `status` = '1', `create_by` = 'jeecg', `create_time` = '2022-07-13 19:02:51', `update_by` = 'ldd', `update_time` = '2023-02-17 21:18:37' WHERE `id` = '707153616621699072';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '飞线地图', `comp_type` = 'JFlyLineMap', `icon` = 'la:plane', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 50,\n  \"dataType\": 1,\n  \"background\": \"#292626\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"贵州\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 106.6992,\n      \"toLat\": 26.7682,\n      \"value\": 100\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"北京\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 116.46,\n      \"toLat\": 39.92,\n      \"value\": 100\n    },\n    {\n      \"fromName\": \"新疆\",\n      \"toName\": \"北京\",\n      \"fromLng\": 87.68,\n      \"fromLat\": 43.67,\n      \"toLng\": 116.46,\n      \"toLat\": 39.92,\n      \"value\": 100\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#323c48\",\n      \"color2\": \"#3B3737\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"effect\": {\n      \"show\": true,\n      \"trailLength\": 0,\n      \"period\": 6,\n      \"symbolSize\": 15\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"padding\": [\n        5,\n        0,\n        0,\n        15\n      ],\n      \"show\": true\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        2\n      ]\n    },\n    \"geo\": {\n      \"top\": 80,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": true,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#5A7864\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"#323c48\",\n          \"shadowColor\": \"\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#EEDD78\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:16:54' WHERE `id` = '100120101';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '柱形地图', `comp_type` = 'JBarMap', `icon` = 'uil:graph-bar', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#ffffff\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"北京\",\n      \"value\": 900\n    },\n    {\n      \"name\": \"山西\",\n      \"value\": 1681\n    },\n    {\n      \"name\": \"内蒙古\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"辽宁\",\n      \"value\": 1667\n    },\n    {\n      \"name\": \"福建\",\n      \"value\": 516\n    },\n    {\n      \"name\": \"江西\",\n      \"value\": 591\n    },\n    {\n      \"name\": \"山东\",\n      \"value\": 419\n    },\n    {\n      \"name\": \"河南\",\n      \"value\": 137\n    },\n    {\n      \"name\": \"云南\",\n      \"value\": 983\n    },\n    {\n      \"name\": \"西藏\",\n      \"value\": 9\n    },\n    {\n      \"name\": \"陕西\",\n      \"value\": 580\n    },\n    {\n      \"name\": \"甘肃\",\n      \"value\": 556\n    },\n    {\n      \"name\": \"海南\",\n      \"value\": 14\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 12,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#3B3737\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#000000\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"tooltip\": {\n      \"trigger\": \"item\",\n      \"show\": false,\n      \"enterable\": true,\n      \"textStyle\": {\n        \"fontSize\": 20,\n        \"color\": \"#fff\"\n      },\n      \"backgroundColor\": \"rgba(0,2,89,0.8)\"\n    },\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"left\": 10,\n      \"show\": true\n    },\n    \"visualMap\": {\n      \"show\": false,\n      \"max\": 200,\n      \"seriesIndex\": [\n        0\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"roam\": true,\n      \"aspectScale\": 0.96,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"#37805B\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\r\n        \"emphasis\": {\r\n          \"areaColor\": \"#fff59c\"\r\n        }\n      }\n    },\n    \"series \": []\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:01' WHERE `id` = '100120102';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '时间轴飞线地图', `comp_type` = 'JTotalFlyLineMap', `icon` = 'fluent:airplane-take-off-16-regular', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 55,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"background\": \"#000000\",\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"贵州\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 106.6992,\n      \"toLat\": 26.7682,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"fromName\": \"河南\",\n      \"toName\": \"云南\",\n      \"fromLng\": 113.4668,\n      \"fromLat\": 34.6234,\n      \"toLng\": 102.9199,\n      \"toLat\": 25.4663,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"甘肃\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 103.5901,\n      \"toLat\": 36.3043,\n      \"value\": 100,\n      \"group\": 2018\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"广东\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 113.12244,\n      \"toLat\": 31.9208,\n      \"value\": 147,\n      \"group\": 2018\n    },\n    {\n      \"fromName\": \"江苏\",\n      \"toName\": \"北京\",\n      \"fromLng\": 118.8062,\n      \"fromLat\": 31.9208,\n      \"toLng\": 116.4551,\n      \"toLat\": 40.2539,\n      \"value\": 100,\n      \"group\": 2019\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": true,\n    \"areaColor\": {\n      \"color1\": \"#0A0909\",\n      \"color2\": \"#3B3737\"\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"show\": true,\n      \"left\": 10,\n      \"textStyle\": {\n        \"color\": \"#70DB93\",\n        \"fontSize\": \"22px\"\n      },\n      \"subtextStyle\": {\n        \"color\": \"#ffffff\",\n        \"fontSize\": \"12px\"\n      }\n    },\n    \"geo\": {\n      \"top\": 50,\n      \"left\": 100,\n      \"label\": {\n        \"normal\": {\n          \"show\": false\n        },\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": true,\n      \"zoom\": 0.9,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#93ebf8\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": -2,\n          \"shadowOffsetY\": 2,\n          \"shadowBlur\": 10\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#EEDD78\",\n          \"borderWidth\": 0\n        }\n      }\n    },\n    \"timeline\": {\n      \"show\": true,\n      \"axisType\": \"category\",\n      \"autoPlay\": false,\n      \"playInterval\": 2000,\n      \"left\": \"10%\",\n      \"right\": \"5%\",\n      \"bottom\": 10,\n      \"padding\": 5,\n      \"width\": \"80%\",\n      \"label\": {\n        \"normal\": {\n          \"textStyle\": {\n            \"color\": \"#ffffff\"\n          }\n        },\n        \"emphasis\": {\n          \"textStyle\": {\n            \"color\": \"#000000\"\n          }\n        }\n      },\n      \"symbolSize\": 10,\n      \"lineStyle\": {\n        \"color\": \"#555555\"\n      },\n      \"checkpointStyle\": {\n        \"borderColor\": \"#777777\",\n        \"borderWidth\": 2\n      },\n      \"controlStyle\": {\n        \"showNextBtn\": true,\n        \"showPrevBtn\": true,\n        \"normal\": {\n          \"color\": \"#666666\",\n          \"borderColor\": \"#666666\"\n        },\n        \"emphasis\": {\n          \"color\": \"#aaaaaa\",\n          \"borderColor\": \"#aaaaaa\"\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:09' WHERE `id` = '100120103';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '柱形排名形地图', `comp_type` = 'JTotalBarMap', `icon` = 'ph:chart-bar-horizontal', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 55,\n  \"dataType\": 1,\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n  \"background\": \"#000000\",\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"江苏\",\n      \"lng\": 118.8062,\n      \"lat\": 31.9208,\n      \"value\": 500,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"贵州\",\n      \"lng\": 106.6992,\n      \"lat\": 26.7682,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"河南\",\n      \"lng\": 113.4668,\n      \"lat\": 34.6234,\n      \"value\": 100,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"云南\",\n      \"lng\": 102.9199,\n      \"lat\": 25.4663,\n      \"value\": 300,\n      \"group\": 2017\n    },\n    {\n      \"name\": \"江苏\",\n      \"lng\": 118.8062,\n      \"lat\": 31.9208,\n      \"value\": 478,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"贵州\",\n      \"lng\": 106.6992,\n      \"lat\": 26.7682,\n      \"value\": 269,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"河南\",\n      \"lng\": 113.4668,\n      \"lat\": 34.6234,\n      \"value\": 128,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"云南\",\n      \"lng\": 102.9199,\n      \"lat\": 25.4663,\n      \"value\": 100,\n      \"group\": 2018\n    },\n    {\n      \"name\": \"江苏\",\n      \"lng\": 118.8062,\n      \"lat\": 31.9208,\n      \"value\": 236,\n      \"group\": 2019\n    },\n    {\n      \"name\": \"贵州\",\n      \"lng\": 106.6992,\n      \"lat\": 26.7682,\n      \"value\": 569,\n      \"group\": 2019\n    },\n    {\n      \"name\": \"河南\",\n      \"lng\": 113.4668,\n      \"lat\": 34.6234,\n      \"value\": 479,\n      \"group\": 2019\n    },\n    {\n      \"name\": \"云南\",\n      \"lng\": 102.9199,\n      \"lat\": 25.4663,\n      \"value\": 259,\n      \"group\": 2019\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": true,\n    \"mapTitle\": \"\",\n    \"dataTitle\": \"数据统计情况\",\n    \"dataTitleSize\": 20,\n    \"dataTitleColor\": \"#ffffff\",\n    \"dataNameColor\": \"#dddddd\",\n    \"dataValueColor\": \"#dddddd\",\n    \"areaColor\": {\n      \"color1\": \"#0A0909\",\n      \"color2\": \"#3B3737\"\n    },\n    \"grid\": {\n      \"bottom\": 50,\n      \"left\": 75,\n      \"top\": 20\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#04387b\",\n        \"#467bc0\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#DDE330\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"综合统计地图\",\n      \"show\": true,\n      \"left\": 10,\n      \"textStyle\": {\n        \"color\": \"#ffffff\",\n        \"fontSize\": \"22px\"\n      },\n      \"subtextStyle\": {\n        \"color\": \"#ffffff\",\n        \"fontSize\": \"12px\"\n      }\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"radar\": [\n      {\n        \"indicator\": []\n      }\n    ],\n    \"timeline\": {\n      \"show\": true,\n      \"axisType\": \"category\",\n      \"autoPlay\": false,\n      \"playInterval\": 2000,\n      \"left\": \"10%\",\n      \"right\": \"5%\",\n      \"bottom\": 5,\n      \"padding\": 5,\n      \"width\": \"80%\",\n      \"label\": {\n        \"normal\": {\n          \"textStyle\": {\n            \"color\": \"#ffffff\"\n          }\n        },\n        \"emphasis\": {\n          \"textStyle\": {\n            \"color\": \"#000000\"\n          }\n        }\n      },\n      \"symbolSize\": 10,\n      \"lineStyle\": {\n        \"color\": \"#555555\"\n      },\n      \"checkpointStyle\": {\n        \"borderColor\": \"#777777\",\n        \"borderWidth\": 2\n      },\n      \"controlStyle\": {\n        \"showNextBtn\": true,\n        \"showPrevBtn\": true,\n        \"normal\": {\n          \"color\": \"#666666\",\n          \"borderColor\": \"#666666\"\n        },\n        \"emphasis\": {\n          \"color\": \"#aaaaaa\",\n          \"borderColor\": \"#aaaaaa\"\n        }\n      }\n    },\n    \"geo\": {\n      \"top\": 80,\n      \"left\": \"3%\",\n      \"show\": true,\n      \"roam\": true,\n      \"zoom\": 0.9,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false\n        }\n      },\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#93ebf8\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": -2,\n          \"shadowOffsetY\": 2,\n          \"shadowBlur\": 10\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#EEDD78\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:17' WHERE `id` = '100120105';
UPDATE `onl_drag_comp` SET `parent_id` = '100120', `comp_name` = '热力地图', `comp_type` = 'JHeatMap', `icon` = 'carbon:heat-map-02', `order_num` = 1, `type_id` = NULL, `comp_config` = '{\n  \"w\": 12,\n  \"h\": 30,\n  \"dataType\": 1,\n  \"background\": \"#000000\",\n  \"url\": \"http://api.jeecg.com/mock/33/radar\",\n  \"timeOut\": 0,\n      \"dataMapping\": [\n    {\n      \"filed\": \"区域\",\n      \"mapping\": \"\"\n    },\n    {\n      \"filed\": \"数值\",\n      \"mapping\": \"\"\n    }\n  ],\n  \"turnConfig\": {\n    \"url\": \"\"\n  },\n  \"linkageConfig\": [],\n  \"chartData\": [\n    {\n      \"name\": \"海门\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"鄂尔多斯\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"招远\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"舟山\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"齐齐哈尔\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"盐城\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"赤峰\",\n      \"value\": 16\n    },\n    {\n      \"name\": \"青岛\",\n      \"value\": 450\n    },\n    {\n      \"name\": \"乳山\",\n      \"value\": 118\n    },\n    {\n      \"name\": \"金昌\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"泉州\",\n      \"value\": 21\n    },\n    {\n      \"name\": \"莱西\",\n      \"value\": 300\n    },\n    {\n      \"name\": \"日照\",\n      \"value\": 121\n    },\n    {\n      \"name\": \"胶南\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"南通\",\n      \"value\": 23\n    },\n    {\n      \"name\": \"拉萨\",\n      \"value\": 321\n    },\n    {\n      \"name\": \"云浮\",\n      \"value\": 444\n    },\n    {\n      \"name\": \"梅州\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"文登\",\n      \"value\": 456\n    },\n    {\n      \"name\": \"上海\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"攀枝花\",\n      \"value\": 125\n    },\n    {\n      \"name\": \"威海\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"承德\",\n      \"value\": 25\n    },\n    {\n      \"name\": \"厦门\",\n      \"value\": 126\n    },\n    {\n      \"name\": \"汕尾\",\n      \"value\": 26\n    },\n    {\n      \"name\": \"潮州\",\n      \"value\": 247\n    },\n    {\n      \"name\": \"丹东\",\n      \"value\": 227\n    },\n    {\n      \"name\": \"太仓\",\n      \"value\": 427\n    },\n    {\n      \"name\": \"曲靖\",\n      \"value\": 327\n    },\n    {\n      \"name\": \"烟台\",\n      \"value\": 28\n    },\n    {\n      \"name\": \"福州\",\n      \"value\": 29\n    },\n    {\n      \"name\": \"瓦房店\",\n      \"value\": 30\n    },\n    {\n      \"name\": \"即墨\",\n      \"value\": 30\n    },\n    {\n      \"name\": \"抚顺\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"玉溪\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"张家口\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"阳泉\",\n      \"value\": 31\n    },\n    {\n      \"name\": \"莱州\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"湖州\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"汕头\",\n      \"value\": 32\n    },\n    {\n      \"name\": \"昆山\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"宁波\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"湛江\",\n      \"value\": 33\n    },\n    {\n      \"name\": \"揭阳\",\n      \"value\": 34\n    },\n    {\n      \"name\": \"荣成\",\n      \"value\": 34\n    },\n    {\n      \"name\": \"连云港\",\n      \"value\": 35\n    },\n    {\n      \"name\": \"葫芦岛\",\n      \"value\": 35\n    },\n    {\n      \"name\": \"常熟\",\n      \"value\": 236\n    },\n    {\n      \"name\": \"东莞\",\n      \"value\": 336\n    },\n    {\n      \"name\": \"河源\",\n      \"value\": 36\n    },\n    {\n      \"name\": \"淮安\",\n      \"value\": 436\n    },\n    {\n      \"name\": \"泰州\",\n      \"value\": 236\n    },\n    {\n      \"name\": \"南宁\",\n      \"value\": 437\n    },\n    {\n      \"name\": \"营口\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"惠州\",\n      \"value\": 337\n    },\n    {\n      \"name\": \"江阴\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"蓬莱\",\n      \"value\": 37\n    },\n    {\n      \"name\": \"韶关\",\n      \"value\": 38\n    },\n    {\n      \"name\": \"嘉峪关\",\n      \"value\": 38\n    },\n    {\n      \"name\": \"广州\",\n      \"value\": 138\n    },\n    {\n      \"name\": \"延安\",\n      \"value\": 138\n    },\n    {\n      \"name\": \"太原\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"清远\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"中山\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"昆明\",\n      \"value\": 139\n    },\n    {\n      \"name\": \"寿光\",\n      \"value\": 440\n    },\n    {\n      \"name\": \"盘锦\",\n      \"value\": 40\n    },\n    {\n      \"name\": \"长治\",\n      \"value\": 41\n    },\n    {\n      \"name\": \"深圳\",\n      \"value\": 41\n    },\n    {\n      \"name\": \"珠海\",\n      \"value\": 42\n    },\n    {\n      \"name\": \"宿迁\",\n      \"value\": 43\n    },\n    {\n      \"name\": \"咸阳\",\n      \"value\": 43\n    },\n    {\n      \"name\": \"铜川\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"平度\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"佛山\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"海口\",\n      \"value\": 44\n    },\n    {\n      \"name\": \"江门\",\n      \"value\": 45\n    },\n    {\n      \"name\": \"章丘\",\n      \"value\": 45\n    },\n    {\n      \"name\": \"肇庆\",\n      \"value\": 46\n    },\n    {\n      \"name\": \"大连\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"临汾\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"吴江\",\n      \"value\": 47\n    },\n    {\n      \"name\": \"石嘴山\",\n      \"value\": 49\n    },\n    {\n      \"name\": \"沈阳\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"苏州\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"茂名\",\n      \"value\": 50\n    },\n    {\n      \"name\": \"嘉兴\",\n      \"value\": 51\n    },\n    {\n      \"name\": \"长春\",\n      \"value\": 51\n    },\n    {\n      \"name\": \"胶州\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"银川\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"张家港\",\n      \"value\": 52\n    },\n    {\n      \"name\": \"三门峡\",\n      \"value\": 53\n    },\n    {\n      \"name\": \"锦州\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"南昌\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"柳州\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"三亚\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"自贡\",\n      \"value\": 156\n    },\n    {\n      \"name\": \"吉林\",\n      \"value\": 156\n    },\n    {\n      \"name\": \"阳江\",\n      \"value\": 257\n    },\n    {\n      \"name\": \"泸州\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"西宁\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"宜宾\",\n      \"value\": 258\n    },\n    {\n      \"name\": \"呼和浩特\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"成都\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"大同\",\n      \"value\": 58\n    },\n    {\n      \"name\": \"镇江\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"桂林\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"张家界\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"宜兴\",\n      \"value\": 59\n    },\n    {\n      \"name\": \"北海\",\n      \"value\": 60\n    },\n    {\n      \"name\": \"西安\",\n      \"value\": 61\n    },\n    {\n      \"name\": \"金坛\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"东营\",\n      \"value\": 62\n    },\n    {\n      \"name\": \"牡丹江\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"遵义\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"绍兴\",\n      \"value\": 63\n    },\n    {\n      \"name\": \"扬州\",\n      \"value\": 64\n    },\n    {\n      \"name\": \"常州\",\n      \"value\": 64\n    },\n    {\n      \"name\": \"潍坊\",\n      \"value\": 65\n    },\n    {\n      \"name\": \"重庆\",\n      \"value\": 66\n    },\n    {\n      \"name\": \"台州\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"南京\",\n      \"value\": 67\n    },\n    {\n      \"name\": \"滨州\",\n      \"value\": 70\n    },\n    {\n      \"name\": \"贵阳\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"无锡\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"本溪\",\n      \"value\": 71\n    },\n    {\n      \"name\": \"克拉玛依\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"渭南\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"马鞍山\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"宝鸡\",\n      \"value\": 72\n    },\n    {\n      \"name\": \"焦作\",\n      \"value\": 75\n    },\n    {\n      \"name\": \"句容\",\n      \"value\": 75\n    },\n    {\n      \"name\": \"北京\",\n      \"value\": 79\n    },\n    {\n      \"name\": \"徐州\",\n      \"value\": 79\n    },\n    {\n      \"name\": \"衡水\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"包头\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"绵阳\",\n      \"value\": 80\n    },\n    {\n      \"name\": \"乌鲁木齐\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"枣庄\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"杭州\",\n      \"value\": 84\n    },\n    {\n      \"name\": \"淄博\",\n      \"value\": 85\n    },\n    {\n      \"name\": \"鞍山\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"溧阳\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"库尔勒\",\n      \"value\": 86\n    },\n    {\n      \"name\": \"安阳\",\n      \"value\": 190\n    },\n    {\n      \"name\": \"开封\",\n      \"value\": 390\n    },\n    {\n      \"name\": \"济南\",\n      \"value\": 292\n    },\n    {\n      \"name\": \"德阳\",\n      \"value\": 393\n    },\n    {\n      \"name\": \"温州\",\n      \"value\": 95\n    },\n    {\n      \"name\": \"九江\",\n      \"value\": 96\n    },\n    {\n      \"name\": \"邯郸\",\n      \"value\": 98\n    },\n    {\n      \"name\": \"临安\",\n      \"value\": 99\n    },\n    {\n      \"name\": \"兰州\",\n      \"value\": 99\n    },\n    {\n      \"name\": \"沧州\",\n      \"value\": 100\n    },\n    {\n      \"name\": \"临沂\",\n      \"value\": 103\n    },\n    {\n      \"name\": \"南充\",\n      \"value\": 104\n    },\n    {\n      \"name\": \"天津\",\n      \"value\": 105\n    },\n    {\n      \"name\": \"富阳\",\n      \"value\": 106\n    },\n    {\n      \"name\": \"泰安\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"诸暨\",\n      \"value\": 112\n    },\n    {\n      \"name\": \"郑州\",\n      \"value\": 113\n    },\n    {\n      \"name\": \"哈尔滨\",\n      \"value\": 114\n    },\n    {\n      \"name\": \"聊城\",\n      \"value\": 116\n    },\n    {\n      \"name\": \"芜湖\",\n      \"value\": 117\n    },\n    {\n      \"name\": \"唐山\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"平顶山\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"邢台\",\n      \"value\": 119\n    },\n    {\n      \"name\": \"德州\",\n      \"value\": 120\n    },\n    {\n      \"name\": \"济宁\",\n      \"value\": 120\n    },\n    {\n      \"name\": \"荆州\",\n      \"value\": 127\n    },\n    {\n      \"name\": \"宜昌\",\n      \"value\": 130\n    },\n    {\n      \"name\": \"义乌\",\n      \"value\": 132\n    },\n    {\n      \"name\": \"丽水\",\n      \"value\": 133\n    },\n    {\n      \"name\": \"洛阳\",\n      \"value\": 134\n    },\n    {\n      \"name\": \"秦皇岛\",\n      \"value\": 136\n    },\n    {\n      \"name\": \"株洲\",\n      \"value\": 143\n    },\n    {\n      \"name\": \"石家庄\",\n      \"value\": 147\n    },\n    {\n      \"name\": \"莱芜\",\n      \"value\": 148\n    },\n    {\n      \"name\": \"常德\",\n      \"value\": 152\n    },\n    {\n      \"name\": \"保定\",\n      \"value\": 153\n    },\n    {\n      \"name\": \"湘潭\",\n      \"value\": 154\n    },\n    {\n      \"name\": \"金华\",\n      \"value\": 157\n    },\n    {\n      \"name\": \"岳阳\",\n      \"value\": 169\n    },\n    {\n      \"name\": \"长沙\",\n      \"value\": 175\n    },\n    {\n      \"name\": \"衢州\",\n      \"value\": 177\n    },\n    {\n      \"name\": \"廊坊\",\n      \"value\": 193\n    },\n    {\n      \"name\": \"菏泽\",\n      \"value\": 194\n    },\n    {\n      \"name\": \"合肥\",\n      \"value\": 229\n    },\n    {\n      \"name\": \"武汉\",\n      \"value\": 273\n    },\n    {\n      \"name\": \"大庆\",\n      \"value\": 279\n    }\n  ],\n  \"commonOption\": {\n    \"barSize\": 10,\n    \"barColor\": \"#D6F263\",\n    \"barColor2\": \"#A3DB6B\",\n    \"gradientColor\": false,\n    \"areaColor\": {\n      \"color1\": \"#f7f7f7\",\n      \"color2\": \"#3B3737\"\n    },\n    \"heat\": {\n      \"pointSize\": 15,\n      \"blurSize\": 20,\n      \"maxOpacity\": 1\n    },\n    \"inRange\": {\n      \"color\": [\n        \"#E08D8D\",\n        \"#ff9800\"\n      ]\n    },\n    \"breadcrumb\": {\n      \"drillDown\": false,\n      \"textColor\": \"#ffffff\"\n    }\n  },\n  \"option\": {\n    \"drillDown\": false,\n    \"area\": {\n      \"markerCount\": 5,\n      \"shadowBlur\": 10,\n      \"markerOpacity\": 1,\n      \"markerColor\": \"#df2425\",\n      \"shadowColor\": \"#DDE330\",\n      \"scatterLabelShow\": false,\n      \"markerType\": \"effectScatter\",\n      \"value\": [\n        \"china\"\n      ],\n      \"name\": [\n        \"中国\"\n      ]\n    },\n    \"graphic\": [],\n    \"grid\": {\n      \"show\": false,\n      \"bottom\": 115\n    },\n    \"card\": {\n      \"title\": \"\",\n      \"extra\": \"\",\n      \"rightHref\": \"\",\n      \"size\": \"default\"\n    },\n    \"title\": {\n      \"text\": \"\",\n      \"left\": 10,\r\n			\"textStyle\":{\r\n			  \"fontWeight\":\"normal\"\r\n			},\n      \"show\": true\n    },\n    \"legend\": {\n      \"data\": []\n    },\n    \"visualMap\": {\n      \"show\": true,\n      \"min\": 0,\n      \"type\": \"continuous\",\n      \"max\": 200,\n      \"left\": \"5%\",\n      \"top\": \"bottom\",\n      \"calculable\": true,\n      \"seriesIndex\": [\n        1\n      ]\n    },\n    \"geo\": {\n      \"top\": 30,\n      \"label\": {\n        \"emphasis\": {\n          \"show\": false,\n          \"color\": \"#fff\"\n        }\n      },\n      \"roam\": true,\n      \"zoom\": 1,\n      \"itemStyle\": {\n        \"normal\": {\n          \"borderColor\": \"#a9a9a9\",\n          \"borderWidth\": 1,\n          \"areaColor\": \"\",\n          \"shadowColor\": \"#80d9f8\",\n          \"shadowOffsetX\": 0,\n          \"shadowOffsetY\": 0,\n          \"shadowBlur\": 0\n        },\n        \"emphasis\": {\n          \"areaColor\": \"#fff59c\",\n          \"borderWidth\": 0\n        }\n      }\n    }\n  }\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-08-05 11:17:24' WHERE `id` = '100120106';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '按钮', `comp_type` = 'JRadioButton', `icon` = 'mdi:gesture-tap-button', `order_num` = 15, `type_id` = NULL, `comp_config` = '{\r\n    \"w\": 12,\r\n    \"h\": 12,\r\n    \"dataType\": 1,\r\n    \"url\": \"\",\r\n    \"timeOut\": 0,\r\n    \"linkageConfig\": [],\r\n    \"chartData\": [\r\n        {\r\n            \"title\": \"按钮一\",\r\n            \"value\": 0,\r\n            \"href\": \"\",\r\n            \"data\": {}\r\n        },\r\n        {\r\n            \"title\": \"按钮二\",\r\n            \"value\": 1,\r\n            \"href\": \"\",\r\n            \"data\": {}\r\n        },\r\n        {\r\n            \"title\": \"按钮三\",\r\n            \"value\": 2,\r\n            \"href\": \"\",\r\n            \"data\": {}\r\n        },\r\n        {\r\n            \"title\": \"按钮四\",\r\n            \"value\": 3,\r\n            \"href\": \"\",\r\n            \"data\": {}\r\n        },\r\n        {\r\n            \"title\": \"按钮五\",\r\n            \"value\": 4,\r\n            \"href\": \"\",\r\n            \"data\": {}\r\n        }\r\n    ],\r\n    \"option\": {\r\n        \"title\": \"按钮\",\r\n        \"body\": {\r\n            \"size\": \"small\",\r\n            \"spaceSize\": 20,\r\n            \"shape\": \"circle\"\r\n        },\r\n        \"customColor\": [\r\n            {\r\n                \"color\": \"#1A7DED\"\r\n            },\r\n            {\r\n                \"color\": \"#F8E71C\"\r\n            },\r\n            {\r\n                \"color\": \"#B8E986\"\r\n            },\r\n            {\r\n                \"color\": \"#50E3C2\"\r\n            }\r\n        ]\r\n    }\r\n}', `status` = '1', `create_by` = NULL, `create_time` = NULL, `update_by` = 'admin', `update_time` = '2022-04-29 10:53:49' WHERE `id` = '100111';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '自定义查询' WHERE `id` = '100100';
UPDATE `onl_drag_comp` SET `parent_id` = '100', `comp_name` = '查询条件' WHERE `id` = '100100';


-- 积木报表升级到1.8.1版本--
ALTER TABLE jimu_report 
ADD COLUMN update_count int NULL DEFAULT 0 COMMENT '乐观锁版本' AFTER tenant_id;
update jimu_report set update_count = 0 where update_count is null;

ALTER TABLE jimu_report
ADD COLUMN `submit_form` tinyint(1) NULL COMMENT '是否填报报表 0不是,1是' ;
ALTER TABLE jimu_report
MODIFY COLUMN type varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '类型' AFTER status;
CREATE TABLE `jimu_report_category`  (
  `id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '主键',
  `name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '分类名称',
  `parent_id` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '父级id',
  `iz_leaf` int(1) NULL DEFAULT NULL COMMENT '是否为叶子节点(0 否 1是)',
  `source_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '来源类型( report 积木报表 screen 大屏  drag 仪表盘)',
  `del_flag` int(1) NULL DEFAULT NULL COMMENT '删除标识(0 正常 1 已删除)',
  `create_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '创建人',
  `create_time` timestamp NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(32) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '更新人',
  `update_time` timestamp NULL DEFAULT NULL COMMENT '更新时间',
  `tenant_id` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '租户id',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '分类' ROW_FORMAT = Dynamic;

INSERT INTO jimu_report_category (id, name, parent_id, iz_leaf, source_type, del_flag, create_by, create_time, update_by, update_time, tenant_id) VALUES ('984272091947253760', '数据报表', '0', 1, 'report', 0, 'admin', '2024-08-16 11:52:44', NULL, NULL, '1000');
INSERT INTO jimu_report_category (id, name, parent_id, iz_leaf, source_type, del_flag, create_by, create_time, update_by, update_time, tenant_id) VALUES ('984302961118724096', '图形报表', '0', 1, 'report', 0, 'admin', '2024-08-16 13:55:24', NULL, NULL, '1000');
INSERT INTO jimu_report_category (id, name, parent_id, iz_leaf, source_type, del_flag, create_by, create_time, update_by, update_time, tenant_id) VALUES ('984302991393210368', '打印设计', '0', 1, 'report', 0, 'admin', '2024-08-16 13:55:31', NULL, NULL, '1000');
update jimu_report set type = '984302991393210368' where type = 'printinfo';
update jimu_report set type = '984272091947253760' where type = 'datainfo';
update jimu_report set type = '984302961118724096' where type = 'chartinfo';
INSERT INTO `jimu_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`) VALUES ('986779503584169984', '民族', 'minzu', '', 0, 'admin', '2024-08-23 09:56:17', NULL, NULL, NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986779561591394304', '986779503584169984', '汉族', 'hanzu', NULL, 1, 1, 'admin', '2024-08-23 09:56:31', 'admin', '2024-08-23 09:56:45');
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986779602800431104', '986779503584169984', '回族', 'huizu', NULL, 1, 1, 'admin', '2024-08-23 09:56:41', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986779663689142272', '986779503584169984', '维吾尔族', 'weiwuer', NULL, 1, 1, 'admin', '2024-08-23 09:56:56', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986779697352626176', '986779503584169984', '藏族', 'zangzu', NULL, 1, 1, 'admin', '2024-08-23 09:57:04', NULL, NULL);
INSERT INTO `jimu_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`) VALUES ('986783181955223552', '学历', 'xueli_sf', '', 0, 'admin', '2024-08-23 10:10:54', NULL, NULL, NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783239996002304', '986783181955223552', '文盲', '0', NULL, 1, 1, 'admin', '2024-08-23 10:11:08', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783271616860160', '986783181955223552', '小学', '1', NULL, 1, 1, 'admin', '2024-08-23 10:11:16', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783303967526912', '986783181955223552', '初中', '2', NULL, 1, 1, 'admin', '2024-08-23 10:11:23', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783326532882432', '986783181955223552', '高中', '3', NULL, 1, 1, 'admin', '2024-08-23 10:11:29', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783354462752768', '986783181955223552', '专科', '4', NULL, 1, 1, 'admin', '2024-08-23 10:11:35', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783373504892928', '986783181955223552', '本科', '5', NULL, 1, 1, 'admin', '2024-08-23 10:11:40', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783415494070272', '986783181955223552', '研究生', '6', NULL, 1, 1, 'admin', '2024-08-23 10:11:50', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986783438734708736', '986783181955223552', '博士', '7', NULL, 1, 1, 'admin', '2024-08-23 10:11:56', NULL, NULL);
INSERT INTO `jimu_dict` (`id`, `dict_name`, `dict_code`, `description`, `del_flag`, `create_by`, `create_time`, `update_by`, `update_time`, `type`, `tenant_id`) VALUES ('986784113082322944', '爱好', 'aihao', '', 0, 'admin', '2024-08-23 10:14:36', NULL, NULL, NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986784154379440128', '986784113082322944', '音乐', '0', NULL, 1, 1, 'admin', '2024-08-23 10:14:46', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986784179134222336', '986784113082322944', '运动', '1', NULL, 1, 1, 'admin', '2024-08-23 10:14:52', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986784205717721088', '986784113082322944', '舞蹈', '2', NULL, 1, 1, 'admin', '2024-08-23 10:14:58', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986784229390372864', '986784113082322944', '棋牌', '3', NULL, 1, 1, 'admin', '2024-08-23 10:15:04', NULL, NULL);
INSERT INTO `jimu_dict_item` (`id`, `dict_id`, `item_text`, `item_value`, `description`, `sort_order`, `status`, `create_by`, `create_time`, `update_by`, `update_time`) VALUES ('986784260960899072', '986784113082322944', '电竞', '4', NULL, 1, 1, 'admin', '2024-08-23 10:15:12', NULL, NULL);
INSERT INTO `jimu_report` (`id`, `code`, `name`, `note`, `status`, `type`, `json_str`, `api_url`, `thumb`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `api_method`, `api_code`, `template`, `view_count`, `css_str`, `js_str`, `py_str`, `tenant_id`, `update_count`, `submit_form`) VALUES ('989065112487022592', '20240823093133__1750', '在线填报-员工信息登记', NULL, NULL, 'datainfo', '{\"loopBlockList\":[],\"querySetting\":{\"izOpenQueryBar\":false,\"izDefaultQuery\":true},\"printConfig\":{\"paper\":\"A4\",\"width\":210,\"height\":297,\"definition\":1,\"isBackend\":false,\"marginX\":10,\"marginY\":10,\"layout\":\"portrait\",\"printCallBackUrl\":\"\"},\"hidden\":{\"rows\":[],\"cols\":[]},\"dbexps\":[],\"dicts\":[],\"freeze\":\"A1\",\"dataRectWidth\":698,\"autofilter\":{},\"validations\":[],\"cols\":{\"0\":{\"width\":76},\"1\":{\"width\":114},\"2\":{\"width\":87},\"3\":{\"width\":99},\"4\":{\"width\":65},\"5\":{\"width\":126},\"6\":{\"width\":131},\"len\":100},\"area\":{\"sri\":10,\"sci\":9,\"eri\":10,\"eci\":9,\"width\":100,\"height\":121},\"pyGroupEngine\":false,\"submitHandlers\":[{\"type\":\"api\",\"code\":\"api\",\"name\":\"api\",\"isMain\":true,\"isEdit\":true,\"apiUrl\":\"https://bootapi.jeecg.com/jmreport/test/submit/handle\"}],\"excel_config_id\":\"989065112487022592\",\"hiddenCells\":[],\"zonedEditionList\":[],\"rows\":{\"0\":{\"cells\":{\"0\":{\"merge\":[1,6],\"height\":90,\"text\":\"员工信息登记表\",\"style\":6}},\"height\":45},\"1\":{\"cells\":{},\"height\":45},\"2\":{\"cells\":{\"0\":{\"text\":\"编号\",\"style\":7,\"fillFormLabel\":\"*\"},\"1\":{\"fillForm\":{\"componentFlag\":\"input-text\",\"component\":\"Input\",\"field\":\"no\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"pattern\":\"\",\"patternErrorTip\":\"\"},\"style\":7,\"text\":\" \"},\"2\":{\"text\":\"年龄\",\"style\":7},\"3\":{\"fillForm\":{\"componentFlag\":\"InputNumber\",\"component\":\"InputNumber\",\"field\":\"age\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"label\":\"\",\"labelText\":\"\",\"precision\":0,\"isLimitMinNum\":false,\"minNum\":0,\"isLimitMaxNum\":false,\"maxNum\":100,\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"age\"}]},\"style\":7,\"text\":\" \"},\"4\":{\"text\":\"填写时间\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"DatePicker-time\",\"component\":\"DatePicker\",\"field\":\"create_time\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"label\":\"\",\"labelText\":\"\",\"dateFormat\":\"yyyy-MM-dd HH:mm:ss\",\"defaultValue\":\"\"},\"style\":7,\"text\":\" \"},\"6\":{\"merge\":[3,0],\"height\":180,\"fillForm\":{\"componentFlag\":\"JUploadImage\",\"component\":\"JUploadImage\",\"field\":\"photo\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"multiple\":false,\"maxUploadNum\":1,\"h_align\":\"center\"},\"style\":7,\"text\":\" \"}},\"height\":45},\"3\":{\"cells\":{\"0\":{\"text\":\"姓名\",\"style\":7,\"fillFormLabel\":\"*\"},\"1\":{\"text\":\" \",\"fillForm\":{\"componentFlag\":\"input-text\",\"component\":\"Input\",\"field\":\"name\",\"placeholder\":\"\",\"required\":true,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"name\"},{\"dbTable\":\"test_form_submit1\",\"dbField\":\"name\"}],\"label\":\"A5\",\"labelText\":\"姓名\",\"pattern\":\"\",\"patternErrorTip\":\"\"}},\"2\":{\"text\":\"性别\",\"style\":7},\"3\":{\"fillForm\":{\"componentFlag\":\"JRadio\",\"component\":\"JRadio\",\"field\":\"sex\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dataSource\":\"dict\",\"options\":[{\"label\":\"男\",\"value\":\"1\"},{\"label\":\"女\",\"value\":\"2\"}],\"apiUrl\":\"\",\"dictCode\":\"sex1\",\"dictName\":\"性别\"},\"style\":8,\"text\":\" \"},\"4\":{\"text\":\"出生日期\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"DatePicker-date\",\"component\":\"DatePicker\",\"field\":\"brithday\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dateFormat\":\"yyyy-MM-dd\",\"dateShowType\":\"date\"},\"style\":7,\"text\":\" \"},\"8\":{}},\"height\":45},\"4\":{\"cells\":{\"0\":{\"text\":\"民族\",\"style\":7,\"fillFormLabel\":\"*\"},\"1\":{\"fillForm\":{\"componentFlag\":\"JSelect\",\"component\":\"JSelect\",\"field\":\"nation\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dataSource\":\"dict\",\"options\":[{\"label\":\"选项1\",\"value\":\"1\"},{\"label\":\"选项2\",\"value\":\"2\"},{\"label\":\"选项3\",\"value\":\"3\"}],\"apiUrl\":\"\",\"dictCode\":\"minzu\",\"dictName\":\"民族\",\"multiple\":\"\"},\"style\":7,\"text\":\" \"},\"2\":{\"text\":\"政治面貌\",\"style\":7},\"3\":{\"fillForm\":{\"componentFlag\":\"JSelect\",\"component\":\"JSelect\",\"field\":\"politics\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"politics\"}],\"dataSource\":\"api\",\"options\":[{\"label\":\"选项1\",\"value\":\"1\"},{\"label\":\"选项2\",\"value\":\"2\"},{\"label\":\"选项3\",\"value\":\"3\"}],\"apiUrl\":\"https://bootapi.jeecg.com/jmreport/test/submit/dict/political\",\"dictCode\":\"\",\"dictName\":\"\",\"multiple\":\"\"},\"style\":7,\"text\":\" \"},\"4\":{\"text\":\"籍贯\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"JAreaLinkage\",\"component\":\"JAreaLinkage\",\"field\":\"native_place\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"native_place\"}],\"areaType\":\"region\"},\"style\":7,\"text\":\" \"}},\"height\":45},\"5\":{\"cells\":{\"0\":{\"text\":\"身高（cm)\",\"style\":7},\"1\":{\"fillForm\":{\"componentFlag\":\"InputNumber\",\"component\":\"InputNumber\",\"field\":\"height\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"height\"}],\"precision\":2,\"isLimitMinNum\":false,\"minNum\":50,\"isLimitMaxNum\":false,\"maxNum\":200},\"style\":7,\"text\":\" \"},\"2\":{\"text\":\"体重\",\"style\":7},\"3\":{\"fillForm\":{\"componentFlag\":\"InputNumber\",\"component\":\"InputNumber\",\"field\":\"weight\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"weight\"}],\"precision\":2,\"isLimitMinNum\":false,\"minNum\":30,\"isLimitMaxNum\":false,\"maxNum\":300},\"style\":7,\"text\":\" \"},\"4\":{\"text\":\"健康状况\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"JSelect\",\"component\":\"JSelect\",\"field\":\"health\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dataSource\":\"static\",\"options\":[{\"label\":\"健康\",\"value\":\"1\"},{\"label\":\"不健康\",\"value\":\"2\"}],\"apiUrl\":\"\",\"dictCode\":\"\",\"dictName\":\"\",\"multiple\":\"\"},\"style\":7,\"text\":\" \"}},\"height\":45},\"6\":{\"cells\":{\"0\":{\"text\":\"身份证号\",\"style\":7},\"1\":{\"merge\":[0,2],\"height\":45,\"fillForm\":{\"componentFlag\":\"input-text\",\"component\":\"Input\",\"field\":\"idcard\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"pattern\":\"^\\\\d{17}[\\\\dX]$\",\"patternErrorTip\":\"请输入身份证号\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"id_card\"}]},\"style\":7,\"text\":\" \"},\"4\":{\"text\":\"学历\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"JSelect\",\"component\":\"JSelect\",\"field\":\"people\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dataSource\":\"dict\",\"options\":[{\"label\":\"选项1\",\"value\":\"1\"},{\"label\":\"选项2\",\"value\":\"2\"},{\"label\":\"选项3\",\"value\":\"3\"}],\"apiUrl\":\"\",\"dictCode\":\"xueli_sf\",\"dictName\":\"学历\",\"multiple\":true},\"style\":7,\"text\":\" \",\"merge\":[0,1],\"height\":45}},\"height\":45},\"7\":{\"cells\":{\"0\":{\"text\":\"联系地址\",\"style\":7},\"1\":{\"fillForm\":{\"componentFlag\":\"input-text\",\"component\":\"Input\",\"field\":\"addr\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"address\"}],\"pattern\":\"\",\"patternErrorTip\":\"\"},\"merge\":[0,2],\"height\":45,\"style\":7,\"text\":\" \"},\"4\":{\"text\":\"手机号\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"input-text\",\"component\":\"Input\",\"field\":\"phone\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"phone\"}],\"pattern\":\"\",\"patternErrorTip\":\"\"},\"style\":7,\"text\":\" \",\"merge\":[0,1],\"height\":45}},\"height\":45},\"8\":{\"cells\":{\"0\":{\"text\":\"毕业证书\",\"style\":7},\"1\":{\"merge\":[0,2],\"height\":45,\"fillForm\":{\"componentFlag\":\"JUploadFile\",\"component\":\"JUploadFile\",\"field\":\"ca\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"multiple\":false,\"maxUploadNum\":1,\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"ca\"}]},\"style\":9,\"text\":\" \"},\"4\":{\"text\":\"幸运色\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"ColorPicker\",\"component\":\"ColorPicker\",\"field\":\"lucky_color\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"alpha\":false},\"style\":7,\"text\":\" \",\"merge\":[0,1],\"height\":45}},\"height\":45},\"9\":{\"cells\":{\"0\":{\"text\":\"教育经历\",\"merge\":[0,6],\"height\":45,\"style\":8}},\"height\":45},\"10\":{\"cells\":{\"0\":{\"merge\":[0,6],\"height\":121,\"fillForm\":{\"componentFlag\":\"input-textarea\",\"component\":\"Input\",\"field\":\"education\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"education\"}],\"pattern\":\"\",\"patternErrorTip\":\"\"},\"style\":7,\"text\":\" \"}},\"height\":121},\"11\":{\"cells\":{\"0\":{\"text\":\"工作经历\",\"merge\":[0,6],\"height\":45,\"style\":8}},\"height\":45},\"12\":{\"cells\":{\"0\":{\"merge\":[0,6],\"height\":150,\"fillForm\":{\"componentFlag\":\"input-textarea\",\"component\":\"Input\",\"field\":\"work_exp\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"work_exp\"}],\"pattern\":\"\",\"patternErrorTip\":\"\"},\"style\":7,\"text\":\" \"}},\"height\":150},\"13\":{\"cells\":{\"0\":{\"text\":\"爱好\",\"style\":7},\"1\":{\"merge\":[0,5],\"height\":45,\"fillForm\":{\"componentFlag\":\"JCheckbox\",\"component\":\"JCheckbox\",\"field\":\"fruity\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"fruity\"}],\"dataSource\":\"dict\",\"options\":[{\"label\":\"选项1\",\"value\":\"1\"},{\"label\":\"选项2\",\"value\":\"2\"},{\"label\":\"选项3\",\"value\":\"3\"}],\"apiUrl\":\"\",\"dictCode\":\"aihao\",\"dictName\":\"爱好\"},\"style\":7,\"text\":\" \"}},\"height\":45},\"14\":{\"cells\":{\"0\":{\"text\":\"所属部门\",\"style\":7},\"1\":{\"fillForm\":{\"componentFlag\":\"JDepartment\",\"component\":\"JDepartment\",\"field\":\"dept\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"dept\"}],\"multiple\":true,\"apiUrl\":\"http://************:8086/jmreport/test/getDepartmentList\"},\"merge\":[0,2],\"height\":45,\"style\":7,\"text\":\" \"},\"4\":{\"text\":\"薪资\",\"style\":7},\"5\":{\"fillForm\":{\"componentFlag\":\"JMoney\",\"component\":\"JMoney\",\"field\":\"pay\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"label\":\"\",\"labelText\":\"\",\"precision\":0,\"addon\":\"prepend\",\"moenyUnit\":\"￥\"},\"style\":7,\"text\":\" \",\"merge\":[0,1],\"height\":45}},\"height\":45},\"15\":{\"cells\":{\"0\":{\"text\":\"角色\",\"style\":7},\"1\":{\"merge\":[0,2],\"height\":45,\"fillForm\":{\"componentFlag\":\"JRole\",\"component\":\"JRole\",\"field\":\"role\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"multiple\":false,\"apiUrl\":\"https://bootapi.jeecg.com/jmreport/test/getRoleList\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"role\"}]},\"style\":7,\"text\":\" \"},\"4\":{\"style\":7,\"text\":\"工位\"},\"5\":{\"style\":7,\"text\":\" \",\"merge\":[0,1],\"height\":45,\"fillForm\":{\"componentFlag\":\"JSelect\",\"component\":\"JSelect\",\"field\":\"station\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"label\":\"\",\"labelText\":\"\",\"dataSource\":\"static\",\"options\":[{\"label\":\"101\",\"value\":\"1\"},{\"label\":\"102\",\"value\":\"2\"},{\"label\":\"103\",\"value\":\"3\"},{\"label\":\"104\",\"value\":\"4\"}],\"apiUrl\":\"\",\"dictCode\":\"\",\"dictName\":\"\",\"multiple\":true}}},\"height\":45},\"16\":{\"cells\":{\"0\":{\"text\":\"直属领导\",\"style\":7},\"1\":{\"fillForm\":{\"componentFlag\":\"JUser\",\"component\":\"JUser\",\"field\":\"leader\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"multiple\":false,\"apiUrl\":\"https://bootapi.jeecg.com/jmreport/test/getUserList\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"leader\"}]},\"merge\":[0,2],\"height\":45,\"style\":7,\"text\":\" \"},\"4\":{\"style\":7,\"text\":\"是否启用\"},\"5\":{\"style\":10,\"text\":\" \",\"merge\":[0,1],\"height\":45,\"fillForm\":{\"componentFlag\":\"JSwitch\",\"component\":\"JSwitch\",\"field\":\"status\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"dbFieldBind\":[{\"dbTable\":\"test_form_submit\",\"dbField\":\"status\"}],\"label\":\"\",\"labelText\":\"\",\"switchOpen\":\"Y\",\"switchClose\":\"N\",\"h_align\":\"center\"}},\"6\":{}},\"height\":45},\"17\":{\"cells\":{\"0\":{\"style\":7,\"text\":\"负责部门\"},\"1\":{\"fillForm\":{\"componentFlag\":\"JSelectTree\",\"component\":\"JSelectTree\",\"field\":\"responsible\",\"value\":\"\",\"defaultValue\":\"\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"multiple\":true,\"apiUrl\":\"https://bootapi.jeecg.com/jmreport/test/getDepartmentList\"},\"style\":7,\"text\":\" \",\"merge\":[0,2],\"height\":45},\"4\":{\"style\":7,\"text\":\"上班时间\"},\"5\":{\"style\":7,\"text\":\" \",\"merge\":[0,1],\"height\":45,\"fillForm\":{\"componentFlag\":\"TimePicker\",\"component\":\"TimePicker\",\"field\":\"key_1724408224853_326455\",\"placeholder\":\"\",\"required\":false,\"requiredTip\":\"不能为空~\",\"label\":\"\",\"labelText\":\"\",\"isRangTime\":false,\"timeType\":\"time\"}}},\"height\":45},\"20\":{\"cells\":{\"6\":{}}},\"len\":201},\"rpbar\":{\"show\":true,\"pageSize\":\"\",\"btnList\":[]},\"fixedPrintHeadRows\":[],\"fixedPrintTailRows\":[],\"displayConfig\":{},\"background\":false,\"name\":\"sheet1\",\"styles\":[{\"align\":\"center\"},{\"align\":\"center\",\"valign\":\"middle\"},{\"align\":\"center\",\"valign\":\"middle\",\"font\":{\"size\":16}},{\"font\":{\"size\":16}},{\"align\":\"center\",\"valign\":\"middle\",\"font\":{\"size\":16,\"bold\":true}},{\"font\":{\"size\":16,\"bold\":true}},{\"align\":\"center\",\"valign\":\"middle\",\"font\":{\"size\":16,\"bold\":true},\"border\":{\"bottom\":[\"thin\",\"#000\"],\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]}},{\"border\":{\"bottom\":[\"thin\",\"#000\"],\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]}},{\"align\":\"center\",\"border\":{\"bottom\":[\"thin\",\"#000\"],\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]}},{\"align\":\"center\",\"border\":{\"bottom\":[\"thin\",\"#000\"],\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]},\"valign\":\"middle\"},{\"align\":\"right\",\"border\":{\"bottom\":[\"thin\",\"#000\"],\"top\":[\"thin\",\"#000\"],\"left\":[\"thin\",\"#000\"],\"right\":[\"thin\",\"#000\"]}}],\"freezeLineColor\":\"rgb(185, 185, 185)\",\"merges\":[\"A1:G2\",\"G3:G6\",\"B7:D7\",\"F7:G7\",\"B8:D8\",\"F8:G8\",\"B9:D9\",\"F9:G9\",\"A10:G10\",\"A11:G11\",\"A12:G12\",\"A13:G13\",\"B14:G14\",\"B15:D15\",\"F15:G15\",\"B16:D16\",\"F16:G16\",\"B17:D17\",\"F17:G17\",\"B18:D18\",\"F18:G18\"]}', NULL, NULL, 'admin', '2024-08-29 17:18:29', 'admin', '2024-08-30 10:24:03', 0, NULL, NULL, 1, 2, NULL, NULL, NULL, '1', 9, 1);
INSERT INTO `jimu_report_share` (`id`, `report_id`, `preview_url`, `preview_lock`, `last_update_time`, `term_of_validity`, `status`, `preview_lock_status`, `SHARE_TOKEN`) VALUES ('989322818603012096', '989065112487022592', '/jmreport/shareView/989065112487022592', '', '2024-08-30 10:22:31', '1', '0', '0', '71e3778ba7ebeae4652d2e53c46a7b1b');

-- 积木报表表小改动
UPDATE jimu_report_db_field SET search_mode = 3 WHERE id = '8fb53733d1fb32d21d2bc2b3c0178c73';
INSERT INTO `jimu_report` (`id`, `code`, `name`, `note`, `status`, `type`, `json_str`, `api_url`, `thumb`, `create_by`, `create_time`, `update_by`, `update_time`, `del_flag`, `api_method`, `api_code`, `template`, `view_count`, `css_str`, `js_str`, `py_str`, `tenant_id`) VALUES ('975549294469992448', '20240723101422', '简单表达式混合运算', NULL, NULL, 'datainfo', '{\"loopBlockList\":[],\"printConfig\":{\"paper\":\"A4\",\"width\":210,\"height\":297,\"definition\":1,\"isBackend\":false,\"marginX\":10,\"marginY\":10,\"layout\":\"portrait\",\"printCallBackUrl\":\"\"},\"hidden\":{\"rows\":[],\"cols\":[]},\"dbexps\":[],\"dicts\":[],\"freeze\":\"A1\",\"dataRectWidth\":1097,\"autofilter\":{},\"validations\":[],\"cols\":{\"0\":{\"width\":81},\"1\":{\"width\":55},\"2\":{\"width\":144},\"3\":{\"width\":143},\"4\":{\"width\":112},\"5\":{\"width\":129},\"7\":{\"width\":333},\"len\":100},\"area\":false,\"pyGroupEngine\":false,\"excel_config_id\":\"975549294469992448\",\"hiddenCells\":[],\"zonedEditionList\":[],\"rows\":{\"0\":{\"cells\":{\"0\":{\"text\":\"1\"},\"1\":{},\"2\":{\"text\":\"SUM(A1,A2)\"},\"3\":{\"text\":\"=SUM(A1,A2)\"},\"4\":{\"text\":\"SUM(A1:A10)\"},\"5\":{\"text\":\"=SUM(A1:A10)\"}}},\"1\":{\"cells\":{\"0\":{\"text\":\"2\"},\"1\":{},\"2\":{\"text\":\"SUM(A1,A2)*2\"},\"3\":{\"text\":\"=SUM(A1,A2)*2\"},\"4\":{\"text\":\"SUM(A1:A10)*2\"},\"5\":{\"text\":\"=SUM(A1:A10)*2\"},\"6\":{},\"7\":{\"text\":\"=2+SUM(A1,A2)*2 \"}}},\"2\":{\"cells\":{\"0\":{\"text\":\"3\"},\"2\":{\"text\":\"SUM(A1,A2)/2\"},\"3\":{\"text\":\"=SUM(A1,A2)/2\"},\"4\":{\"text\":\"SUM(A1:A10)/2\"},\"5\":{\"text\":\"=SUM(A1:A10)/2\"},\"7\":{\"text\":\"=A1*A2\"}}},\"3\":{\"cells\":{\"0\":{\"text\":\"4\"},\"2\":{\"text\":\"SUM(A1,A2)+2\"},\"3\":{\"text\":\"=SUM(A1,A2)+2\"},\"4\":{\"text\":\"SUM(A1:A10)+2\"},\"5\":{\"text\":\"=SUM(A1:A10)+2\"}}},\"4\":{\"cells\":{\"0\":{\"text\":\"5\"},\"2\":{\"text\":\"SUM(A1,A2)-2\"},\"3\":{\"text\":\"=SUM(A1,A2)-2\"},\"4\":{\"text\":\"SUM(A1:A10)-2\"},\"5\":{\"text\":\"=SUM(A1:A10)-2\"},\"7\":{}}},\"5\":{\"cells\":{\"0\":{\"text\":\"6\"}}},\"6\":{\"cells\":{\"0\":{\"text\":\"7\"},\"2\":{\"text\":\"MAX(A1,A2)\"},\"3\":{\"text\":\"=MAX(A1,A2)\"},\"4\":{\"text\":\"MAX(A1:A10)\"},\"5\":{\"text\":\"=MAX(A1:A10)\"}}},\"7\":{\"cells\":{\"0\":{\"text\":\"8\"},\"2\":{\"text\":\"MAX(A1,A2)*2\"},\"3\":{\"text\":\"=MAX(A1,A2)*2\"},\"4\":{\"text\":\"MAX(A1:A10)*2\"},\"5\":{\"text\":\"=MAX(A1:A10)*2\"}}},\"8\":{\"cells\":{\"0\":{\"text\":\"9\"},\"2\":{\"text\":\"MAX(A1,A2)/2\"},\"3\":{\"text\":\"=MAX(A1,A2)/2\"},\"4\":{\"text\":\"MAX(A1:A10)/2\"},\"5\":{\"text\":\"=MAX(A1:A10)/2\"}}},\"9\":{\"cells\":{\"0\":{\"text\":\"10\"},\"2\":{\"text\":\"MAX(A1,A2)+2\"},\"3\":{\"text\":\"=MAX(A1,A2)+2\"},\"4\":{\"text\":\"MAX(A1:A10)+2\"},\"5\":{\"text\":\"=MAX(A1:A10)+2\"}}},\"10\":{\"cells\":{\"0\":{},\"2\":{\"text\":\"MAX(A1,A2)-2\"},\"3\":{\"text\":\"=MAX(A1,A2)-2\"},\"4\":{\"text\":\"MAX(A1:A10)-2\"},\"5\":{\"text\":\"=MAX(A1:A10)-2\"}}},\"11\":{\"cells\":{\"0\":{}}},\"12\":{\"cells\":{\"2\":{\"text\":\"MIN(A1,A2)\"},\"3\":{\"text\":\"=MIN(A1,A2)\"},\"4\":{\"text\":\"MIN(A1:A10)\"},\"5\":{\"text\":\"=MIN(A1:A10)\"}}},\"13\":{\"cells\":{\"2\":{\"text\":\"MIN(A1,A2)*2\"},\"3\":{\"text\":\"=MIN(A1,A2)*2\"},\"4\":{\"text\":\"MIN(A1:A10)*2\"},\"5\":{\"text\":\"=MIN(A1:A10)*2\"}}},\"14\":{\"cells\":{\"2\":{\"text\":\"MIN(A1,A2)/2\"},\"3\":{\"text\":\"=MIN(A1,A2)/2\"},\"4\":{\"text\":\"MIN(A1:A10)/2\"},\"5\":{\"text\":\"=MIN(A1:A10)/2\"}}},\"15\":{\"cells\":{\"2\":{\"text\":\"MIN(A1,A2)+2\"},\"3\":{\"text\":\"=MIN(A1,A2)+2\"},\"4\":{\"text\":\"MIN(A1:A10)+2\"},\"5\":{\"text\":\"=MIN(A1:A10)+2\"}}},\"16\":{\"cells\":{\"2\":{\"text\":\"MIN(A1,A2)-2\"},\"3\":{\"text\":\"=MIN(A1,A2)-2\"},\"4\":{\"text\":\"MIN(A1:A10)-2\"},\"5\":{\"text\":\"=MIN(A1:A10)-2\"}}},\"18\":{\"cells\":{\"2\":{\"text\":\"AVERAGE(A1,A2)\"},\"3\":{\"text\":\"=AVERAGE(A1,A2)\"},\"4\":{\"text\":\"AVERAGE(A1:A10)\"},\"5\":{\"text\":\"=AVERAGE(A1:A10)\"}}},\"19\":{\"cells\":{\"2\":{\"text\":\"AVERAGE(A1,A2)*2\"},\"3\":{\"text\":\"=AVERAGE(A1,A2)*2\"},\"4\":{\"text\":\"AVERAGE(A1:A10)*2\"},\"5\":{\"text\":\"=AVERAGE(A1:A10)*2\"}}},\"20\":{\"cells\":{\"2\":{\"text\":\"AVERAGE(A1,A2)/2\"},\"3\":{\"text\":\"=AVERAGE(A1,A2)/2\"},\"4\":{\"text\":\"AVERAGE(A1:A10)/2\"},\"5\":{\"text\":\"=AVERAGE(A1:A10)/2\"}}},\"21\":{\"cells\":{\"2\":{\"text\":\"AVERAGE(A1,A2)+2\"},\"3\":{\"text\":\"=AVERAGE(A1,A2)+2\"},\"4\":{\"text\":\"AVERAGE(A1:A10)+2\"},\"5\":{\"text\":\"=AVERAGE(A1:A10)+2\"}}},\"22\":{\"cells\":{\"2\":{\"text\":\"AVERAGE(A1,A2)-2\"},\"3\":{\"text\":\"=AVERAGE(A1,A2)-2\"},\"4\":{\"text\":\"AVERAGE(A1:A10)-2\"},\"5\":{\"text\":\"=AVERAGE(A1:A10)-2\"}}},\"24\":{\"cells\":{\"2\":{\"text\":\"COUNTNZ(A1,A2)\"},\"3\":{\"text\":\"=COUNTNZ(A1,A2)\"},\"4\":{\"text\":\"COUNTNZ(A1:A10)\"},\"5\":{\"text\":\"=COUNTNZ(A1:A10)\"}}},\"25\":{\"cells\":{\"2\":{\"text\":\"COUNTNZ(A1,A2)*2\"},\"3\":{\"text\":\"=COUNTNZ(A1,A2)*2\"},\"4\":{\"text\":\"COUNTNZ(A1:A10)*2\"},\"5\":{\"text\":\"=COUNTNZ(A1:A10)*2\"}}},\"26\":{\"cells\":{\"2\":{\"text\":\"COUNTNZ(A1,A2)/2\"},\"3\":{\"text\":\"=COUNTNZ(A1,A2)/2\"},\"4\":{\"text\":\"COUNTNZ(A1:A10)/2\"},\"5\":{\"text\":\"=COUNTNZ(A1:A10)/2\"}}},\"27\":{\"cells\":{\"2\":{\"text\":\"COUNTNZ(A1,A2)+2\"},\"3\":{\"text\":\"=COUNTNZ(A1,A2)+2\"},\"4\":{\"text\":\"COUNTNZ(A1:A10)+2\"},\"5\":{\"text\":\"=COUNTNZ(A1:A10)+2\"}}},\"28\":{\"cells\":{\"2\":{\"text\":\"COUNTNZ(A1,A2)-2\"},\"3\":{\"text\":\"=COUNTNZ(A1,A2)-2\"},\"4\":{\"text\":\"COUNTNZ(A1:A10)-2\"},\"5\":{\"text\":\"=COUNTNZ(A1:A10)-2\"}}},\"len\":200},\"rpbar\":{\"show\":true,\"pageSize\":\"\",\"btnList\":[]},\"fixedPrintHeadRows\":[],\"fixedPrintTailRows\":[],\"displayConfig\":{},\"background\":false,\"name\":\"sheet1\",\"styles\":[],\"freezeLineColor\":\"rgb(185, 185, 185)\",\"merges\":[]}', NULL, NULL, 'admin', '2024-07-23 10:14:22', 'admin', '2024-07-24 19:17:39', 0, NULL, NULL, 0, 71, NULL, NULL, NULL, NULL);
ALTER TABLE `jimu_report_data_source` MODIFY COLUMN `connect_times` int(11) NULL DEFAULT 0 COMMENT '连接失败次数' AFTER `update_time`;
UPDATE sys_quartz_job SET create_time = '2021-06-30 16:03:09' WHERE id = 'df26ecacf0f75d219d746750fe84bbee';

-- 补充权限注解--
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) VALUES ('1810652607946940417', '1438782641187074050', '批量彻底删除', NULL, NULL, 0, NULL, NULL, 2, 'system:dict:deleteRecycleBin', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-07-09 20:29:56.679000', '15931993294', '2024-07-09 20:30:39.092000', 0, NULL, '1', 0);
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) VALUES ('1808098125316870145', '3f915b2769fc80648e92d04e84ca059d', 'app端编辑用户', NULL, NULL, 0, NULL, NULL, 2, 'system:user:app:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, 'admin', '2024-07-02 19:19:20.566000', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO `sys_permission` VALUES ('1800372628805861377', '1701575168519839746', '列表权限', NULL, NULL, 0, NULL, NULL, 2, 'system:tableWhite:list', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-06-11 11:40:59', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO `sys_permission` VALUES ('1800372727493640194', '1701575168519839746', '添加权限', NULL, NULL, 0, NULL, NULL, 2, 'system:tableWhite:add', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-06-11 11:41:22', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO `sys_permission` VALUES ('1800372811518132225', '1701575168519839746', '修改权限', NULL, NULL, 0, NULL, NULL, 2, 'system:tableWhite:edit', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-06-11 11:41:42', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO `sys_permission` VALUES ('1800372906330374146', '1701575168519839746', '删除权限', NULL, NULL, 0, NULL, NULL, 2, 'system:tableWhite:delete', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-06-11 11:42:05', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO `sys_permission` VALUES ('1800373633509441537', '1701575168519839746', '批量删除', NULL, NULL, 0, NULL, NULL, 2, 'system:tableWhite:deleteBatch', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-06-11 11:44:58', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO `sys_permission` VALUES ('1800373733220630530', '1701575168519839746', '通过id查询', NULL, NULL, 0, NULL, NULL, 2, 'system:tableWhite:queryById', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-06-11 11:45:22', NULL, NULL, 0, NULL, '1', 0);
update sys_permission set is_leaf = 0 where id = '1701575168519839746';

-- 网关路由支持逻辑删除---
ALTER TABLE `sys_gateway_route`
ADD COLUMN `del_flag` int(11) NULL DEFAULT NULL COMMENT '删除状态' AFTER `sys_org_code`;
ALTER TABLE `sys_gateway_route`
MODIFY COLUMN `name` varchar(64) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '服务名' AFTER `router_id`;
UPDATE sys_gateway_route SET del_flag = 0 WHERE del_flag is null;
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) VALUES ('1810923799513612290', '1439399179791409153', '彻底删除', NULL, NULL, 0, NULL, NULL, 2, 'system:gateway:deleteRecycleBin', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-07-10 14:27:33.792000', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) VALUES ('1811685464467230721', '1439399179791409153', '还原逻辑删除', NULL, NULL, 0, NULL, NULL, 2, 'system:gateway:putRecycleBin', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-07-12 16:54:08.873000', NULL, NULL, 0, NULL, '1', 0);
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external) VALUES ('1811685368354754561', '1439399179791409153', '复制路由', NULL, NULL, 0, NULL, NULL, 2, 'system:gateway:copyRoute', '1', NULL, 0, NULL, 1, 0, 0, 0, NULL, '15931993294', '2024-07-12 16:53:45.957000', NULL, NULL, 0, NULL, '1', 0);

